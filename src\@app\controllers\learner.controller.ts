import { PERMISSIONS } from '@app/constants';
import { Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { LearnerService } from '@app/services';
import { PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Get, HttpCode, HttpStatus, Post, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard, PermissionsGuard)
@Controller('learners')
export class LearnerController {
    constructor(private readonly learnerService: LearnerService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.LEARNER_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.learnerService.getLearnerOptions(request, body);
    }

    @Permissions(PERMISSIONS.LEARNER_VIEW.code)
    @Get('autocomplete')
    async searchAutocomplete(@Query('text') text: string) {
        return await this.learnerService.searchText(text);
    }

    // @Permissions(PERMISSIONS.LEARNER_EDIT.code)
    // @Post()
    // async createLearner(@Body(new CustomValidationPipe()) dto: CreateLearnerDTO, @Logged() logged: JwtPayloadDTO) {
    //     return await this.learnerService.createLearner(dto, logged);
    // }

    // @Permissions(PERMISSIONS.LEARNER_EDIT.code)
    // @Put()
    // async updateLearner(@Body(new CustomValidationPipe()) dto: UpdateLearnerDTO, @Logged() logged: JwtPayloadDTO) {
    //     return await this.learnerService.updateLearner(dto, logged);
    // }

    // @Permissions(PERMISSIONS.LEARNER_EDIT.code)
    // @Delete(':id')
    // async dateleCourse(@Param('id') id: string, @Logged() logged: JwtPayloadDTO) {
    //     return await this.learnerService.deleteLearner(id, logged);
    // }
}
