import { SelectConfig } from './generic-select.component';
import { PROBATION_STATUS_OPTIONS } from '@shared/enums/probation-status.enum';

/**
 * Generic Select Configurations with Built-in Templates
 *
 * Each config can specify a templateName to use built-in templates:
 * - 'employee': Shows code chip + profile.fullname
 * - 'position': Shows code chip + name
 * - 'department': Shows code chip + name
 * - 'role': Shows code chip + name
 *
 * Usage:
 * <generic-p-select [config]="employeeSelectConfig"></generic-p-select>
 *
 * The template will be automatically applied based on config.templateName.
 * You can still override with custom ng-template if needed.
 */

// Employee Select Configuration - All employees
export const EMPLOYEE_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getEmployeeOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (employee: any) => `${employee.code} - ${employee.fullname || 'N/A'} (${employee.nickname})`,
    valueField: 'id',
    placeholder: 'Chọn nhân viên',
    sortField: 'code,asc',
    templateName: 'employee',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};
        const trimmedSearchTerm = searchTerm.trim()

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && trimmedSearchTerm) {
            // When searching by text (user typing in filter)
            body.orQ = {
                code: trimmedSearchTerm,
                fullname: trimmedSearchTerm,
                nickname: trimmedSearchTerm,
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    }
};

// Employee Select Configuration - Filtered by department
export const EMPLOYEE_BY_DEPARTMENT_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getEmployeeOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (employee: any) => `${employee.code} - ${employee.fullname || 'N/A'}`,
    valueField: 'id',
    placeholder: 'Chọn nhân viên trong phòng ban',
    sortField: 'code,asc',
    templateName: 'employee',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && searchTerm.trim()) {
            // When searching by text (user typing in filter)
            body.orQ = {
                code: searchTerm.trim(),
                fullname: searchTerm.trim()
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    }
};

// Employee Select Configuration - Active employees only
export const ACTIVE_EMPLOYEE_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getEmployeeOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (employee: any) => `${employee.code} - ${employee.fullname || 'N/A'}`,
    valueField: 'id',
    placeholder: 'Chọn nhân viên đang làm việc',
    sortField: 'code,asc',
    templateName: 'employee',
    baseQuery: { status: 'active' },
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && searchTerm.trim()) {
            // When searching by text (user typing in filter)
            body.orQ = {
                code: searchTerm.trim(),
                fullname: searchTerm.trim()
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    }
};

// Department Select Configuration - Root departments only
export const ROOT_DEPARTMENT_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getRoots',
    idField: 'id', // Field name to query by ID
    labelFormatter: (dept: any) => `${dept.code} - ${dept.name}`,
    valueField: 'id',
    placeholder: 'Chọn phòng ban gốc',
    sortField: 'code,asc',
    templateName: 'department',
};

// Department Select Configuration - Child departments by parent
export const DEPARTMENT_BY_PARENT_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getChildren',
    idField: 'id', // Field name to query by ID
    labelFormatter: (dept: any) => `${dept.code} - ${dept.name}`,
    valueField: 'id',
    placeholder: 'Chọn team/subdept',
    sortField: 'code,asc',
    templateName: 'department',
    onDependencyChange: (parentId: any) => {
        return parentId ? { parentId: parentId } : {};
    }
};

// Course Select Configuration
export const COURSE_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getCourseOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (course: any) => `${course.name} - ${course.level} (${course.type})`,
    valueField: 'code',
    placeholder: 'Chọn khóa học',
    sortField: 'name,asc',
    templateName: 'course',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};
        const trimmedSearchTerm = searchTerm.trim();

        if (idQuery) {
            body.andQ = idQuery;
        } else if (trimmedSearchTerm) {
            body.orQ = { name: trimmedSearchTerm };
        }

        if (baseQuery) {
            if (body.andQ) {
                body.andQ = { ...body.andQ, ...baseQuery };
            } else {
                body.andQ = baseQuery;
            }
        }

        return body;
    }
};

// Position Select Configuration
export const POSITION_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getPositionOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (position: any) => `${position.code} - ${position.name}`,
    valueField: 'id',
    placeholder: 'Chọn vị trí',
    sortField: 'code,asc',
    templateName: 'position',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};
        const trimmedSearchTerm = searchTerm.trim();

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && trimmedSearchTerm) {
            // When searching by text (user typing in filter)
            body.orQ = {
                code: trimmedSearchTerm,
                name: trimmedSearchTerm
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    }
};

// Position Select Configuration - Filtered by department
export const POSITION_BY_DEPARTMENT_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getPositionOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (position: any) => `${position.code} - ${position.name}`,
    valueField: 'id',
    placeholder: 'Chọn vị trí trong phòng ban',
    sortField: 'code,asc',
    templateName: 'position',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};
        const trimmedSearchTerm = searchTerm.trim();

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && trimmedSearchTerm) {
            // When searching by text (user typing in filter)
            body.orQ = {
                code: trimmedSearchTerm,
                name: trimmedSearchTerm
            };
        }

        // Add any base query filters (always apply these)
        // For department filtering, use rootDepartmentId as expected by backend
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            // Map departmentId to rootDepartmentId for backend compatibility
            const mappedBaseQuery = { ...baseQuery };
            if (mappedBaseQuery.departmentId) {
                mappedBaseQuery.rootDepartmentId = mappedBaseQuery.departmentId;
                delete mappedBaseQuery.departmentId;
            }
            body.andQ = { ...body.andQ, ...mappedBaseQuery };
        }

        return body;
    }
};

// Role Select Configuration
export const ROLE_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getRoles',
    idField: 'id', // Field name to query by ID
    labelFormatter: (role: any) => role.name,
    valueField: 'id',
    placeholder: 'Chọn vai trò',
    sortField: 'name,1',
    templateName: 'role'
};

// Employee Position Select Configuration
export const EMPLOYEE_POSITION_SELECT_CONFIG: SelectConfig = {
    service: null, // Will be injected
    method: 'getEmployeePositionOptions',
    idField: 'id', // Field name to query by ID
    labelFormatter: (employeePosition: any) => `${employeePosition.employee?.code || 'N/A'} - ${employeePosition.employee?.name || 'N/A'} (${employeePosition.position?.name || 'N/A'})`,
    valueField: 'id',
    placeholder: 'Chọn vị trí nhân viên',
    sortField: 'employee.code,asc',
    templateName: 'employeePosition',
    queryBuilder: (searchTerm: string, baseQuery: any) => {
        const query: any = { ...baseQuery };

        if (searchTerm && searchTerm.trim()) {
            query.text = searchTerm.trim();
        }

        return query;
    }
};

// Probation Status Select Configuration
export const PROBATION_STATUS_SELECT_CONFIG: SelectConfig = {
    staticOptions: PROBATION_STATUS_OPTIONS,
    optionLabel: 'label',
    optionValue: 'value',
    placeholder: 'Chọn trạng thái thử việc'
};

// Employee Position Select Configuration - Filtered by Employee ID
export const EMPLOYEE_POSITION_BY_EMPLOYEE_SELECT_CONFIG: SelectConfig = {
    service: null,
    method: 'getEmployeePositionOptions',
    idField: 'id',
    valueField: 'id',
    labelFormatter: (employeePosition: any) => `${employeePosition.positionName || 'N/A'}`,
    sortField: 'positionName,asc',
    placeholder: 'Chọn vị trí',
    templateName: 'employeePosition',
    usePostMethod: true,
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery: any) => {
        const body: any = { ...baseQuery };

        if (idQuery) {
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && searchTerm.trim()) {
            body.orQ = {
                positionName: searchTerm.trim()
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    },
    // Dependency callback only - dependsOn field removed as it's not used
    onDependencyChange: (employeeId: any) => {
        return employeeId ? { andQ: { employeeId: employeeId } } : {};
    }
};

