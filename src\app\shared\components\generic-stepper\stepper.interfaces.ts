import { FormGroup } from '@angular/forms';
import { FormFieldConfig } from '../edit-create-form';

export interface StepperConfig {
  entityType: string;
  width?: string;
  header?: string;
  modal?: boolean;
  dismissableMask?: boolean;
  styleClass?: string;
  linear?: boolean;
  steps: StepConfig[];
  actions: StepAction[];
  onComplete?: () => void;
  onCancel?: () => void;
  confirmOnClose?: boolean;
  hasPartialData?: (stepperState: StepperState) => boolean;
  exitConfirmationMessage?: (stepperState: StepperState) => string;
}



export interface StepConfig {
  value: number;
  label: string;
  entityLabel?: string;
  formConfig?: StepFormConfig;
  handlerConfig?: StepHandlerConfig;
  disabled?: boolean | ((stepperState: StepperState) => boolean);
  hidden?: boolean | ((stepperState: StepperState) => boolean);
}

export interface StepFormConfig {
  fields: FormFieldConfig[];
  editMode?: boolean | ((stepperState: StepperState) => boolean);
  isDisabled?: boolean | ((stepperState: StepperState) => boolean);
}

export interface StepHandlerConfig {
  service: any;
  createMethod?: string;
  updateMethod?: string;
  commonDataTransform?: (formValue: any, stepperState: StepperState) => any;
  createDataTransform?: (formValue: any, stepperState: StepperState) => any;
  updateDataTransform?: (formValue: any, stepperState: StepperState) => any;
  stepDataTransform?: (apiResponse: any, formValue: any, stepperState: StepperState) => any;
}

// Clean action interface - onClick uses function name mapping for simplicity
export interface StepAction {
  label?: string | ((stepperState: StepperState) => string);
  icon?: string | ((stepperState: StepperState) => string);
  severity?: "success" | "info" | "warn" | "danger" | "help" | "primary" | "secondary" | "contrast" | null | undefined;
  location: 'left' | 'right';
  hideOnSteps?: number[];
  showOnSteps?: number[];
  disabled?: (stepperState: StepperState) => boolean;
  hidden?: (stepperState: StepperState) => boolean;
  loading?: (stepperState: StepperState) => boolean;
  onClick: string; // Function name to be mapped in component
}

export type StepMode = 'create' | 'view' | 'edit';

export type StepperEntityType = 'employee-creation';

// Type for stepper config factory functions
export type StepperConfigFactory = (onSuccess: () => void, onCancel: () => void) => StepperConfig;

export type StepperConfigRegistry = Record<StepperEntityType, () => Promise<StepperConfigFactory>>;

export interface StepperState {
  currentStep: number;
  forms: { [stepValue: number]: FormGroup };
  stepData: { [stepValue: number]: any };
  stepModes: { [stepValue: number]: StepMode };
  isLoading: boolean;
  initialData?: any;
}

export interface StepperResult {
  success: boolean;
  data?: any;
  error?: any;
}
