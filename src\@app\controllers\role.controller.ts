import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { JwtPayloadDTO, RoleDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { RoleService } from '@app/services/role.service';
import { CustomValidationPipe, PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Delete, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard, PermissionsGuard)
@Controller('roles')
export class RoleController {
    constructor(private readonly roleService: RoleService) {}

    @Permissions(PERMISSIONS.ROLE_VIEW.code)
    @Get()
    async findWithPagination(@Query(new PaginationPipe()) request: PaginationRequest): Promise<any> {
        return await this.roleService.findWithPagination(request);
    }

    @Permissions(PERMISSIONS.ROLE_EDIT.code)
    @Post()
    async createRole(@Body(new CustomValidationPipe()) dto: RoleDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.roleService.createRole(dto, logged);
    }

    @Permissions(PERMISSIONS.ROLE_EDIT.code)
    @Put()
    async editRole(@Body(new CustomValidationPipe()) dto: RoleDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.roleService.editRole(dto, logged);
    }

    @Permissions(PERMISSIONS.ROLE_EDIT.code)
    @Delete(':code')
    async dateleCourse(@Param('code') code: string, @Logged() logged: JwtPayloadDTO) {
        return await this.roleService.deleteRole(code, logged);
    }
}
