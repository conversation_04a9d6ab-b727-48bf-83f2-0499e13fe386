import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { JwtPayloadDTO, ProbationDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { ProbationService } from '@app/services/probation.service';
import { PaginationPipe } from '@app/shared/pipes';
import { CustomValidationPipe } from '@app/shared/pipes';
import { Body, Controller, Get, HttpCode, HttpStatus, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard)
@Controller('probations')
export class ProbationController {
    constructor(private probationService: ProbationService) { }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.PROBATION_VIEW.code)
    @Get()
    async getWithPagination(@Query(new PaginationPipe()) request: PaginationRequest) {
        return await this.probationService.findWithPagination(request);
    }
    
    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.PROBATION_EDIT.code)
    @Post()
    async createProbation(@Body(new CustomValidationPipe()) dto: ProbationDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.probationService.createProbation(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.PROBATION_EDIT.code)
    @Put()
    async updateProbation(@Body(new CustomValidationPipe()) dto: ProbationDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.probationService.updateProbation(dto, logged);
    }

    // @HttpCode(HttpStatus.OK)
    // @UseGuards(PermissionsGuard)
    // @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    // @Delete()
    // async deleteProbation(@Param('code') code: string, @Logged() logged: JwtPayloadDTO) {
    //     return await this.probationService.deleteProbation(code, logged);
    // }
}
