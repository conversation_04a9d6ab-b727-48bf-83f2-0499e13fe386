import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { CourseDocument } from '../schemas';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>per, StringHelper } from '@app/shared/helpers';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { SearchOptionsDTO } from '@app/models/dto';

@Injectable()
export class CourseRepository extends GenericRepository<CourseDocument> {
    private readonly logger = new Logger(CourseRepository.name);

    constructor(
        @Inject(MONGO_CONST.COURSE_COLLECTION)
        private readonly courseModel: Model<CourseDocument>,
    ) {
        super(courseModel);
    }

    async findExistingCodes(codes: string[]): Promise<string[]> {
        const result = await this.courseModel.aggregate([
            {
                $match: { code: { $in: codes } },
            },
            {
                $project: {
                    _id: 0,
                    code: 1,
                },
            },
            {
                $group: {
                    _id: null,
                    existedCodes: { $push: '$code' },
                },
            },
        ]);
        return ArrayHelper.isEmpty(result) ? [] : result[0].existedCodes;
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const type = andQ?.type || null;
        const name = orQ?.name || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        }
        if (!StringHelper.isEmpty(type)) {
            matchQ['type'] = type;
        }
        if (!StringHelper.isEmpty(name)) {
            matchQ['$or'] = [{ name: { $regex: name, $options: 'i' } }];
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.courseModel.aggregate([
            {
                $match: matchQ,
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    name: 1,
                    type: 1,
                    level: 1,
                    teacher: 1,
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }
}
