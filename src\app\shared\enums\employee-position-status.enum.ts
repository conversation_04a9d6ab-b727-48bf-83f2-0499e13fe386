export enum EmployeePositionStatus {
    TO_ONBOARD = 'TO_ONBOARD',
    PROBATION = 'PROBATION',
    PROBATION_OVERDUE = 'PROBATION_OVERDUE',
    PROBATION_PENDING = 'PROBATION_PENDING',
    PROBATION_FAIL = 'PROBATION_FAIL',
    PROBATION_PASS = 'PROBATION_PASS',
    PROBATION_CANCELLED = 'PROBATION_CANCELLED',
    ACTIVE = 'ACTIVE',
    TO_INACTIVE = 'TO_INACTIVE',
    INACTIVE = 'INACTIVE',
}

export const EMPLOYEE_POSITION_STATUS_CONFIG = {
    [EmployeePositionStatus.TO_ONBOARD]: {
        label: 'Onboard',
        cssClass: 'text-sm !bg-blue-100 !text-blue-700'
    },
    [EmployeePositionStatus.PROBATION]: {
        label: 'Thử việc',
        cssClass: 'text-sm !bg-blue-100 !text-blue-700'
    },
    [EmployeePositionStatus.PROBATION_OVERDUE]: {
        label: 'Th<PERSON> việc (qu<PERSON> hạn)',
        cssClass: 'text-sm !bg-red-100 !text-red-700'
    },
    [EmployeePositionStatus.PROBATION_PENDING]: {
        label: 'Thử việc (chờ kết quả)',
        cssClass: 'text-sm !bg-yellow-100 !text-yellow-700'
    },
    [EmployeePositionStatus.PROBATION_FAIL]: {
        label: 'Thử việc (fail)',
        cssClass: 'text-sm !bg-red-100 !text-red-700'
    },
    [EmployeePositionStatus.PROBATION_PASS]: {
        label: 'Thử việc (pass)',
        cssClass: 'text-sm !bg-green-100 !text-green-700'
    },
    [EmployeePositionStatus.PROBATION_CANCELLED]: {
        label: 'Thử việc (cancelled)',
        cssClass: 'text-sm !bg-gray-100 !text-gray-700'
    },
    [EmployeePositionStatus.ACTIVE]: {
        label: 'Đang làm',
        cssClass: 'text-sm !bg-green-100 !text-green-700'
    },
    [EmployeePositionStatus.TO_INACTIVE]: {
        label: 'Sắp nghỉ',
        cssClass: 'text-sm !bg-orange-100 !text-orange-700'
    },
    [EmployeePositionStatus.INACTIVE]: {
        label: 'Đã nghỉ',
        cssClass: 'text-sm !bg-gray-100 !text-gray-700'
    }
};
