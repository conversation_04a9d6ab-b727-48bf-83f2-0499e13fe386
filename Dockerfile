# Step 1: Build Angular app
FROM node:18-alpine AS build

WORKDIR /app

# Add build argument for environment
ARG BUILD_ENV=prod
ENV BUILD_ENV=${BUILD_ENV}

COPY package*.json ./
RUN npm install

COPY . .
# Use different build commands based on the environment
RUN if [ "$BUILD_ENV" = "dev" ]; then \
        npm run build:dev; \
    else \
        npm run build:prod; \
    fi

# Step 2: Serve using Nginx
FROM nginx:alpine

# Remove default nginx static assets
RUN rm -rf /usr/share/nginx/html/*

# Copy build output to Nginx HTML folder
COPY --from=build /app/dist/weset-fe/browser /usr/share/nginx/html

# Copy custom Nginx config
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]