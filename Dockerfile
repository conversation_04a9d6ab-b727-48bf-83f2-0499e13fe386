#Stage 1: Build
FROM node:18-alpine AS build
WORKDIR /user/src/app
COPY package.json tsconfig.json ./
RUN npm install --no-cache
COPY ./src ./src
COPY ./environments ./environments
RUN npm run build
COPY ./src/email-templates ./dist/email-templates

# Stage 2: Production
FROM node:18-alpine AS production
WORKDIR /user/src/app
RUN apk add --no-cache curl
COPY package.json tsconfig.json ./
RUN npm install --only=production --omit-dev && npm cache clean --force
COPY --from=build /user/src/app/dist ./dist

EXPOSE 8800
CMD [ "node", "dist/main" ]
