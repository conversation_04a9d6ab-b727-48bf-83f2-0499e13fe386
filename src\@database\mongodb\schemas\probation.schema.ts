import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';
import { ProbationStatusEnum } from '@app/enums';

@Schema({ versionKey: false, timestamps: true })
export class ProbationDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    employeePositionId: string;
    @Prop({ index: true, enum: ProbationStatusEnum, default: ProbationStatusEnum.IN_PROGRESS })
    status: ProbationStatusEnum;
    @Prop({ index: true, required: true })
    fromDate: Date;
    @Prop({ index: true, default: null })
    toDate: Date;
    @Prop({ index: true, required: true })
    deadline: Date;
    @Prop({ index: true, default: false })
    isManual: boolean;
}
export const ProbationSchema = SchemaFactory.createForClass(ProbationDocument);
