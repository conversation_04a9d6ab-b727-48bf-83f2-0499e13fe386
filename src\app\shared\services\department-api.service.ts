import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequestHelper } from '@shared/helpers/request.helper';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class DepartmentApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/departments`;
    }

    getDepartments(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(this.url, { params: options });
    }

    createDepartment(department: any): Observable<any> {
        return this.httpClient.post<any>(this.url, department);
    }

    updateDepartment(department: any): Observable<any> {
        return this.httpClient.put<any>(this.url, department);
    }

    deleteDepartment(id: string): Observable<any> {
        return this.httpClient.delete<any>(`${this.url}/${id}`);
    }

    getRoots(): Observable<any> {
        return this.httpClient.get<any>(`${this.url}/root`);
    }

    getAllDepartments(): Observable<any> {
        return this.httpClient.get<any>(`${this.url}/all`);
    }

    getOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }
}
