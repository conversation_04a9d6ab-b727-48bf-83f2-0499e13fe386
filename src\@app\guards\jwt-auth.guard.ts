import { JwtPayloadDTO } from '@app/models/dto/jwt-payload.dto';
import { UserService } from '@app/services';
import { StringHelper } from '@app/shared/helpers';
import { BcError, UnauthorizedError } from '@errors/error-base';
import { errorCode } from '@errors/error-message';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtGuard implements CanActivate {
    constructor(
        private jwtService: JwtService,
        private configService: ConfigService,
        private userService: UserService,
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new UnauthorizedError('Mising token');
        }
        try {
            const payload: JwtPayloadDTO = await this.jwtService.verifyAsync(token, {
                secret: this.configService.get('APP_TOKEN_PRIVATE_KEY'),
            });
            const user = await this.userService.getUserAvailable(payload.username);
            if (!user) {
                throw new UnauthorizedError('User not found');
            }
            if (user.permissionsKey != payload.perKey) {
                throw new BcError(errorCode.PERMISSIONS_CHANGED);
            }
            request['user'] = user;
        } catch (error) {
            if (error?.name === 'TokenExpiredError') {
                throw new BcError(errorCode.TOKEN_EXPIRED);
            }
            throw new UnauthorizedError('Token not valid');
        }
        return true;
    }

    private extractTokenFromHeader(request: Request): string | undefined {
        const [type, token] = request.headers.authorization?.split(StringHelper.SPACE) ?? [];
        return type === 'Bearer' ? token : undefined;
    }
}
