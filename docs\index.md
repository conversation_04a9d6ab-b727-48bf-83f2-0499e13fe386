# Documentation Index

This is an automatically generated index of all documentation files in the project.

**Generated on:** 2025-07-17  
**Total Files:** 15

## 📚 Documentation Structure

### 📁 Architecture

- [Project Overview](./architecture/project-overview.md)

### 📁 Components

- [Components Documentation](./components/README.md)
- [Edit Create Form Component Usage](./components/edit-create-form.md)
- [Edit-Create-Dialog System Documentation](./components/edit-create-dialog.md)
- [Generic P-Table Component Usage Guide](./components/generic-p-table.md)
- [Generic Popover Component - Usage Examples](./components/generic-popover.md)
- [Generic Stepper Component - Usage Examples](./components/generic-stepper.md)

### 📁 Examples

- [Examples & Patterns Documentation](./examples/README.md)

### 📁 Root Documentation

- [Portal Frontend Documentation](./README.md)
- [Documentation Index](./index.md)
- [Documentation Navigation Guide](./navigation.md)

### 📁 Services

- [Services Documentation](./services/README.md)
- [Edit Create Form Config Service - Usage Examples](./services/edit-create-form-config.md)
- [Form Handler Service Documentation](./services/form-handler.md)
- [PopoverConfigService Documentation](./services/popover-config.md)

## 🚀 Quick Navigation

### By Category
- **Components:** [Components Overview](./components/README.md)
- **Services:** [Services Overview](./services/README.md)
- **Examples:** [Examples & Patterns](./examples/README.md)
- **Testing:** [Testing Guidelines](./testing/README.md)

### By Task
- **Getting Started:** [Project Overview](./architecture/project-overview.md)
- **Component Usage:** [Component Integration](./examples/component-integration.md)
- **Configuration:** [Configuration Examples](./examples/configuration-examples.md)
- **Troubleshooting:** [Troubleshooting Guide](./deployment/troubleshooting.md)

## 🔧 Maintenance

This index is automatically generated by running:
```bash
node scripts/generate-docs-index.js
```

To update the index after adding new documentation:
1. Add your new markdown files to the appropriate directory
2. Run the index generation script
3. Commit the updated index.md file

---

**💡 Tip:** Use the [Navigation Guide](./navigation.md) for task-based documentation discovery.
