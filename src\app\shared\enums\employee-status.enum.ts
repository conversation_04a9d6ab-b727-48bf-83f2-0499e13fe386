export enum EmployeeStatus {
    NO_POSITION = 'NO_POSITION',
    TO_ONBOARD = 'TO_ONBOARD',
    PROBATION = 'PROBATION',
    PROBATION_PENDING = 'PROBATION_PENDING',
    PROBATION_FAIL = 'PROBATION_FAIL',
    PROBATION_PASS = 'PROBATION_PASS',
    ACTIVE = 'ACTIVE',
    TO_DEACTIVE = 'TO_DEACTIVE',
    INACTIVE = 'INACTIVE',
}

export const EMPLOYEE_STATUS_CONFIG = {
    [EmployeeStatus.NO_POSITION]: {
        label: 'Chưa có vị trí',
        cssClass: 'text-sm !bg-gray-100 !text-gray-700'
    },
    [EmployeeStatus.TO_ONBOARD]: {
        label: 'Onboard',
        cssClass: 'text-sm !bg-blue-100 !text-blue-700'
    },
    [EmployeeStatus.PROBATION]: {
        label: 'Thử việc',
        cssClass: 'text-sm !bg-blue-100 !text-blue-700'
    },
    [EmployeeStatus.PROBATION_PENDING]: {
        label: 'Th<PERSON> việc (chờ kết quả)',
        cssClass: 'text-sm !bg-yellow-100 !text-yellow-700'
    },
    [EmployeeStatus.PROBATION_FAIL]: {
        label: 'Thử việc (fail)',
        cssClass: 'text-sm !bg-red-100 !text-red-700'
    },
    [EmployeeStatus.PROBATION_PASS]: {
        label: 'Thử việc (pass)',
        cssClass: 'text-sm !bg-green-100 !text-green-700'
    },
    [EmployeeStatus.ACTIVE]: {
        label: 'Đang làm',
        cssClass: 'text-sm !bg-green-100 !text-green-700'
    },
    [EmployeeStatus.TO_DEACTIVE]: {
        label: 'Sắp nghỉ',
        cssClass: 'text-sm !bg-orange-100 !text-orange-700'
    },
    [EmployeeStatus.INACTIVE]: {
        label: 'Đã nghỉ',
        cssClass: 'text-sm !bg-gray-100 !text-gray-700'
    }
};
