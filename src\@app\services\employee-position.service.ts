import { EVENTS } from '@app/constants';
import { Logged } from '@app/decorators';
import { ProbationStatusEnum } from '@app/enums';
import { AssignEmployeePositionDTO, JwtPayloadDTO, RetireDTO, SearchOptionsDTO, UpdateEmployeePositionDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { ArrayHelper, StringHelper } from '@app/shared/helpers';
import { DepartmentRepository, EmployeePositionHistoryRepository, EmployeePositionRepository, EmployeeRepository, PositionRepository } from '@database/mongodb/repositories';
import { NotFoundError, RequestInvalidError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
@Injectable()
export class EmployeePositionService {
    private readonly logger = new Logger(EmployeePositionService.name);

    constructor(
        private employeePositionRepository: EmployeePositionRepository,
        private departmentRepository: DepartmentRepository,
        private positionRepository: PositionRepository,
        private employeeRepository: EmployeeRepository,
        private employeePositionHistoryRepository: EmployeePositionHistoryRepository,
        private eventEmitter: EventEmitter2,
    ) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.employeePositionRepository.findWithPaginationAggregated(request);
    }

    async getEmployeePositionOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.employeePositionRepository.getOptions(request, body);
    }

    async assignPositionToEmployee(data: AssignEmployeePositionDTO, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug('assignEmployeePosition', JSON.stringify(data));
        const employee = await this.employeeRepository.findOne({ id: data.employeeId, deletedAt: null });
        if (!employee) {
            throw new NotFoundError('Employee not found');
        }
        // Check if the position exists
        const position = await this.positionRepository.findOne({ id: data.positionId, deletedAt: null });
        if (!position) {
            throw new NotFoundError('Position not found');
        }
        await this.checkDepartmentInRoot(position.departmentId, data.departmentId);
        const positionOngoing = await this.employeePositionRepository.checkExistingPosition(data.employeeId, data.positionId, data.fromDate);
        if (positionOngoing) {
            throw new RequestInvalidError('Employee position already exists');
        }

        const result = await this.employeePositionRepository.create({
            employeeId: data.employeeId,
            positionId: data.positionId,
            departmentId: data.departmentId,
            fromDate: data.fromDate,
            status: this.employeePositionRepository.mappingStatus({ fromDate: data.fromDate, toDate: null }),
            createdBy: logged.username,
            updatedBy: logged.username,
        });

        await this.employeePositionHistoryRepository.create({
            employeePositionId: result.id,
            departmentId: data.departmentId,
            fromDate: data.fromDate,
            createdBy: logged.username,
            updatedBy: logged.username,
        });
        await this.eventEmitter.emitAsync(EVENTS.EMPLOYEE_POSITION_STATUS_CHANGED, { employeeId: data.employeeId });
        return result;
    }

    async updateEmployeePosition(data: UpdateEmployeePositionDTO, @Logged() logged: JwtPayloadDTO) {
        const results = await this.employeePositionRepository.getEmployeePositionByIdFull(data.id);
        if (ArrayHelper.isEmpty(results)) {
            throw new NotFoundError('Employee position not found');
        }
        const employeePosition = results[0];
        await this.checkDepartmentInRoot(employeePosition?.position?.departmentId, data.departmentId);
        const result = await this.employeePositionRepository.updateOne(
            { id: data.id },
            {
                positionId: data.positionId,
                fromDate: data.fromDate,
                departmentId: data.departmentId,
                updatedBy: logged.username,
            },
        );

        await this.employeePositionHistoryRepository.updateOne(
            { employeePositionId: data.id },
            {
                positionId: data.positionId,
                departmentId: data.departmentId,
                fromDate: data.fromDate,
                updatedBy: logged.username,
            },
        );
        await this.eventEmitter.emitAsync(EVENTS.EMPLOYEE_POSITION_UPDATED, { employeePositionId: data.id });
    }

    async checkDepartmentInRoot(rootId: string, departmentId: string) {
        if (rootId == departmentId) return;
        const departmentResults: any = await this.departmentRepository.getChildren(rootId);
        if (
            ArrayHelper.isEmpty(departmentResults) ||
            (!ArrayHelper.isEmpty(departmentResults[0].childs) && departmentResults[0].childs.findIndex(i => i.id === departmentId)) == -1
        ) {
            throw new RequestInvalidError('Department not in the position');
        }
    }

    async retireEmployeePosition(dto: RetireDTO, logged: JwtPayloadDTO) {
        const entities: any = await this.employeePositionRepository.getEmployeePositionExtProbation(dto.id);
        const data = ArrayHelper.isEmpty(entities) ? null : entities[0];
        if (!data) {
            throw new NotFoundError('EmployeePosition not found');
        }
        if (data.fromDate > dto.toDate) {
            throw new RequestInvalidError('Retire date is before onboard date');
        }
        if (data.probation && (data.probation.status != ProbationStatusEnum.PASS || dto.toDate < data.probation.toDate)) {
            throw new RequestInvalidError('probation not finished');
        }
        await this.employeePositionRepository.updateOne({ id: dto.id }, { toDate: dto.toDate, updatedBy: logged.username });
        await this.eventEmitter.emitAsync(EVENTS.EMPLOYEE_POSITION_UPDATED, { employeePositionId: data.id });
    }
}
