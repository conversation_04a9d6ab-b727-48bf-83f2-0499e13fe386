// Component-specific styles for generic-p-table
:host {
    display: block;
    width: 100%;
}

// Ensure proper table layout
.p-datatable {
    .p-datatable-wrapper {
        overflow: auto;
    }
}

// Action buttons styling
.p-button.p-button-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

// Toolbar styling
.p-toolbar {
    border: none !important;
    
    h3 {
        margin: 0;
        color: var(--text-color);
    }
}

// Loading overlay
.p-datatable .p-datatable-loading-overlay {
    background-color: rgba(255, 255, 255, 0.8);
}
