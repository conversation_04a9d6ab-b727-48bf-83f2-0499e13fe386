import { Component, Input, OnInit, OnDestroy, OnChanges, SimpleChanges, forwardRef, ViewChild, TemplateRef, ContentChild, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';
import { TreeSelectModule } from 'primeng/treeselect';
import { ChipModule } from 'primeng/chip';
import { TreeNode } from 'primeng/api';
import { ErrorHandlerService } from '@shared/services';

export interface TreeSelectConfig<T = any> {
    service: any; // The service to call
    method: string; // Method name to call on the service
    treeBuilder: (items: T[]) => TreeNode[]; // Function to build tree structure
    valueField: string; // Which field to use as value
    placeholder?: string;
    // Additional customization options
    baseQuery?: any; // Static query that's always applied
    queryBuilder?: (searchTerm: string, baseQuery?: any) => any; // Custom query builder
    additionalParams?: any; // Additional parameters to pass to the service
    transformResponse?: (response: any) => any; // Transform the response before processing
    selectionMode?: 'single' | 'multiple' | 'checkbox'; // Selection mode
    sortField?: string; // Sort field for data ordering
    // POST method support (similar to generic-select)
    usePostMethod?: boolean; // Whether to use POST instead of GET
    bodyBuilder?: (searchTerm: string, baseQuery: any, idQuery?: any) => any; // Function to build request body for POST
    idField?: string; // Field name to query by ID (for pre-selected values)
    // Dependency tracking for automatic reloading (use [dependencyValue] input)
    onDependencyChange?: (dependencyValue: any) => any; // Callback to update baseQuery when dependency changes
}

@Component({
    selector: 'generic-p-treeselect',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        TreeSelectModule,
        ChipModule
    ],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => GenericTreeSelectComponent),
            multi: true
        }
    ],
    template: `
    <p-treeselect #genericTreeSelect
                [id]="id"
                [options]="treeOptions"
                [placeholder]="config.placeholder || placeholder"
                [disabled]="disabled"
                [filter]="filter"
                [selectionMode]="config.selectionMode || selectionMode"
                [loading]="loading"
                [appendTo]="appendTo"
                [containerStyleClass]="containerStyleClass"
                [showClear]="showClear"
                [ngModel]="value"
                (ngModelChange)="onValueChange($event)">
        <ng-template #empty>
            <ng-container *ngIf="loading; else noResults">
                <span class="italic">Loading — please wait…</span>
            </ng-container>
            <ng-template #noResults>
                <span class="italic">No results found.</span>
            </ng-template>
        </ng-template>
    </p-treeselect>
    `
})
export class GenericTreeSelectComponent<T = any> implements OnInit, OnDestroy, OnChanges, ControlValueAccessor {
    @Input() id: string = '';
    @Input() placeholder: string = 'Chọn...';
    @Input() disabled: boolean = false;
    @Input() filter: boolean = true;
    @Input() selectionMode: 'single' | 'multiple' | 'checkbox' = 'single';
    @Input() appendTo: string = 'body';
    @Input() containerStyleClass: string = 'w-full';
    @Input() showClear: boolean = false;
    @Input() config!: TreeSelectConfig<T>;
    @Input() additionalFilters: any = {}; // Dynamic filters that can change
    @Input() dependencyValue: any = null; // Value that this treeselect depends on

    @ContentChild('selectedItem') selectedItemTemplate?: TemplateRef<any>;

    @ViewChild('genericTreeSelect') genericTreeSelect!: any;

    // Data for tree select
    items: T[] = [];
    treeOptions: TreeNode[] = [];

    // Loading state
    loading: boolean = false;

    // ControlValueAccessor properties
    value: any = null; // Will be TreeNode object(s) or key(s) depending on selection mode
    pendingValue: any = null; // Store value when tree data isn't loaded yet
    onChange = (value: any) => { };
    onTouched = () => { };

    constructor(
        private errorHandlerService: ErrorHandlerService,
        private cd: ChangeDetectorRef
    ) { }

    ngOnInit() {
        if (!this.config) {
            throw new Error('GenericTreeSelectComponent requires a config input');
        }

        // Load initial data
        this.loadData();
    }

    ngOnChanges(changes: SimpleChanges) {
        // Reload data when additionalFilters change
        if (changes['additionalFilters'] && !changes['additionalFilters'].firstChange) {
            this.loadData();
        }

        // Reload data when dependencyValue changes
        if (changes['dependencyValue'] && !changes['dependencyValue'].firstChange) {
            const currentValue = changes['dependencyValue'].currentValue;
            const previousValue = changes['dependencyValue'].previousValue;

            // Only reload if the value actually changed
            if (currentValue !== previousValue) {
                // Clear current selection when dependency changes
                this.value = null;
                this.onChange(null);

                // Update baseQuery using onDependencyChange callback if available
                if (this.config.onDependencyChange) {
                    this.config.baseQuery = this.config.onDependencyChange(currentValue);
                }

                // Reload data if dependency has a value, otherwise clear data
                if (currentValue !== null && currentValue !== undefined) {
                    this.loadData();
                } else {
                    this.items = [];
                    this.treeOptions = [];
                }
            }
        }
    }

    ngOnDestroy() {
        // Cleanup if needed
    }

    loadData() {
        this.loading = true;

        // Build query using custom query builder or default logic
        let query = {};
        let body = null;

        if (this.config.usePostMethod && this.config.bodyBuilder) {
            // Use custom body builder for POST requests (no ID query for regular data loading)
            body = this.config.bodyBuilder('', {
                ...this.config.baseQuery,
                ...this.additionalFilters
            }, null);
        } else if (this.config.queryBuilder) {
            // Use custom query builder
            query = this.config.queryBuilder('', {
                ...this.config.baseQuery,
                ...this.additionalFilters
            });
        } else {
            // Default query building logic
            const baseQuery = { ...this.config.baseQuery, ...this.additionalFilters };
            if (Object.keys(baseQuery).length > 0) {
                query = baseQuery;
            }
        }

        // Call the service method dynamically
        const serviceMethod = this.config.service[this.config.method];
        if (!serviceMethod) {
            console.error(`Method ${this.config.method} not found on service`);
            this.loading = false;
            return;
        }

        // For POST methods, we need to pass pagination params and body separately
        // For GET methods, we use the old parameter structure
        let serviceCall;
        if (this.config.usePostMethod) {
            // Create pagination request for POST methods
            const params = { ...this.config.additionalParams };
            serviceCall = serviceMethod.call(this.config.service, params, body);
        } else {
            // Legacy support for GET methods
            const hasParams = query || (this.config.additionalParams && Object.keys(this.config.additionalParams).length > 0);
            const params = hasParams ? {
                query: query,
                ...this.config.additionalParams
            } : undefined;

            serviceCall = params ?
                serviceMethod.call(this.config.service, params) :
                serviceMethod.call(this.config.service);
        }

        serviceCall.subscribe({
            next: (response: any) => {
                // Apply response transformation if provided
                const transformedResponse = this.config.transformResponse
                    ? this.config.transformResponse(response)
                    : response;

                const { hasError, data } = this.errorHandlerService.handleInternal(transformedResponse);
                if (!hasError) {
                    // For POST methods with pagination, extract items from the response
                    // For GET methods, use data directly
                    let items = data || [];
                    if (this.config.usePostMethod && data && data.items) {
                        items = data.items;
                    }

                    this.items = items;

                    // Build tree structure using the provided tree builder
                    this.treeOptions = this.config.treeBuilder(this.items);

                    // After loading data, apply any pending value
                    this.applyPendingValue();

                    this.loading = false;
                }
            },
            error: () => {
                this.loading = false;
            }
        });
    }

    onValueChange(value: any) {
        this.value = value;

        // Convert TreeSelect value back to form control format
        const formValue = this.convertToFormValue(value);
        this.onChange(formValue);
        this.onTouched();
    }

    // ControlValueAccessor implementation
    writeValue(value: any): void {
        if (this.treeOptions.length === 0) {
            // Tree data not loaded yet, store the value for later
            this.pendingValue = value;
            this.value = this.getDefaultValue();
        } else {
            // Tree data is available, convert immediately
            this.pendingValue = null;
            this.value = this.convertToTreeSelectValue(value);
            this.cd.markForCheck();
        }
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    // Helper methods for value conversion
    private findNodeByKey(key: string, nodes: TreeNode[]): TreeNode | null {
        for (const node of nodes) {
            if (node.key === key) {
                return node;
            }
            if (node.children && node.children.length > 0) {
                const found = this.findNodeByKey(key, node.children);
                if (found) return found;
            }
        }
        return null;
    }

    private convertToTreeSelectValue(formValue: any): any {
        if (!formValue || this.treeOptions.length === 0) {
            return this.getDefaultValue();
        }

        const selectionMode = this.config.selectionMode || this.selectionMode;

        if (selectionMode === 'single') {
            // For single selection, find the TreeNode by key
            const node = this.findNodeByKey(formValue, this.treeOptions);
            return node || null;
        } else {
            // For multiple/checkbox selection, convert array of keys to array of TreeNodes
            if (!Array.isArray(formValue)) {
                return this.getDefaultValue();
            }

            const selectedNodes: TreeNode[] = [];
            formValue.forEach(key => {
                const node = this.findNodeByKey(key, this.treeOptions);
                if (node) {
                    selectedNodes.push(node);
                }
            });
            return selectedNodes;
        }
    }

    private convertToFormValue(treeSelectValue: any): any {
        if (!treeSelectValue) {
            return null;
        }

        const selectionMode = this.config.selectionMode || this.selectionMode;

        if (selectionMode === 'single') {
            // For single selection, extract the key from TreeNode
            return treeSelectValue.key || treeSelectValue;
        } else {
            // For multiple/checkbox selection, extract keys from array of TreeNodes
            if (!Array.isArray(treeSelectValue)) {
                return [];
            }
            return treeSelectValue.map(node => node.key || node);
        }
    }

    private getDefaultValue(): any {
        const selectionMode = this.config.selectionMode || this.selectionMode;
        return selectionMode === 'single' ? null : [];
    }

    private applyPendingValue(): void {
        if (this.pendingValue !== null && this.pendingValue !== undefined) {
            // Apply the pending value now that tree data is loaded
            this.value = this.convertToTreeSelectValue(this.pendingValue);
            this.pendingValue = null;
            this.cd.markForCheck();
        }
    }

    // Public methods
    reset() {
        this.items = [];
        this.treeOptions = [];
        this.value = this.getDefaultValue();
        this.pendingValue = null;
    }

    reload() {
        this.loadData();
    }
}
