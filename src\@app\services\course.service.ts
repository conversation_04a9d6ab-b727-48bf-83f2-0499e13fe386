import { CourseType, SettingType } from '@app/enums';
import { SyncType } from '@app/enums/sync-type.enum';
import { CreateCourseDTO, SearchOptionsDTO } from '@app/models/dto';
import { JwtPayloadDTO } from '@app/models/dto/jwt-payload.dto';
import { PaginationRequest } from '@app/models/pagination';
import { Array<PERSON>elper, ExceltHelper, StringHelper } from '@app/shared/helpers';
import { TeleBotService } from '@app/shared/services/tele-bot.service';
import { CourseRepository, SyncErrorDataRepository, SettingRepository } from '@database/mongodb/repositories';
import { CourseDocument } from '@database/mongodb/schemas';
import { NotFoundError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { GoogleSheetsService } from './google-sheet.service';

@Injectable()
export class CourseService {
    private readonly logger = new Logger(CourseService.name);

    constructor(
        private courseRepository: CourseRepository,
        private settingRepository: SettingRepository,
        private googleSheetsService: GoogleSheetsService,
        private teleBotService: TeleBotService,
        private syncErrorDataRepository: SyncErrorDataRepository,
    ) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.courseRepository.findWithPagination(request);
    }

    async createCourse(dto: CreateCourseDTO, logged: JwtPayloadDTO) {
        this.logger.debug('createCourse', dto);
        const levelSetting = await this.settingRepository.findOne({ code: dto.level });
        if (!levelSetting) {
            throw new NotFoundError('Level not found');
        }
        await this.courseRepository.create({
            code: dto.code.toLowerCase(),
            name: dto.code,
            level: dto.level,
            createdBy: logged.username,
            updatedBy: logged.username,
        });
    }

    async deleteCourse(id: string, logged: JwtPayloadDTO) {
        this.logger.debug('deleteCourse', id);
        await this.courseRepository.updateOne({ id }, { updatedBy: logged.username, deletedAt: new Date(), deletedBy: logged.username });
    }

    // async import(filePath: string, logged: JwtPayloadDTO, isFull?: string): Promise<any[]> {
    //     const workbook = new ExcelJS.Workbook();
    //     await workbook.xlsx.readFile(filePath);
    //     const worksheet = workbook.worksheets[0];

    //     let dataItems: any[] = [];
    //     let checkCourseCodes = new Set<string>();
    //     let checkCourseLevels = new Set<string>();
    //     let errors: string[];
    //     let validItems: any[] = [];
    //     let invalidItems: any[] = [];

    //     let rowValue: any;
    //     let courseType: CourseType;
    //     let code: string;
    //     let level: string;
    //     let item;
    //     worksheet.eachRow((row, rowNumber) => {
    //         if (rowNumber == 1) {
    //             return;
    //         }
    //         errors = [];
    //         rowValue = row.values;

    //         courseType = ExceltHelper.parseEnum(rowValue[1], CourseType);
    //         if (!courseType) {
    //             errors.push('Loại lớp không hợp lệ');
    //             courseType = rowValue[1];
    //         }

    //         code = ExceltHelper.parseString(rowValue[2]);
    //         checkCourseCodes.add(code?.toLowerCase());

    //         level = ExceltHelper.parseString(rowValue[3]);
    //         checkCourseLevels.add(level);

    //         item = {
    //             type: courseType,
    //             code: code?.toLowerCase(),
    //             name: code,
    //             level,
    //         };
    //         if (ArrayHelper.isEmpty(errors)) {
    //             dataItems.push(item);
    //         } else {
    //             invalidItems.push({
    //                 ...item,
    //                 errorMessage: errors.join(StringHelper.SPLITTER),
    //             });
    //         }
    //     });

    //     const [existedCourseCodes, existedCourseLevels] = await Promise.all([
    //         this.courseRepository.findExistingCodes(Array.from(checkCourseCodes)),
    //         this.settingRepository.findExistingCodes(Array.from(checkCourseLevels)),
    //     ]);
    //     const invalidCourseCodes = Array.from(checkCourseCodes).filter(i => existedCourseCodes.includes(i));
    //     const invalidCourseLevels = Array.from(checkCourseLevels).filter(i => !existedCourseLevels.includes(i));

    //     dataItems.forEach(i => {
    //         errors = [];
    //         // Check code is require and exist
    //         if (!i.code || invalidCourseCodes.includes(i.code)) {
    //             errors.push('Mã lớp không được để trống hoặc đã tồn tại');
    //         }

    //         if (!i.level || invalidCourseLevels.includes(i.level)) {
    //             errors.push('Level không được để trống hoặc không tồn tại');
    //         }

    //         if (ArrayHelper.isEmpty(errors)) {
    //             validItems.push({
    //                 ...i,
    //                 createdBy: logged.username,
    //                 updatedBy: logged.username,
    //             });
    //         } else {
    //             invalidItems.push({
    //                 ...i,
    //                 errorMessage: errors.join(StringHelper.SPLITTER),
    //             });
    //         }
    //     });
    //     if (isFull == '1') {
    //         return [...validItems, invalidItems];
    //     }
    //     if (!ArrayHelper.isEmpty(validItems)) {
    //         await this.courseRepository.insertMany(validItems);
    //     }
    //     return invalidItems;
    // }

    async syncCourseFromSheet() {
        let dataImported: Partial<CourseDocument>[] = [];
        let dataInvalid: any[] = [];
        try {
            const data = await this.googleSheetsService.readSheet(SyncType.COURSE);
            const settings = await this.settingRepository.findAll({ type: SettingType.COURSE_LEVEL });
            let itemVal: Partial<CourseDocument>;
            let errors: string[];
            const sheetIndexs = {
                courseType: 0,
                courseName: 1,
                courseLevel: 2,
                courseTeacher: 3,
            };
            data.forEach(item => {
                if (StringHelper.isEmpty(item[sheetIndexs.courseName])) {
                    return;
                }
                errors = [];
                itemVal = {
                    name: item[sheetIndexs.courseName],
                    type: ExceltHelper.parseEnumRaw(item[sheetIndexs.courseType], 'courseType', CourseType, errors, true),
                    level: ExceltHelper.parseSettingRaw(item[sheetIndexs.courseLevel], 'courseLevel', 'code', settings, errors),
                    teacher: item[sheetIndexs.courseTeacher]
                };

                if (itemVal.type == CourseType.TUTOR && itemVal.level) {
                    errors.push('Loại lớp là TUTOR thì courseLevel không được có giá trị');
                } else if (itemVal.type == CourseType.CLASS && !itemVal.level) {
                    errors.push('Loại lớp là CLASS thì courseLevel không được để trống / courseLevel không hợp lệ');
                } else if (dataImported.findIndex(i => i.name.toLowerCase() == itemVal.name.toLowerCase()) != -1) {
                    errors.push('Mã lớp đã tồn tại');
                }

                if (!ArrayHelper.isEmpty(errors)) {
                    dataInvalid.push({
                        data: item,
                        sheetIndexs,
                        error: errors.join(StringHelper.SPLITTER),
                    });
                } else {
                    dataImported.push({
                        ...itemVal,
                        code: itemVal.name.toLowerCase(),
                    });
                }
            });
            await this.courseRepository.deleteMany({});
            await this.courseRepository.insertMany(dataImported);

            if (!ArrayHelper.isEmpty(dataInvalid)) {
                await this.syncErrorDataRepository.create({
                    data: dataInvalid,
                    type: SyncType.COURSE,
                });
            }
        } catch (error) {
            this.teleBotService.notifyMessage(`syncCourseFromSheet has error ${JSON.stringify(error?.message)}`);
            await this.syncErrorDataRepository.create({
                type: SyncType.COURSE,
                message: error?.detail || error?.message,
            });
        }
    }

    async getCourseOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.courseRepository.getOptions(request, body);
    }
}
