import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

@Schema({ versionKey: false, timestamps: true })
export class DepartmentDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ default: null })
    name: string;
    @Prop({ index: true, default: null })
    parentId: string;
}
export const DepartmentSchema = SchemaFactory.createForClass(DepartmentDocument);
