<p-toolbar styleClass="mb-4 !border-none">
    <ng-template #start>
        <h3 class="text-xl font-semibold">Q<PERSON>ản lý phân quyền</h3>
    </ng-template>
    <ng-template #end>
        <p-button pRipple
                  styleClass="h-10"
                  icon="pi pi-plus"
                  label="Add Role"
                  (onClick)="openNew()" />
    </ng-template>
</p-toolbar>

<p-table [value]="roles"
         [lazy]="true"
         [paginator]="true"
         [rows]="itemPerPage"
         [totalRecords]="totalRecords"
         [rowsPerPageOptions]="itemPerPageOptions"
         [showCurrentPageReport]="true"
         currentPageReportTemplate="{first} - {last} of {totalRecords} roles"
         (onLazyLoad)="onLazyLoad($event)">
    <ng-template pTemplate="header">
        <tr>
            <th pSortableColumn="code">Mã role <p-sortIcon field="code" /></th>
            <th pSortableColumn="name">Tên role <p-sortIcon field="name" /></th>
            <th>Quyền</th>
            <th>Hành động</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body"
                 let-role>
        <tr>
            <td>{{ role.code }}</td>
            <td class="items-center">
                <span class="mr-2">{{ role.name }}</span>
                <p-tag *ngIf="role.default"
                        value="Mặc định"
                        severity="secondary" />
            </td>
            <td>{{ role.permissions?.length || 0 }} quyền</td>
            <td>
                <div class="flex gap-2">
                    <button pButton
                            icon="pi pi-pencil"
                            class="p-button-text p-button-sm"
                            (click)="editRole(role)"></button>
                    <button pButton
                            icon="pi pi-clone"
                            class="p-button-text p-button-sm"
                            pTooltip="Duplicate role"
                            (click)="cloneRole(role)"></button>
                    <button pButton
                            [pTooltip]="role.default ? 'Không thể xoá role hệ thống' : ''"
                            icon="pi pi-trash"
                            class="p-button-text p-button-danger p-button-sm"
                            [disabled]="role.default"
                            (click)="deleteRole(role)"></button>
                </div>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="4"
                class="text-center p-4">Chưa có vai trò nào cả.</td>
        </tr>
    </ng-template>
</p-table>

<!-- Role Dialog -->
<p-dialog [(visible)]="roleDialog"
          [style]="{width: '550px'}"
          header="{{ editMode ? 'Edit Role' : (cloneMode ? 'Duplicate Role' : 'Add Role') }}"
          [modal]="true"
          dismissableMask="true"
          styleClass="p-fluid"
          (onShow)="onDialogShow()">
    <ng-template pTemplate="content">
        <form [formGroup]="roleForm">
            <div class="flex flex-col mb-4 gap-2">
                <label for="code" class="block text-sm font-medium text-gray-700">Mã role <span class="text-red-500">*</span></label>
                <input type="text"
                       pInputText
                       [pTooltip]="editMode ? 'Không thể sửa mã role đã có' : ''"
                       id="code"
                       formControlName="code"
                       placeholder="Nhập mã role (chữ thường, số, dấu gạch ngang)" />
                <small class="text-red-500"
                       *ngIf="roleForm.get('code')?.invalid && roleForm.get('code')?.touched">
                    Mã role là bắt buộc và chỉ chứa chữ thường, số, dấu gạch ngang
                </small>
            </div>

            <div class="flex flex-col mb-4 gap-2">
                <label for="name" class="block text-sm font-medium text-gray-700">Tên role <span class="text-red-500">*</span></label>
                <input type="text"
                       pInputText
                       id="name"
                       formControlName="name"
                       placeholder="Nhập tên role" />
                <small class="text-red-500"
                       *ngIf="roleForm.get('name')?.invalid && roleForm.get('name')?.touched">
                    Tên role là bắt buộc
                </small>
            </div>

            <p-divider />

            <div class="flex flex-col mb-4 gap-2">
                <label class="block text-sm font-medium text-gray-700">Quyền được cấp</label>
                <div *ngIf="permissionsLoading"
                     class="flex justify-center p-4">
                    <i class="pi pi-spin pi-spinner text-2xl"></i>
                </div>

                <p-tree #permTree
                        *ngIf="!permissionsLoading"
                        [value]="permissionsTree"
                        selectionMode="checkbox"
                        expanded="true"
                        [propagateSelectionUp]="true"
                        [propagateSelectionDown]="true"
                        styleClass="w-full"
                        [(selection)]="selectedPermissionNodes">
                    <ng-template pTemplate="default"
                                 let-node>
                        <div class="flex items-center"
                             [pTooltip]="isDefaultRole ? 'Vai trò hệ thống, không thể sửa quyền' : ''">
                            <span>{{ node.label }}</span>
                            <span *ngIf="node.data"
                                  class="ml-2 text-sm text-gray-600">{{ node.data }}</span>
                        </div>
                    </ng-template>
                </p-tree>
            </div>
        </form>
    </ng-template>

    <ng-template pTemplate="footer">
        <p-button defaultCancelButton
                  (onClick)="roleDialog = false" />
        <p-button defaultConfirmButton
                  (onClick)="updateRole()"
                  type="submit"
                  [disabled]="roleForm.invalid" />
    </ng-template>
</p-dialog>

<p-confirmDialog [style]="{width: '450px'}" dismissableMask="true"/>