<p-table [value]="data" [scrollable]="true" scrollHeight="400px" responsiveLayout="scroll">
    <ng-template #header>
        <tr>
            <th pSortableColumn="eventDate">
                <div class="flex items-center gap-1">
                    eventDate
                    <p-sortIcon field="eventDate"/>
                </div>
            </th>
            <th pSortableColumn="testType">
                <div class="flex items-center gap-1">
                    testType
                    <p-sortIcon field="testType"/>
                </div>
            </th>
            <th pSortableColumn="courseCodeLevel">
                <div class="flex items-center gap-1">
                    courseCodeLevel
                    <p-sortIcon field="courseCodeLevel"/>
                </div>
            </th>
            <th pSortableColumn="test">
                <div class="flex items-center gap-1">
                    test
                    <p-sortIcon field="test"/>
                </div>
            </th>
            <th pSortableColumn="testCode">
                <div class="flex items-center gap-1">
                    testCode
                    <p-sortIcon field="testCode"/>
                </div>
            </th>
            <th pSortableColumn="learnerCode">
                <div class="flex items-center gap-1">
                    learnerCode
                    <p-sortIcon field="learnerCode"/>
                </div>
            </th>
            <th pSortableColumn="rScore">
                <div class="flex items-center gap-1">
                    rScore
                    <p-sortIcon field="rScore"/>
                </div>
            </th>
            <th pSortableColumn="lScore">
                <div class="flex items-center gap-1">
                    lScore
                    <p-sortIcon field="lScore"/>
                </div>

            </th>
            <th class="min-w-[10px]" pSortableColumn="graWriScore">
                <div class="flex items-center gap-1">
                    graWriScore
                    <p-sortIcon field="graWriScore"/>
                </div>

            </th>
            <th pSortableColumn="pronSpkScore">
                <div class="flex items-center gap-1">pronSpkScore
                    <p-sortIcon field="pronSpkScore"/>
                </div>

            </th>
            <th pSortableColumn="total">
                <div class="flex items-center gap-1">total
                    <p-sortIcon field="total"/>
                </div>

            </th>
            <th pSortableColumn="midTotal">
                <div class="flex items-center gap-1">midTotal
                    <p-sortIcon field="midTotal"/>
                </div>

            </th>
            <th pSortableColumn="courseTotal">
                <div class="flex items-center gap-1">courseTotal
                    <p-sortIcon field="courseTotal"/>
                </div>

            </th>
            <th pSortableColumn="outcome">
                <div class="flex items-center gap-1">outcome
                    <p-sortIcon field="outcome"/>
                </div>

            </th>
            <th style="min-width: 15rem" alignFrozen="right" pSortableColumn="error" pFrozenColumn [frozen]="true">
                error
                <p-sortIcon field="error"/>
            </th>
        </tr>
    </ng-template>
    <ng-template #body let-childItem>
        <tr>
            <td>
                {{ childItem.eventDate }}
            </td>
            <td>
                {{ childItem.testType }}
            </td>
            <td>
                {{ childItem.courseCodeLevel }}
            </td>
            <td>
                {{ childItem.test }}
            </td>
            <td>
                {{ childItem.testCode }}
            </td>
            <td>
                {{ childItem.learnerCode }}
            </td>
            <td>
                {{ childItem.rScore }}
            </td>
            <td>
                {{ childItem.lScore }}
            </td>
            <td>
                {{ childItem.graWriScore }}
            </td>
            <td>
                {{ childItem.pronSpkScore }}
            </td>
            <td>
                {{ childItem.total }}
            </td>
            <td>
                {{ childItem.midTotal }}
            </td>
            <td>
                {{ childItem.courseTotal }}
            </td>
            <td>
                {{ childItem.outcome }}
            </td>
            <td style="min-width: 20rem" alignFrozen="right" pFrozenColumn [frozen]="true">
                {{ childItem.error }}
            </td>
        </tr>
    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="numberOfCols">Không có dữ liệu</td>
        </tr>
    </ng-template>
</p-table>