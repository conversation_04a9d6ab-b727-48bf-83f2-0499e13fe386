<div class="card">
    <p-toolbar styleClass="mb-4 !border-none">
        <ng-template #start>
            <div class="flex items-center flex-wrap gap-2">
                <p-datepicker class="w-72"
                              styleClass="w-full"
                              showIcon
                              [iconDisplay]="'input'"
                              placeholder="Từ ngày - Đến ngày"
                              selectionMode="range"
                              [showButtonBar]="true"
                              [dateFormat]="dateFormatSelect"
                              [(ngModel)]="filterDates"
                              [maxDate]="filterToDate"
                              (ngModelChange)="fetchData()"
                              [showClear]="true" />
                <p-multiSelect class="w-80"
                               [options]="syncTypeOptions"
                               [maxSelectedLabels]="2"
                               [(ngModel)]="filterTypes"
                               [showClear]="true"
                               [showHeader]="false"
                               display="chip"
                               placeholder="Loại"
                               (onPanelHide)="fetchData()"
                               (onClear)="onClearSyncTypeSelected()">
                </p-multiSelect>
                <p-button styleClass="h-10"
                          label="Reset"
                          (onClick)="resetFilter()" />
            </div>
        </ng-template>

        <ng-template #end>
            <ng-container *ngIf="canEdit">
                <p-menu #menu
                        [model]="actionItems"
                        [popup]="true" />
                <p-button styleClass="h-10"
                          (click)="menu.toggle($event)"
                          icon="pi pi-sync"
                          label="Đồng bộ" />
            </ng-container>
        </ng-template>
    </p-toolbar>

    <p-table #dt
             [loading]="isLoadingTable"
             dataKey="id"
             [value]="listData"
             [rowHover]="true"
             [scrollable]="true"
             [lazy]="true"
             [lazyLoadOnInit]="false"
             (onLazyLoad)="onLazyLoad($event)"
             [paginator]="true"
             [rows]="itemPerPage"
             [totalRecords]="totalItems"
             [rowsPerPageOptions]="itemPerPageOptions"
             [showCurrentPageReport]="true"
             currentPageReportTemplate="{first} - {last}/{totalRecords}"
             rowExpandMode="single">
        <ng-template #header>
            <tr>
                <th style="width: 5%"></th>
                <th style="width: 20%">Ngày</th>
                <th style="width: 20%">Loại</th>
                <th style="width: 45%">Lỗi chung</th>
                <th style="width: 10%">Số lượng</th>
            </tr>
        </ng-template>
        <ng-template #body
                     let-item
                     let-expanded="expanded">
            <tr>
                <td>
                    <button type="button"
                            pButton
                            pRipple
                            [pRowToggler]="item"
                            text
                            rounded
                            plain
                            class="mr-2"
                            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"></button>
                </td>
                <td>{{ item.date | date: dateFormatTable }}</td>
                <td>{{ item.type }}</td>
                <td>{{ item.message }}</td>
                <td>{{ item.total }}</td>
            </tr>
        </ng-template>
        <ng-template #expandedrow
                     let-item>
            <tr>
                <td colspan="5"
                    style="max-width: 100px">
                    <div class="p-3">
                        <ng-container [ngSwitch]="item.type">
                            <ng-container *ngSwitchCase="syncType.LEARNER_EVENT">
                                <result-learner-event [data]="item.data"></result-learner-event>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.COURSE">
                                <result-pre [data]="item.data"></result-pre>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.TEST_RESULTS_CE">
                                <result-ce [data]="item.data"></result-ce>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.TEST_RESULTS_INTER_ABOVE">
                                <result-inter [data]="item.data"></result-inter>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.TEST_RESULTS_PRE">
                                <result-pre [data]="item.data"></result-pre>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.MOODLE_USER">
                                <result-moodle [data]="item.data"></result-moodle>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.MOODLE_COHORT">
                                <result-moodle [data]="item.data"></result-moodle>
                            </ng-container>
                            <ng-container *ngSwitchCase="syncType.MOODLE_GROUP_USER">
                                <result-moodle [data]="item.data"></result-moodle>
                            </ng-container>
                        </ng-container>
                    </div>
                </td>
            </tr>
        </ng-template>
    </p-table>
</div>