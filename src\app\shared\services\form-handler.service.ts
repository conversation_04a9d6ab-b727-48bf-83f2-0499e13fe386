import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';

// Import existing interfaces from their original locations
import { DialogHandlerConfig } from '@shared/components/edit-create-dialog';
import { PopoverHandlerConfig } from '@shared/components/generic-popover';

// Internal types for the service
type UnifiedHandlerConfig = DialogHandlerConfig | PopoverHandlerConfig;

interface FormOperationContext {
  config: UnifiedHandlerConfig;
  formValue: any;
  editMode?: boolean; // Only relevant for dialog operations
}

interface FormHandlerResult {
  success: boolean;
  error?: any;
}

// Type guards to determine configuration type
function isDialogHandlerConfig(config: UnifiedHandlerConfig): config is DialogHandlerConfig {
  return 'createMethod' in config && 'updateMethod' in config;
}

function isPopoverHandlerConfig(config: UnifiedHandlerConfig): config is PopoverHandlerConfig {
  return 'method' in config && !('createMethod' in config);
}



@Injectable({
  providedIn: 'root'
})
export class FormHandlerService {

  constructor(
    private toastService: ToastService,
    private errorHandlerService: ErrorHandlerService
  ) { }

  /**
   * Unified save handler that works with both dialog and popover configurations
   * @param context The operation context containing config, formValue, and editMode
   * @returns Promise that resolves when operation completes
   */
  async handleSave(context: FormOperationContext): Promise<FormHandlerResult> {
    try {
      const { config, formValue, editMode } = context;

      if (!config) {
        throw new Error('Handler configuration is required');
      }

      if (isDialogHandlerConfig(config)) {
        return this.handleDialogSaveInternal(config, formValue, editMode || false);
      } else if (isPopoverHandlerConfig(config)) {
        return this.handlePopoverSaveInternal(config, formValue);
      } else {
        throw new Error('Invalid handler configuration type');
      }
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Unified cancel handler that works with both dialog and popover configurations
   * @param config The handler configuration
   */
  handleCancel(config: UnifiedHandlerConfig): void {
    if (config?.onCancel) {
      config.onCancel();
    }
  }

  /**
   * Handle dialog-specific save operations (create/update)
   */
  private async handleDialogSaveInternal(
    handlerConfig: DialogHandlerConfig,
    formValue: any,
    editMode: boolean
  ): Promise<FormHandlerResult> {
    try {
      let serviceCall: Observable<any>;
      let successMessage: string;
      let transformedData: any;

      // Apply commonDataTransform first if it exists
      const commonTransformed = handlerConfig.commonDataTransform ? handlerConfig.commonDataTransform(formValue) : formValue;
      // Then apply specific transform (create or update) on top of common transform
      const specificTransformFunction = editMode ? handlerConfig.updateDataTransform : handlerConfig.createDataTransform;
      transformedData = specificTransformFunction
        ? { ...commonTransformed, ...specificTransformFunction(formValue) }
        : commonTransformed;

      // Call the service method
      const methodName = editMode ? handlerConfig.updateMethod : handlerConfig.createMethod;
      const serviceMethod = handlerConfig.service[methodName];
      if (!serviceMethod) {
        return { success: false, error: `Method ${methodName} not found in service` };
      }
      serviceCall = serviceMethod.call(handlerConfig.service, transformedData);
      successMessage = this.getDefaultDialogSuccessMessage(handlerConfig.entityLabel, editMode);

      return this.executeServiceCall(serviceCall, successMessage, handlerConfig);
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Handle popover-specific save operations (single operation)
   */
  private async handlePopoverSaveInternal(
    handlerConfig: PopoverHandlerConfig,
    formValue: any
  ): Promise<FormHandlerResult> {
    try {
      // Transform data if transformation function is provided
      const transformedData = handlerConfig.dataTransform ? handlerConfig.dataTransform(formValue) : formValue;

      // Get the service method
      const serviceMethod = handlerConfig.service[handlerConfig.method];
      if (!serviceMethod) {
        return { success: false, error: `Method ${handlerConfig.method} not found on service` };
      }

      // Call the service method
      const serviceCall: Observable<any> = serviceMethod.call(handlerConfig.service, transformedData);
      const successMessage = handlerConfig.successMessage || this.getDefaultSuccessMessage(handlerConfig.entityLabel);

      return this.executeServiceCall(serviceCall, successMessage, handlerConfig);
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Execute the service call with unified error handling and success logic
   */
  private async executeServiceCall(
    serviceCall: Observable<any>,
    successMessage: string,
    handlerConfig: UnifiedHandlerConfig
  ): Promise<FormHandlerResult> {
    return new Promise((resolve) => {
      serviceCall.subscribe({
        next: (response) => {
          const { hasError } = this.errorHandlerService.handleInternal(response);
          if (!hasError) {
            this.toastService.showSuccess(successMessage);
            if (handlerConfig.onSuccess) {
              handlerConfig.onSuccess();
            }
            resolve({ success: true });
          } else {
            resolve({ success: false, error: response });
          }
        },
        error: (error) => {
          resolve({ success: false, error });
        }
      });
    });
  }



  // === PRIVATE MESSAGE GENERATION METHODS ===
  /**
   * Generate default success/error message based on entity label (for dialog operations)
   */
  private getDefaultDialogSuccessMessage(entityLabel?: string, isUpdate: boolean = false): string {
    const action = isUpdate ? 'Cập nhật' : 'Tạo';
    return entityLabel ? `${action} ${entityLabel} thành công` : `${action} thành công`
  }
  /**
   * Generate default create success message based on entity label
   */
  private getDefaultCreateMessage(entityLabel?: string): string {
    if (!entityLabel) {
      return 'Tạo thành công';
    }
    return `Tạo ${entityLabel} thành công`;
  }

  /**
   * Generate default update success message based on entity label
   */
  private getDefaultUpdateMessage(entityLabel?: string): string {
    if (!entityLabel) {
      return 'Cập nhật thành công';
    }
    return `Cập nhật ${entityLabel} thành công`;
  }

  /**
   * Generate default success message based on entity label (for popover operations)
   */
  private getDefaultSuccessMessage(entityLabel?: string): string {
    if (!entityLabel) {
      return 'Thao tác thành công';
    }
    return `Thao tác ${entityLabel} thành công`;
  }


}
