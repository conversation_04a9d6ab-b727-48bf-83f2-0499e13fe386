import { EVENTS } from '@app/constants';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DateHelper } from '@app/shared/helpers';
import { EmployeePositionRepository, EmployeeRepository, PositionRepository, UserRepository } from '@database/mongodb/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';
import _ from 'lodash';

@Injectable()
export class EmployeeListener {
    private logger = new Logger(EmployeeListener.name);
    constructor(
        private employeePositionRepository: EmployeePositionRepository,
        private userRepository: UserRepository,
        private employeeRepository: EmployeeRepository,
        private positionRepository: PositionRepository,
    ) {}

    @OnEvent([EVENTS.PROBATION_UPDATED, EVENTS.EMPLOYEE_POSITION_UPDATED])
    async handleUpdateStatusEmpPos(data: { employeePositionId: string }) {
        this.logger.log(`Handle UpdateStatusEmpPos ${JSON.stringify(data)}`);
        const { employeePositionId } = data;
        const employeePosition = await this.employeePositionRepository.onChangeStatus(employeePositionId);
        this.handleUpdateStatusEmp({ employeeId: employeePosition.employeeId });
    }

    @OnEvent(EVENTS.EMPLOYEE_POSITION_STATUS_CHANGED)
    async handleUpdateStatusEmp(data: { employeeId: string }) {
        this.logger.log(`Handle UpdateStatusEmp - ${JSON.stringify(data)}`);
        const { employeeId } = data;
        // Update status employee latest
        await this.employeeRepository.onChangeStatus(employeeId);
        await this.handleUpdatePermisionEmp({ employeeId });
    }

    @OnEvent(EVENTS.EMPLOYEE_POSITION_ROLE_CHANGED)
    async handleUpdatePermisionEmp(data: { employeeId: string }) {
        this.logger.log(`Handle UpdatePermisionEmp - ${JSON.stringify(data)}`);
        const { employeeId } = data;
        const today = DateHelper.getStartOfDate();
        // Get all available postion of employeeId
        const availablePostions = await this.employeePositionRepository.findAll({
            employeeId,
            fromDate: { $lte: today },
            $or: [{ toDate: null }, { toDate: { $gte: today } }],
        });
        // Update permission for user
        let permissions = [];
        let permissionsKey = null;
        if (!ArrayHelper.isEmpty(availablePostions)) {
            const roles = await this.positionRepository.getRolesByPostionIds(availablePostions.map(i => i.positionId));
            permissions = _.uniq(_.flatMap(roles, 'permissions'));
            permissionsKey = ArrayHelper.buildArrayKey(permissions);
        }
        await this.userRepository.updateOne({ id: employeeId }, { permissions, permissionsKey });
    }
}
