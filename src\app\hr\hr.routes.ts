import { Routes } from '@angular/router';
import { PERMISSIONS } from '@shared/constants';
import { PermissionGuard } from '../core/guards/permission.guard';
import { AppLayout } from '../layout/app.layout';
import { EmployeesComponent } from './employees/employees.component';
import { EmployeesListComponent } from './employees/employees-list/employees-list.component';
import { EmployeesPositionsComponent } from './employees/employees-positions/employees-positions.component';
import { EmployeesDeptComponent } from './employees/employees-dept/employees-dept.component';
import { ProbationComponent } from './employees/probation/probation.component';
import { PositionsComponent } from './positions/positions.component';

export default [
    {
        path: '',
        component: AppLayout,
        children: [
            { path: '', redirectTo: 'employees', pathMatch: 'full' },
            {
                path: 'employees',
                component: EmployeesComponent,
                children: [
                    { path: '', redirectTo: 'list', pathMatch: 'full' },
                    {
                        path: 'list',
                        component: EmployeesListComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.EMPLOYEE_VIEW.code] }
                    },
                    {
                        path: 'positions',
                        component: EmployeesPositionsComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.EMPLOYEE_VIEW.code] }
                    },
                    {
                        path: 'departments',
                        component: EmployeesDeptComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.EMPLOYEE_VIEW.code] }
                    },
                    {
                        path: 'probation',
                        component: ProbationComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.PROBATION_VIEW.code] }
                    }
                ]
            },
            {
                path: 'positions',
                component: PositionsComponent,
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.POSITION_VIEW.code] }
            }
        ]
    }
] as Routes;
