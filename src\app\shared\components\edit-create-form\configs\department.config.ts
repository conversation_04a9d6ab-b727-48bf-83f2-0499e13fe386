import { inject } from '@angular/core';
import { Validators } from '@angular/forms';
import { FormConfig } from '../edit-create-form.interfaces';
import { DepartmentApiService } from '@shared/services';
import { DEPARTMENT_TREE_SELECT_CONFIG } from '@shared/components/generic-treeselect/generic-treeselect.configs';

export const departmentFormConfig = (): FormConfig => {
  const departmentApiService = inject(DepartmentApiService);

  return {
    fields: [
      {
        key: 'id', label: 'ID', type: 'text', width: 'full', hidden: true
      },
      {
        key: 'code', label: 'Mã phòng ban', required: true, width: 'half',
        type: 'text', validators: [Validators.pattern(/^[a-z0-9-]+$/)],
        placeholder: 'Nhập mã phòng ban',
        tooltip: (editMode: boolean, formValue: any) =>
          editMode && formValue.code ? 'Không thể sửa mã phòng ban đã có' : '',
        customErrorMessage: 'Mã phòng ban là bắt buộc và chỉ chứa chữ thường, số, dấu gạch ngang',
        disabled: (editMode: boolean, formValue: any) => editMode && formValue.code
      },
      {
        key: 'name', label: 'Tên phòng ban', required: true, width: 'half',
        type: 'text', placeholder: 'Nhập tên phòng ban'
      },
      {
        key: 'parentId', label: 'Phòng ban cha', width: 'full',
        type: 'treeselect', config: { ...DEPARTMENT_TREE_SELECT_CONFIG, service: departmentApiService }
      }
    ]
  };
};
