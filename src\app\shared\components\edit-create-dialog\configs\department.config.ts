import { inject } from '@angular/core';
import { DepartmentApiService } from '@shared/services';
import { DialogHandlerConfig, EditCreateDialogConfig } from '../edit-create-dialog.interfaces';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { departmentFormConfig } from '@shared/components/edit-create-form';

export const departmentDialogConfig = (onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig => {
  const departmentApiService = inject(DepartmentApiService);

  return {
    width: '550px',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    formConfig: departmentFormConfig(),
    actions: [defaultCancelAction, defaultConfirmAction],
    handlerConfig: {
      service: departmentApiService,
      createMethod: 'createDepartment',
      updateMethod: 'updateDepartment',
      entityLabel: 'phòng ban',
      commonDataTransform: (formValue: any) => ({
        code: formValue.code,
        name: formValue.name,
        parentId: formValue.parentId
      }),
      updateDataTransform: (formValue: any) => ({
        id: formValue.id
      }),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id,
      }),
      onSuccess,
      onCancel
    } as DialogHandlerConfig
  };
};
