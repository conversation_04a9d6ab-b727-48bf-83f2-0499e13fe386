import { Routes } from '@angular/router';
import { FULL_ROUTES, ROUTES } from '@shared/constants';
import { UserType } from '@shared/enums/user-type.enum';
import { UserTypeGuard } from './core/guards/user-type.guard';
import { LandingRedirect } from './core/redirects/landing-redirect';
import { Noaccess } from './shared/pages/noaccess/noaccess';
import { Notfound } from './shared/pages/notfound/notfound';

export const appRoutes: Routes = [
    { path: ROUTES.ROOT, component: LandingRedirect },
    { path: ROUTES.AUTH, loadChildren: () => import('./auth/auth.routes') },
    {
        path: ROUTES.ADMIN,
        loadChildren: () => import('./admin/admin.routes'),
        canActivate: [UserTypeGuard],
        data: { userTypes: [UserType.EMPLOYEE] }
    },
    {
        path: ROUTES.LEARNER,
        loadChildren: () => import('./learner/learner.routes'),
        canActivate: [UserTypeGuard],
        data: { userTypes: [UserType.LEARNER] }
    },
    {
        path: ROUTES.LEARNERS,
        loadChildren: () => import('./learners/learners.routes'),
        canActivate: [UserTypeGuard],
        data: { userTypes: [UserType.EMPLOYEE] }
    },
    {
        path: ROUTES.HR,
        loadChildren: () => import('./hr/hr.routes'),
        canActivate: [UserTypeGuard],
        data: { userTypes: [UserType.EMPLOYEE] }
    },
    {
        path: ROUTES.SYSTEM,
        loadChildren: () => import('./system/system.routes'),
        canActivate: [UserTypeGuard],
        data: { userTypes: [UserType.EMPLOYEE] }
    },
    { path: ROUTES.NOTFOUND, component: Notfound },
    { path: ROUTES.NOACCESS, component: Noaccess },
    { path: '**', redirectTo: FULL_ROUTES.NOTFOUND },
];
