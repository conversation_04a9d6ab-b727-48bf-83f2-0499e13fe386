import { SyncType } from '@app/enums/sync-type.enum';
import { StringHelper } from '@app/shared/helpers';
import { BcError } from '@errors/error-base';
import { errorCode } from '@errors/error-message';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { sheets } from '@googleapis/sheets';
import { JWT } from 'google-auth-library';

@Injectable()
export class GoogleSheetsService {
    private readonly logger = new Logger(GoogleSheetsService.name);
    private readonly defineSheet = {
        [SyncType.LEARNER_EVENT]: { id: '10gwoYMrtugBOkz1Fa83uoNuvtxQcOLlSPTUKWvwYIS0', colNumber: 12 },
        [SyncType.LEARNER]: { id: '10gwoYMrtugBOkz1Fa83uoNuvtxQcOLlSPTUKWvwYIS0', colNumber: 12 },
        [SyncType.COURSE]: { id: '1amZap4GvvC59bBxnL7nSmZo562zSX01sui6Vr2PRhKs', colNumber: 3 },
        [SyncType.TEST_RESULTS_INTER_ABOVE]: { id: '1LsHkje0LDE5Q9rB9QotxLkfNPHgKZEGVozT-Mt-ZrXM', colNumber: 23 },
        [SyncType.TEST_RESULTS_PRE]: { id: '1LsHkje0LDE5Q9rB9QotxLkfNPHgKZEGVozT-Mt-ZrXM', colNumber: 23 },
        [SyncType.TEST_RESULTS_CE]: { id: '1LsHkje0LDE5Q9rB9QotxLkfNPHgKZEGVozT-Mt-ZrXM', colNumber: 20 },
    };
    private sheetsClient;

    constructor(private readonly configService: ConfigService) {
        const auth = new JWT(this.configService.get('GOOGLE_CLIENT_EMAIL'), undefined, this.configService.get('GOOGLE_PRIVATE_KEY')?.replace(/\\n/g, '\n'), [
            'https://www.googleapis.com/auth/spreadsheets',
        ]);
        this.sheetsClient = sheets({ version: 'v4', auth });
    }

    
    async readSheet(type: SyncType) {
        if (!type) return;
        let { id, colNumber } = this.defineSheet[type];
        let rangeName: string = type.toString();
        return await this.readRows(id, rangeName, colNumber);
    }

    async readRows(sheetId: string, range: string, colNumber: number) {
        try {
            const spreadsheetId = sheetId;
            const response = await this.sheetsClient.spreadsheets.values.get({
                spreadsheetId,
                range,
            });
            const paddedValues = response.data.values.map(row => {
                while (row.length < colNumber) {
                    row.push(StringHelper.EMPTY);
                }
                return row;
            });
            return paddedValues;
        } catch (error) {
            this.logger.error('Has error', error);
            throw new BcError(errorCode.GOOGLE_SHEET_ERR, error?.message);
        }
    }
}
