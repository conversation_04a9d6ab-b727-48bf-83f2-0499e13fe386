import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { LearnerEventModel } from '../schemas';
import { LearnerEventType } from '@app/enums';
import { StringHelper } from '@app/shared/helpers';

@Injectable()
export class LearnerEventRepository extends GenericRepository<LearnerEventModel> {
    private readonly context = LearnerEventRepository.name;

    constructor(
        @Inject(MONGO_CONST.LEARNER_EVENT_COLLECTION)
        private readonly learnerEventModel: Model<LearnerEventModel>,
    ) {
        super(learnerEventModel);
    }

    async getLeanerEvents(learnerCode: string) {
        return await this.learnerEventModel.aggregate([
            {
                $match: { learnerCode },
            },
            {
                $addFields: {
                    formatCourse: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$testType', 'INDEPENDENT'] },
                                    then: {
                                        $concat: [
                                            'INDEPENDENT',
                                            StringHelper.HYPHEN,
                                            {
                                                $dateToString: {
                                                    format: '%Y%m%d',
                                                    date: '$eventDate',
                                                },
                                            },
                                            StringHelper.HYPHEN,
                                            '$level',
                                        ],
                                    },
                                },
                                {
                                    case: {
                                        $and: [
                                            {
                                                $eq: ['$eventType', LearnerEventType.RESERVED],
                                            },
                                            {
                                                $ne: ['$level', null],
                                            },
                                        ],
                                    },
                                    then: {
                                        $concat: [
                                            LearnerEventType.RESERVED,
                                            StringHelper.HYPHEN,
                                            {
                                                $dateToString: {
                                                    format: '%Y%m%d',
                                                    date: '$eventDate',
                                                },
                                            },
                                            StringHelper.HYPHEN,
                                            '$level',
                                        ],
                                    },
                                },
                            ],
                            default: '$courseCode',
                        },
                    },
                    sortDate: {
                        $cond: {
                            if: {
                                $and: [
                                    {
                                        $eq: ['$eventtype', LearnerEventType.RESERVED],
                                    },
                                    {
                                        $eq: ['$eventDate', null],
                                    },
                                ],
                            },
                            then: '$reservedFromDate',
                            else: '$eventDate',
                        },
                    },
                },
            },
            {
                $sort: { sortDate: 1 },
            },
            {
                $group: {
                    _id: { key: '$formatCourse', courseCode: '$courseCode' },
                    details: {
                        $push: {
                            eventType: '$eventType',
                            eventDate: '$eventDate',
                            reservedFromDate: '$reservedFromDate',
                            reservedToDate: '$reservedToDate',
                            test: '$test',
                            testType: '$testType',
                            outcome: '$outcome',
                            level: '$level',
                            testCode: '$testCode',
                            result: '$result',
                            note: '$note',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    courseCode: '$_id.key',
                    details: 1,
                    firstDetail: { $arrayElemAt: ['$details', 0] },
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.COURSE_COLLECTION,
                    localField: 'courseCode',
                    foreignField: 'code',
                    as: 'courseInfo',
                },
            },
            {
                $unwind: {
                    path: '$courseInfo',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $project: {
                    _id: 0,
                    courseName: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$firstDetail.testType', 'INDEPENDENT'] },
                                    then: 'Thi độc lập',
                                },
                                {
                                    case: {
                                        $and: [
                                            {
                                                $eq: ['$firstDetail.eventType', LearnerEventType.RESERVED],
                                            },
                                            {
                                                $ne: ['$firstDetail.level', null],
                                            },
                                        ],
                                    },
                                    then: 'Bảo lưu',
                                },
                            ],
                            default: '$courseInfo.name',
                        },
                    },
                    firstDetail: 1,
                    courseInfo: 1,
                    courseLevel: {
                        $switch: {
                            branches: [
                                {
                                    case: { $eq: ['$firstDetail.testType', 'INDEPENDENT'] },
                                    then: '$firstDetail.level',
                                },
                                {
                                    case: {
                                        $and: [
                                            {
                                                $eq: ['$firstDetail.eventType', LearnerEventType.RESERVED],
                                            },
                                            {
                                                $ne: ['$firstDetail.level', null],
                                            },
                                        ],
                                    },
                                    then: '$firstDetail.level',
                                },
                            ],
                            default: '$courseInfo.level',
                        },
                    },
                    courseCode: 1,
                    details: 1,
                    createdCourse: {
                        $cond: {
                            if: {
                                $ne: ['$firstDetail', null],
                            },
                            then: '$firstDetail.eventDate',
                            else: null,
                        },
                    },
                },
            },
            {
                $sort: { createdCourse: -1 },
            },
        ]);
    }

    async findAllEventsLatestByEventTypes(eventTypes: LearnerEventType[]): Promise<{ learnerCode: string; courseCode: string; eventType: string }[]> {
        return await this.learnerEventModel.aggregate([
            {
                $match: {
                    eventType: {
                        $in: eventTypes,
                    },
                },
            },
            {
                $addFields: {
                    sortDate: {
                        $cond: {
                            if: { $eq: ['$eventDate', null] },
                            then: '$reservedFromDate',
                            else: '$eventDate',
                        },
                    },
                },
            },
            {
                $sort: {
                    sortDate: -1,
                },
            },
            {
                $group: {
                    _id: {
                        learnerCode: '$learnerCode',
                        courseCode: '$courseCode',
                    },
                    events: { $push: '$$ROOT' },
                },
            },
            {
                $project: {
                    learnerCode: '$_id.learnerCode',
                    courseCode: '$_id.courseCode',
                    eventLatest: {
                        $arrayElemAt: ['$events', 0],
                    },
                },
            },
            {
                $project: {
                    learnerCode: 1,
                    courseCode: 1,
                    eventType: '$eventLatest.eventType',
                },
            },
        ]);
    }
}
