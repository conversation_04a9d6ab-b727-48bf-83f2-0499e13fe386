import { Component } from '@angular/core';
import { PageConstant, SUCCESS_MESSAGE } from '@shared/constants';
import { SYNC_TYPE_OPTIONS, SyncType } from '@shared/enums';
import { PERMISSIONS } from '@shared/constants';
import { DateHelper } from '@shared/helpers';
import { AuthService, ErrorHandlerService, SyncApiService, ToastService } from '@shared/services';
import { PaginationRequest } from '@shared/services';
import { ISort, SORT_VAL } from '@shared/services';
import { NgxSpinnerService } from 'ngx-spinner';
import { TableLazyLoadEvent } from 'primeng/table';
import { finalize, tap } from 'rxjs';

@Component({
    standalone: false,
    templateUrl: './sync-errors.component.html'
})
export class SyncErrorsComponent {
    dateFormatTable = DateHelper.DATE_TIME_FORMAT;
    dateFormatSelect = DateHelper.DATE_FORMAT_DISPLAY_SELECT;
    syncType = SyncType;
    syncTypeOptions = SYNC_TYPE_OPTIONS;
    now = new Date();

    filterFromDate: Date | null = new Date(new Date().setHours(0, 0, 0));
    filterToDate = null;
    filterDates = [this.filterFromDate, this.filterToDate];
    filterTypes = [];
    listData: any[] = [];

    // Sort
    sort: ISort = {
        field: 'date',
        order: -1
    };

    // Pagination
    itemPerPage: number = 10;
    offset: number = 0;
    itemPerPageOptions = PageConstant.ITEM_PER_PAGE_OPTIONS;
    totalItems = 0;
    isLoadingTable = false;

    expandedRows = {};

    actionItems = [
        {
            label: 'Tất cả',
            command: () => {
                this.syncData(0);
            }
        },
        {
            label: 'LogBook',
            command: () => {
                this.syncData(1);
            }
        },
        {
            label: 'Learner',
            command: () => {
                this.syncData(2);
            }
        },
        {
            label: 'Learner event',
            command: () => {
                this.syncData(3);
            }
        },
        {
            label: 'Moodle learner group',
            command: () => {
                this.syncData(4);
            }
        }
    ];

    canEdit = false;

    constructor(
        private toastService: ToastService,
        private spinner: NgxSpinnerService,
        private syncApiService: SyncApiService,
        private errorHandlerService: ErrorHandlerService,
        private authService: AuthService
    ) {
        this.canEdit = this.authService.hasPermission(PERMISSIONS.SYNC_EDIT.code);
    }

    ngOnInit() {
        this.fetchData();
    }

    resetFilter() {
        this.filterDates = [];
        this.filterTypes = [];
        this.fetchData();
    }

    onClearSyncTypeSelected() {
        this.filterTypes = [];
        this.fetchData();
    }

    fetchData() {
        const body: any = {
            syncTypes: this.filterTypes,
            fromDate: this.filterDates?.[0] ?? null,
            toDate: this.filterDates?.[1] ?? null
        };
        const sortStr = `${this.sort.field},${SORT_VAL[`${this.sort?.order}`]}`;
        const params: PaginationRequest = {
            limit: this.itemPerPage,
            offset: this.offset,
            sort: sortStr,
            query: undefined
        };
        this.syncApiService.getAllErrors(params, body).subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.listData = data?.items;
                this.totalItems = data?.total;
            }
        });
    }

    onLazyLoad(data: TableLazyLoadEvent) {
        this.offset = data.first || 0;
        this.itemPerPage = data.rows || 0;
        this.sort = { field: data.sortField, order: data.sortOrder || 1 };
        this.fetchData();
    }

    syncData(type: number) {
        this.spinner.show();
        let request: any;
        switch (type) {
            case 0: {
                request = this.syncApiService.syncAll();
                break;
            }
            case 1: {
                request = this.syncApiService.syncCourse();
                break;
            }
            case 2: {
                request = this.syncApiService.syncLearner();
                break;
            }
            case 3: {
                request = this.syncApiService.syncLearnerEvent();
                break;
            }
            case 4: {
                request = this.syncApiService.syncMoodleLearnerGroup();
                break;
            }
        }
        request
            .pipe(
                tap(() => {
                    this.toastService.showSuccess(SUCCESS_MESSAGE.SYNC_DATA);
                    this.fetchData();
                }),
                finalize(() => this.spinner.hide())
            )
            .subscribe();
    }
}
