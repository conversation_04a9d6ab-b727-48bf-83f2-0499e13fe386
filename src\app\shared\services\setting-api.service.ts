import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SettingType } from '@shared/enums';
import { RequestHelper } from '@shared/helpers';
import { Observable } from 'rxjs';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class SettingApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/settings`;
    }

    getAll(types?: SettingType[]): Observable<any> {
        const params = RequestHelper.createRequestOption({ types });
        return this.httpClient.get<any>(`${this.url}`, {
            params
        });
    }
}
