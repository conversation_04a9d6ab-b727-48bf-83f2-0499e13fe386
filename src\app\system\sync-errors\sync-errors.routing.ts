import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SyncErrorsComponent } from './sync-errors.component';

const ROUTES: Routes = [
    {
        path: '',
        pathMatch: 'full',
        component: SyncErrorsComponent
    }
];

@NgModule({
    imports: [RouterModule.forChild(ROUTES)],
    exports: [RouterModule]
})
export class SyncErrorsRoutingModule {}
