import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { EditCreateDialogComponent, EditCreateDialogConfig, departmentDialogConfig } from '@shared/components/edit-create-dialog';
import { PageConstant } from '@shared/constants';
import { DepartmentApiService, ErrorHandlerService, ToastService } from '@shared/services';
import { ConfirmationService, MessageService, TreeNode } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToolbarModule } from 'primeng/toolbar';
import { TooltipModule } from 'primeng/tooltip';
import { TreeTableModule } from 'primeng/treetable';

@Component({
    selector: 'app-departments',
    standalone: true,
    imports: [
        CommonModule,
        TreeTableModule,
        ButtonModule,
        ConfirmDialogModule,
        TooltipModule,
        ToolbarModule,
        ChipModule,
        EditCreateDialogComponent
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './departments.component.html',
    styleUrl: './departments.component.scss'
})
export class DepartmentsComponent implements OnInit {
    departments: any[] = [];
    departmentsTree: TreeNode[] = [];
    loading: boolean = false;

    // Dialog properties
    departmentDialog: boolean = false;
    editMode: boolean = false;
    selectedDepartment: any = null;
    dialogConfig!: EditCreateDialogConfig;

    // Pagination properties (for display only)
    totalRecords: number = 0;
    itemPerPage: number = PageConstant.ITEM_PER_PAGE;
    itemPerPageOptions: number[] = PageConstant.ITEM_PER_PAGE_OPTIONS;

    constructor(
        private departmentApiService: DepartmentApiService,
        private errorHandlerService: ErrorHandlerService,
        private toastService: ToastService,
        private confirmationService: ConfirmationService
    ) {
        // Load dialog configuration in constructor - synchronous and immediate
        this.dialogConfig = departmentDialogConfig(
            () => this.onDialogSuccess(),
            () => this.onDialogCancel()
        );
    }

    ngOnInit() {
        this.loadDepartments();
    }

    loadDepartments() {
        this.loading = true;

        this.departmentApiService.getAllDepartments().subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.departments = data || [];
                    this.totalRecords = this.departments.length;

                    // Build tree structure
                    this.buildDepartmentTree();
                }
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            }
        });
    }

    buildDepartmentTree() {
        this.departmentsTree = DepartmentsComponent.buildDepartmentTree(this.departments);
    }

    static buildDepartmentTree(departments: any[]): TreeNode[] {
        if (!departments || departments.length === 0) {
            return [];
        }

        // Create a map of departments by id for quick lookup
        const deptMap = new Map<string, TreeNode>();

        // First create all tree nodes
        departments.forEach(dept => {
            const node: TreeNode = {
                key: dept.id,
                data: {
                    id: dept.id,
                    code: dept.code,
                    name: dept.name,
                    parentId: dept.parentId
                },
                label: dept.name,
                children: []
            };
            deptMap.set(dept.id, node);
        });

        // Then build the hierarchy
        const rootNodes: TreeNode[] = [];

        departments.forEach(dept => {
            const currentNode = deptMap.get(dept.id);

            if (currentNode && dept.parentId && deptMap.has(dept.parentId)) {
                // This is a child node, add it to its parent
                const parentNode = deptMap.get(dept.parentId);
                if (parentNode && parentNode.children) {
                    parentNode.children.push(currentNode);
                } else if (parentNode) {
                    parentNode.children = [currentNode];
                }
            } else if (currentNode) {
                // This is a root node
                rootNodes.push(currentNode);
            }
        });

        return rootNodes;
    }



    openNew() {
        this.editMode = false;
        this.selectedDepartment = null;
        this.departmentDialog = true;
    }

    editDepartment(department: any) {
        this.editMode = true;
        this.selectedDepartment = department;
        this.departmentDialog = true;
    }

    deleteDepartment(department: any) {
        const deptCode = department.code || department.data.code;
        const deptName = department.name || department.data.name;

        this.confirmationService.confirm({
            message: `Bạn có chắc chắn muốn xóa phòng ban "${deptName}"?`,
            header: 'Xác nhận',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.departmentApiService.deleteDepartment(deptCode).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Xóa phòng ban thành công');
                            this.loadDepartments();
                        }
                    }
                });
            }
        });
    }

    onDialogSuccess() {
        this.departmentDialog = false;
        this.selectedDepartment = null;
        this.loadDepartments();
    }

    onDialogCancel() {
        this.departmentDialog = false;
        this.selectedDepartment = null;
    }
}
