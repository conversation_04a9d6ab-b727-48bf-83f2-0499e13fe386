import { Validators } from '@angular/forms';
import { FormConfig } from '../edit-create-form.interfaces';
import { AppValidators } from '@shared/validators/app-validators';

export const employeeDialogFormConfig = (): FormConfig => {
  return {
    fields: [
      {
        key: 'code', label: 'Mã nhân viên', required: true, width: 'half',
        type: 'text', validators: [Validators.pattern(/^[A-Z0-9-]+$/)],
        placeholder: 'Ch<PERSON> hoa, số, dấu gạch ngang',
        tooltip: (editMode: boolean, formValue: any) =>
          editMode && formValue.code ? 'Không thể sửa mã nhân viên đã có' : '',
        customErrorMessage: 'Mã nhân viên là bắt buộc và chỉ chứa chữ hoa, số, dấu gạch ngang',
        disabled: (editMode: boolean, formValue: any) => editMode && formValue.code
      },
      {
        key: 'nickname', label: 'Tên gọi', width: 'half', type: 'text', placeholder: 'Nhập tên gọi'
      },
      {
        key: 'fullname', label: 'Họ tên', required: true, width: 'full',
        type: 'text', validators: [AppValidators.name], placeholder: 'Nhập họ tên đầy đủ',
        customErrorMessage: 'Họ tên là bắt buộc; viết hoa chữ cái đầu',
        showDividerAfter: true
      },
      {
        key: 'phone', label: 'Số điện thoại', width: 'half',
        type: 'text', validators: [AppValidators.vnPhone], placeholder: 'Nhập số điện thoại',
        customErrorMessage: 'Số điện thoại không hợp lệ - nếu có ? tức là dư số',
        autoFormat: 'vnPhone'
      },
      {
        key: 'birthday', label: 'Ngày sinh', width: 'half', type: 'datepicker', placeholder: 'Chọn ngày sinh'
      },
      {
        key: 'email', label: 'Email', width: 'half',
        type: 'text', validators: [Validators.email], placeholder: 'Nhập địa chỉ email',
        customErrorMessage: 'Email không hợp lệ'
      },
      {
        key: 'identification', label: 'CMND/CCCD', width: 'half',
        type: 'text', validators: [AppValidators.ssn], placeholder: 'Nhập số CMND/CCCD',
        customErrorMessage: 'Số CMND/CCCD không hợp lệ'
      },
      {
        key: 'note', label: 'Ghi chú', width: 'full', type: 'textarea', rows: 3, placeholder: 'Nhập ghi chú'
      }
    ]
  };
};
