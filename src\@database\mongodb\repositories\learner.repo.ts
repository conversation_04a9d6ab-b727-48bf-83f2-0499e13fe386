import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { LearnerDocument } from '../schemas';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>per, StringHelper } from '@app/shared/helpers';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { SearchOptionsDTO } from '@app/models/dto';

@Injectable()
export class LearnerRepository extends GenericRepository<LearnerDocument> {
    private readonly logger = new Logger(LearnerRepository.name);

    constructor(
        @Inject(MONGO_CONST.LEARNER_COLLECTION)
        private readonly learnerModel: Model<LearnerDocument>,
    ) {
        super(learnerModel);
    }

    async findExistingCodes(codes: string[]): Promise<string[]> {
        const result = await this.learnerModel.aggregate([
            {
                $match: { code: { $in: codes } },
            },
            {
                $project: {
                    _id: 0,
                    code: 1,
                },
            },
            {
                $group: {
                    _id: null,
                    existedCodes: { $push: '$code' },
                },
            },
        ]);
        return ArrayHelper.isEmpty(result) ? [] : result[0].existedCodes;
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const courseCode = andQ?.courseCode || null;
        const fullname = orQ?.fullname || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        }
        if (!StringHelper.isEmpty(courseCode)) {
            matchQ['events'] = {
                $elemMatch: {
                    courseCode,
                },
            };
        }
        if (!StringHelper.isEmpty(fullname)) {
            matchQ['$or'] = [{ 'profile.fullname': { $regex: fullname, $options: 'i' } }];
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.learnerModel.aggregate([
            { $lookup: { from: MONGO_CONST.LEARNER_EVENT_COLLECTION, localField: 'code', foreignField: 'learnerCode', as: 'events' } },
            { $lookup: { from: MONGO_CONST.PROFILE_COLLECTION, localField: 'id', foreignField: 'id', as: 'profile' } },
            { $unwind: '$profile' },
            {
                $match: matchQ,
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    fullname: '$profile.fullname',
                    ec: 1,
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [
                        ...(sort && Object.keys(sort).length > 0 ? [{ $sort: sort }] : []),
                        { $skip: offset },
                        { $limit: limit }
                    ],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }
}
