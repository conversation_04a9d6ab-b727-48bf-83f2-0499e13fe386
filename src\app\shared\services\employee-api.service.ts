import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequestHelper } from '@shared/helpers/request.helper';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class EmployeeApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/employees`;
    }

    getEmployees(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(`${this.url}/with-profile`, { params: options });
    }

    getEmployeeOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }

    createEmployee(employee: any): Observable<any> {
        return this.httpClient.post<any>(this.url, employee);
    }

    updateEmployee(employee: any): Observable<any> {
        return this.httpClient.put<any>(this.url, employee);
    }

    deleteEmployee(code: string): Observable<any> {
        return this.httpClient.delete<any>(`${this.url}/${code}`);
    }

    getEmployeeById(id: string): Observable<any> {
        return this.httpClient.get<any>(`${this.url}/${id}`);
    }
}
