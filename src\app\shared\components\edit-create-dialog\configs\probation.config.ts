import { inject } from '@angular/core';
import { DialogHandlerConfig, EditCreateDialogConfig } from '../edit-create-dialog.interfaces';
import { ProbationApiService } from '@shared/services';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { probationDialogFormConfig } from '@shared/components/edit-create-form';

export const probationDialogConfig = (onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig => {
  const probationApiService = inject(ProbationApiService);

  const commonDataTransform = (formValue: any) => ({
    employeePositionId: formValue.employeePositionId,
    toDate: formValue.toDate,
    deadline: formValue.deadline
  });

  return {
    width: '550px',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    formConfig: probationDialogFormConfig(),
    actions: [defaultCancelAction, defaultConfirmAction],
    handlerConfig: {
      service: probationApiService,
      createMethod: 'createProbation',
      updateMethod: 'updateProbation',
      entityLabel: 'thử việc',
      createDataTransform: (formValue: any) => commonDataTransform(formValue),
      updateDataTransform: (formValue: any) => commonDataTransform(formValue),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id,
        employeeId: rawData.employeePosition.employeeId,
        ...commonDataTransform(rawData),
        toDate: rawData.toDate ? new Date(rawData.toDate) : null,
        deadline: rawData.deadline ? new Date(rawData.deadline) : null
      }),
      onSuccess,
      onCancel
    } as DialogHandlerConfig
  };
};
