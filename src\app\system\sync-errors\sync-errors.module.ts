import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { DialogService } from 'primeng/dynamicdialog';
import { MultiSelectModule } from 'primeng/multiselect';
import { Ripple } from 'primeng/ripple';
import { SplitButtonModule } from 'primeng/splitbutton';
import { TableModule } from 'primeng/table';
import { CeTableComponent } from './containers/ce-table/ce-table.component';
import { SyncErrorsComponent } from './sync-errors.component';
import { SyncErrorsRoutingModule } from './sync-errors.routing';
import { InterTableComponent } from './containers/inter-table/inter-table.component';
import { PreTableComponent } from './containers/pre-table/pre-table.component';
import { LearnerEventTableComponent } from './containers/learner-event-table/learner-event-table.component';
import { MoodleTableComponent } from './containers/moodle-table/moodle-table.component';
import { DatePickerModule } from 'primeng/datepicker';
import { FormsModule } from '@angular/forms';
import { ToolbarModule } from 'primeng/toolbar';
import { Menu } from 'primeng/menu';

@NgModule({
    imports: [CommonModule, FormsModule, TableModule, ButtonModule, Ripple, SplitButtonModule, SyncErrorsRoutingModule, DatePickerModule, MultiSelectModule, ToolbarModule, Menu],
    declarations: [SyncErrorsComponent, CeTableComponent, InterTableComponent, PreTableComponent, LearnerEventTableComponent, MoodleTableComponent],
    providers: [DialogService]
})
export class SyncErrorsModule {}
