export const MONGO_CONST = {
    CON_CORE_TOKEN: 'mongodb_connection_core_token',

    COURSE_COLLECTION: `courses`,
    LEARNER_COLLECTION: `learners`,
    LEARNER_EVENT_COLLECTION: `learner-events`,
    USER_COLLECTION: `users`,
    SETTING_COLLECTION: `settings`,
    SYNC_ERROR_DATA_COLLECTION: `sync-error-data`,
    CENTER_LOCATION_COLLECTION: `center-locations`,
    PARTNER_COLLECTION: `partners`,
    DEPARTMENT_COLLECTION: `departments`,
    POSITION_COLLECTION: `positions`,
    EMPLOYEE_POSITION_COLLECTION: `employee-positions`,
    EMPLOYEE_POSITION_HISTORY_COLLECTION: `employee-position-histories`,

    PERMISSION_COLLECTION: `permissions`,
    ROLE_COLLECTION: `roles`,
    PROFILE_COLLECTION: `profiles`,
    EMPLOYEE_COLLECTION: `employees`,
    PROBATION_COLLECTION: `probations`,
};
export class SchemaConfig {
    public static readonly ToObject = {
        virtuals: true,
        versionKey: false,
        transform: (doc, ret) => {
            delete ret._id;
        },
    };
    public static readonly ToJSON = {
        virtuals: true,
        versionKey: false,
        transform: (doc, ret) => {
            delete ret._id;
        },
    };
}
