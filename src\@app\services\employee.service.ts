import { Logged } from '@app/decorators';
import { UserType } from '@app/enums';
import { CreateEmployeeDTO, JwtPayloadDTO, ProfileDTO, SearchEmployeeDTO, SearchOptionsDTO, UpdateEmployeeDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { TeleBotService } from '@app/shared/services';
import { EmployeeRepository } from '@database/mongodb/repositories';
import { EntityExistedError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { ProfileService } from './profile.service';
import { UserService } from './user.service';
import { v4 } from 'uuid';

@Injectable()
export class EmployeeService {
    private readonly logger = new Logger(EmployeeService.name);

    constructor(
        private employeeRepository: EmployeeRepository,
        private userService: UserService,
        private profileService: ProfileService,
        private teleBotService: TeleBotService,
    ) {}

    async searchEmployees(request: PaginationRequest, body: SearchEmployeeDTO) {
        return await this.employeeRepository.searchEmployees(request, body);
    }

    async getEmployeeOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.employeeRepository.getOptions(request, body);
    }

    async findWithPaginationWithProfile(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.employeeRepository.findWithPaginationWithProfile(request);
    }

    async createEmployee(data: CreateEmployeeDTO, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug('Create employee', JSON.stringify(data));
        const entity = await this.findEmployeeByCode(data.code);
        if (entity) {
            throw new EntityExistedError('Employee code already exists');
        }
        const uuid = v4();
        this.logger.debug(`Create employee with uuid: ${uuid}`);
        const profile = plainToInstance(ProfileDTO, data, {
            excludeExtraneousValues: true,
        });
        try {
            const [userResult, profileResults, employeeResult] = await Promise.all([
                this.userService.createUser(
                    {
                        id: uuid,
                        username: data.code,
                        type: UserType.EMPLOYEE,
                    },
                    logged,
                ),
                this.profileService.createProfiles([{ ...profile, id: uuid, birthday: profile.birthday ? new Date(profile.birthday) : null }], logged),
                this.employeeRepository.create({
                    id: uuid,
                    code: data.code,
                    note: data.note,
                    createdBy: logged.username,
                    updatedBy: logged.username,
                }),
            ]);
            return employeeResult;
        } catch (ex) {
            this.logger.error(`Create employee has error`);
            this.teleBotService.notifyMessage(`Create employee has error ${uuid}: ${JSON.stringify(data)}`);
        }
    }

    async updateEmployee(data: UpdateEmployeeDTO, @Logged() logged: JwtPayloadDTO) {
        const entity = await this.findEmployeeByCode(data.code);
        if (!entity) {
            throw new EntityExistedError('Employee not found');
        }
        const profile = plainToInstance(ProfileDTO, data, {
            excludeExtraneousValues: true,
        });
        try {
            await Promise.all([
                this.profileService.updateProfile(profile, entity.id, logged),
                this.employeeRepository.updateOne(
                    { code: data.code },
                    {
                        note: data.note,
                        updatedBy: logged.username,
                    },
                ),
            ]);
        } catch (ex) {
            this.logger.error(`Update employee has error`);
            this.teleBotService.notifyMessage(`Update employee has error ${entity.id}: ${JSON.stringify(data)}`);
        }
    }

    async deleteEmployee(code: string, @Logged() logged: JwtPayloadDTO) {
        const entity = await this.findEmployeeByCode(code);
        if (!entity) {
            throw new EntityExistedError('Employee not found');
        }
        try {
            await Promise.all([
                this.profileService.deleteProfile(entity.id, logged),
                this.employeeRepository.updateOne({ code }, { deletedAt: new Date(), deletedBy: logged.username }),
            ]);
        } catch (ex) {
            this.logger.error(`Delete employee has error`);
            this.teleBotService.notifyMessage(`Delete employee has error ${entity.id}: ${JSON.stringify(code)}`);
        }
    }

    async findEmployeeByCode(code: string) {
        return this.employeeRepository.findOne({ code: code, deletedAt: null });
    }

    async findOneWithDetails(id: string) {
        return await this.employeeRepository.findOneWithDetails(id);
    }
}
