import { inject } from '@angular/core';
import { EmployeeApiService } from '@shared/services';
import { DialogHandlerConfig, EditCreateDialogConfig } from '../edit-create-dialog.interfaces';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { employeeDialogFormConfig } from '@shared/components/edit-create-form';

export const employeeDialogConfig = (onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig => {
  const employeeApiService = inject(EmployeeApiService);

  return {
    width: '550px',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    formConfig: employeeDialogFormConfig(),
    actions: [defaultCancelAction, defaultConfirmAction],
    handlerConfig: {
      service: employeeApiService,
      createMethod: 'createEmployee',
      updateMethod: 'updateEmployee',
      entityLabel: 'nhân viên',
      commonDataTransform: (formValue: any) => ({
        code: formValue.code?.toUpperCase(),
        fullname: formValue.fullname,
        nickname: formValue.nickname,
        birthday: formValue.birthday,
        identification: formValue.identification,
        phone: formValue.phone,
        email: formValue.email,
        note: formValue.note
      }),
      updateDataTransform: (formValue: any) => ({
        id: formValue.id
      }),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id,
        code: rawData.code,
        fullname: rawData.profile.fullname,
        nickname: rawData.profile.nickname,
        birthday: rawData.profile.birthday ? new Date(rawData.profile.birthday) : null,
        identification: rawData.profile.identification,
        phone: rawData.profile.phone,
        email: rawData.profile.email,
        note: rawData.note
      }),
      onSuccess,
      onCancel
    } as DialogHandlerConfig
  };
};
