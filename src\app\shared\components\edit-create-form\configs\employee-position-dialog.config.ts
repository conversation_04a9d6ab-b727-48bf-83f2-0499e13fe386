import { inject } from '@angular/core';
import { FormConfig } from '../edit-create-form.interfaces';
import { DepartmentApiService, EmployeeApiService, PositionApiService } from '@shared/services';
import { EMPLOYEE_SELECT_CONFIG, POSITION_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';
import { DEPARTMENT_TREE_SELECT_CONFIG } from '@shared/components/generic-treeselect/generic-treeselect.configs';

export const employeePositionDialogFormConfig = (): FormConfig => {
  const employeeApiService = inject(EmployeeApiService);
  const positionApiService = inject(PositionApiService);
  const departmentApiService = inject(DepartmentApiService);

  return {
    fields: [
      {
        key: 'id', label: 'ID', type: 'text', width: 'full', hidden: true
      },
      {
        key: 'employeeId', label: 'Nhân viên', required: true, width: 'full',
        type: 'select', config: { ...EMPLOYEE_SELECT_CONFIG, service: employeeApiService },
        disabled: (editMode) => editMode
      },
      {
        key: 'positionId', label: 'Vị trí', required: true, width: 'half',
        type: 'select', config: { ...POSITION_SELECT_CONFIG, service: positionApiService },
        disabled: (editMode) => editMode
      },
      {
        key: 'departmentId', label: 'Phòng ban', required: true, width: 'half',
        type: 'treeselect', config: { ...DEPARTMENT_TREE_SELECT_CONFIG, service: departmentApiService }
      },
      {
        key: 'fromDate', label: 'Ngày nhận việc', required: true, width: 'full',
        type: 'datepicker', placeholder: 'Chọn ngày nhận việc'
      }
    ]
  };
};
