import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PaginationRequest } from '@shared/services';
import { RequestHelper } from '@shared/helpers/request.helper';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class RoleApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/roles`;
    }

    private get permissionUrl(): string {
        return `${this.environmentService.getCurrentApiUrl()}/permissions`;
    }

    getRoles(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(this.url, { params: options });
    }

    createRole(role: any): Observable<any> {
        return this.httpClient.post<any>(this.url, role);
    }

    updateRole(role: any): Observable<any> {
        return this.httpClient.put<any>(this.url, role);
    }

    deleteRole(id: string): Observable<any> {
        return this.httpClient.delete<any>(`${this.url}/${id}`);
    }

    getPermissions(): Observable<any> {
        return this.httpClient.get<any>(`${this.permissionUrl}/all`);
    }
}
