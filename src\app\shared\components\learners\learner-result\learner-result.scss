
.timeline-item {
    border-left: 4px solid;
    // box-shadow: 5px 5px 10px -5px #cccccc;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 8px;
    transition: all 0.3s ease;

    &-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title-container {
            margin: 0;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
            

            .level-tag {
                padding: 2px 8px;
                border-radius: 8px;
                text-transform: uppercase;
            }
        }

        .time-container {
            width: 13rem;
            font-style: italic;
        }
    }

    &-body {
        margin-top: 10px;
        padding: 5px 20px;
        border-left: 3px solid var(--surface-border);

        .event-item {
            margin-top: .5rem;
            margin-bottom: .5rem;

            .outcome-tag {
                padding: 3px 9px;
                border-radius: 8px;
                font-weight: bold;
            }
        }
    } 
}
