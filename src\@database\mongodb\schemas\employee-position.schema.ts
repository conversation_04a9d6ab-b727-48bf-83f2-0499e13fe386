import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';
import { EmployeePositionStatusEnum } from '@app/enums';

@Schema({ versionKey: false, timestamps: true })
export class EmployeePositionDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ index: true, required: true })
    employeeId: string;
    @Prop({ index: true, required: true })
    positionId: string;
    @Prop({ index: true, required: true })
    fromDate: Date;
    @Prop({ index: true, default: null })
    toDate: Date;
    @Prop({ index: true, required: true })
    departmentId: string;
    @Prop({ index: true, enum: EmployeePositionStatusEnum, required: true })
    status: EmployeePositionStatusEnum;
}
export const EmployeePositionSchema = SchemaFactory.createForClass(EmployeePositionDocument);
