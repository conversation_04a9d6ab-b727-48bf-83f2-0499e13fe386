import { EVENTS } from '@app/constants';
import { Logged } from '@app/decorators';
import { JwtPayloadDTO, ProbationDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { TeleBotService } from '@app/shared/services';
import { EmployeePositionRepository, EmployeeRepository, ProbationRepository } from '@database/mongodb/repositories';
import { BcError, EntityExistedError, NotFoundError, RequestInvalidError } from '@errors/error-base';
import { errorCode } from '@errors/index';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
@Injectable()
export class ProbationService {
    private readonly logger = new Logger(ProbationService.name);

    constructor(
        private probationRepository: ProbationRepository,
        private employeePositionRepository: EmployeePositionRepository,
        private employeeRepository: EmployeeRepository,
        private teleBotService: TeleBotService,
        private eventEmitter: EventEmitter2,
    ) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.probationRepository.findWithPaginationAggregate(request);
    }

    async createProbation(data: ProbationDTO, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug(`Create probation: ${JSON.stringify(data)}`);
        const existingEmployeePosition = await this.employeePositionRepository.findOne({
            id: data.employeePositionId,
            deletedAt: null,
        });
        if (!existingEmployeePosition) {
            throw new NotFoundError('Employee position not found');
        }
        const fromDate = existingEmployeePosition.fromDate;
        if ((data.toDate && !data.deadline) || (!data.toDate && data.deadline)) {
            throw new RequestInvalidError('To date and deadline must co-exist');
        } else if ((data.toDate && data.toDate < fromDate) || data.deadline < fromDate || data.deadline > data.toDate) {
            throw new BcError(errorCode.RANGE_DATE_INVALID);
        }

        const existingProbation = await this.probationRepository.findOne({
            employeePositionId: data.employeePositionId,
            deletedAt: null,
        });
        if (existingProbation) {
            throw new EntityExistedError('Probation already exists for this employee position');
        }
        const entity = { ...data, fromDate, createdBy: logged.username, updatedBy: logged.username };
        const result = await this.probationRepository.create(entity);
        await this.eventEmitter.emitAsync(EVENTS.PROBATION_UPDATED, { employeePositionId: result.employeePositionId });
    }

    async updateProbation(data: ProbationDTO, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug(`Update probation: ${JSON.stringify(data)}`);
        const probations = await this.probationRepository.findAll({ deletedAt: null });
        const index = probations.findIndex(i => i.employeePositionId == data.employeePositionId);
        if (index == -1) {
            throw new NotFoundError('Probation not found for this employee position');
        }
        const result = await this.probationRepository.updateOne(
            { employeePositionId: data.employeePositionId, deletedAt: null },
            {
                ...data,
                updatedBy: logged.username,
            },
        );
        const employeePosition = await this.employeePositionRepository.onChangeStatus(result.employeePositionId);
        await this.eventEmitter.emitAsync(EVENTS.EMPLOYEE_POSITION_STATUS_CHANGED, { employeeId: employeePosition.employeeId });
    }
}
