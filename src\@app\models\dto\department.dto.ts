import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class DepartmentDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toLowerCase())
    readonly code: string;

    @ApiProperty({ description: 'name' })
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @ApiProperty({ description: 'parentId' })
    @IsOptional()
    @IsString()
    readonly parentId: string;
}
