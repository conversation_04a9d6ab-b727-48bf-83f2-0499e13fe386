import { Directive, Input, OnInit, Optional } from '@angular/core';
import { Button } from 'primeng/button';

/**
 * Directive for default cancel button styling and behavior
 * Usage: <p-button defaultCancelButton (onClick)="handleCancel()" />
 */
@Directive({
  selector: '[defaultCancelButton]',
})
export class DefaultCancelButtonDirective implements OnInit {
  @Input() label: string = 'Huỷ';
  @Input() icon: string = 'pi pi-times';
  @Input() severity: 'secondary' | 'success' | 'info' | 'warn' | 'danger' | 'help' | 'primary' | 'contrast' | null | undefined = 'secondary';
  @Input() size?: 'small' | 'large';

  constructor(
    @Optional() private button: Button
  ) {}

  ngOnInit() {
    if (!this.button) {
      console.warn('defaultCancelButton directive used outside of a p-button');
      return;
    }

    // Set default properties if not already set
    if (!this.button.label) {
      this.button.label = this.label;
    }

    if (!this.button.icon) {
      this.button.icon = this.icon;
    }

    if (!this.button.severity) {
      this.button.severity = this.severity;
    }

    if (this.size && !this.button.size) {
      this.button.size = this.size;
    }
  }
}

/**
 * Directive for default confirm/save button styling and behavior
 * Usage: <p-button defaultConfirmButton (onClick)="handleSave()" />
 * Usage: <p-button defaultConfirmButton label="Xác nhận" (onClick)="handleConfirm()" />
 */
@Directive({
  selector: '[defaultConfirmButton]'
})
export class DefaultConfirmButtonDirective implements OnInit {
  @Input() label: string = 'Lưu';
  @Input() icon: string = 'pi pi-check';
  @Input() severity?: 'secondary' | 'success' | 'info' | 'warn' | 'danger' | 'help' | 'primary' | 'contrast' | null;
  @Input() size?: 'small' | 'large';

  constructor(
    @Optional() private button: Button
  ) {}

  ngOnInit() {
    if (!this.button) {
      console.warn('defaultConfirmButton directive used outside of a p-button');
      return;
    }

    // Set default properties if not already set
    if (!this.button.label) {
      this.button.label = this.label;
    }

    if (!this.button.icon) {
      this.button.icon = this.icon;
    }

    if (this.severity && !this.button.severity) {
      this.button.severity = this.severity;
    }

    if (this.size && !this.button.size) {
      this.button.size = this.size;
    }
  }
}
