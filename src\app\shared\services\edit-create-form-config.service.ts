import { Injectable } from '@angular/core';
import { FormConfig, FormEntityType, FormConfigRegistry } from '@shared/components/edit-create-form';

// Module-level variables (shared across all imports of this module)
const formConfigCache = new Map<FormEntityType, FormConfig>();

const formConfigRegistry: FormConfigRegistry = {
  'employeeInfo': () => import('../components/edit-create-form/configs/employee-info.config').then(m => m.employeeInfoFormConfig),
  'employeePosition': () => import('../components/edit-create-form/configs/employee-position.config').then(m => m.employeePositionFormConfig),
  'employeeProbation': () => import('../components/edit-create-form/configs/employee-probation.config').then(m => m.employeeProbationFormConfig),
  'employeePositionDialog': () => import('../components/edit-create-form/configs/employee-position-dialog.config').then(m => m.employeePositionDialogFormConfig),
  'probationDialog': () => import('../components/edit-create-form/configs/probation-dialog.config').then(m => m.probationDialogFormConfig),
  'employeeDialog': () => import('../components/edit-create-form/configs/employee-dialog.config').then(m => m.employeeDialogFormConfig),
  'retireEmployeePosition': () => import('../components/edit-create-form/configs/retire-employee-position.config').then(m => m.retireEmployeePositionFormConfig),
  'position': () => import('../components/edit-create-form/configs/position.config').then(m => m.positionFormConfig),
  // Add more configs here as needed - each will be lazy loaded
};

/**
 * Gets form configuration for a specific entity type with lazy loading
 * @param entityType The type of entity form to create
 * @returns Promise that resolves to complete form configuration
 */
export const getFormConfig = async (entityType: FormEntityType): Promise<FormConfig> => {
  // Check module-level cache first
  if (formConfigCache.has(entityType)) {
    return formConfigCache.get(entityType)!;
  }

  const configFactory = formConfigRegistry[entityType];
  if (!configFactory) {
    throw new Error(`Unsupported entity type: ${entityType}. Available types: ${Object.keys(formConfigRegistry).join(', ')}`);
  }

  // Dynamically import and create the config
  const configFunction = await configFactory();
  const config = configFunction();

  // Cache in module-level variable
  formConfigCache.set(entityType, config);
  return config;
};

/**
 * Optional service class for backward compatibility or DI scenarios
 * Delegates to standalone functions
 */
@Injectable({
  providedIn: 'root'
})
export class EditCreateFormConfigService {
  async getFormConfig(entityType: FormEntityType): Promise<FormConfig> {
    return getFormConfig(entityType);
  }
}
