import { FormConfig } from '@shared/components/edit-create-form';

export interface PopoverHandlerConfig {
  service: any; // The service that handles operations
  method: string; // Method name for the operation (e.g., 'retireEmployeePosition')
  entityLabel?: string; // Entity label for generating default messages (e.g., 'nhân viên', 'lịch sử công tác')
  successMessage?: string; // Success message (optional if entityLabel provided)
  errorMessage?: string; // Error message (optional if entityLabel provided)
  // Data transformation functions
  dataTransform?: (formValue: any) => any;
  initialDataTransform?: (rawData: any) => any; // Transform raw data into form-ready format
  // Callback functions
  onSuccess?: () => void; // Called after successful operation
  onCancel?: () => void; // Called when popover is cancelled
}

export interface PopoverHandlerResult {
  success: boolean;
  error?: any;
}

export interface PopoverAction {
  label?: string;
  icon?: string;
  severity?: "success" | "info" | "warn" | "danger" | "help" | "primary" | "secondary" | "contrast" | null | undefined;
  disabled?: (formValue: any, formValid: boolean) => boolean;
  onClick: string;
  type?: 'button' | 'submit';
  useDefaultStyle?: 'cancel' | 'confirm';
}

export interface PopoverConfig {
  width?: string;
  title?: string;
  dismissable?: boolean;
  styleClass?: string;
  formConfig: FormConfig; // Form configuration object
  actions: PopoverAction[];
  handlerConfig?: PopoverHandlerConfig; // Optional - for automatic operation handling
}

// Type for popover configuration factory functions
export type PopoverConfigFactory = (onSuccess: () => void, onCancel: () => void) => PopoverConfig;

// Type for supported popover entity types
export type PopoverEntityType = 'employeeRetire';
