<p-toolbar styleClass="mb-4 !border-none">
    <ng-template #start>
        <h3 class="text-xl font-semibold">Quản lý phòng ban</h3>
    </ng-template>
    <ng-template #end>
        <p-button pRipple styleClass="h-10" icon="pi pi-plus" label="Thêm phòng ban" (onClick)="openNew()" />
    </ng-template>
</p-toolbar>

<p-treetable
             [value]="departmentsTree"
             [paginator]="true"
             [rows]="itemPerPage"
             [totalRecords]="totalRecords"
             [rowsPerPageOptions]="itemPerPageOptions"
             [showCurrentPageReport]="true"
             currentPageReportTemplate="{first} - {last} of {totalRecords} phòng ban"
             [loading]="loading">
    <ng-template pTemplate="header">
        <tr>
            <th ttSortableColumn="code">Mã phòng ban <p-treeTableSortIcon field="code" /></th>
            <th ttSortableColumn="name">Tên phòng ban <p-treeTableSortIcon field="name" /></th>
            <th>Hành động</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-department let-rowData="rowData" let-expanded="expanded">
        <tr [ttRow]="department">
            <td>
                <p-treetable-toggler [rowNode]="department" />
                {{ rowData.code }}
            </td>
            <td>{{ rowData.name }}</td>
            <td>
                <div class="flex gap-2">
                    <button pButton icon="pi pi-pencil" class="p-button-text p-button-sm" (click)="editDepartment(rowData)"></button>
                    <button pButton icon="pi pi-trash" class="p-button-text p-button-danger p-button-sm" (click)="deleteDepartment(rowData)"></button>
                </div>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="3" class="text-center p-4">Chưa có phòng ban nào.</td>
        </tr>
    </ng-template>
</p-treetable>

<!-- Reusable Edit Create Dialog -->
<edit-create-dialog #editCreateDialog
                    [(visible)]="departmentDialog"
                    [editMode]="editMode"
                    [dialogConfig]="dialogConfig"
                    [initialData]="selectedDepartment">
</edit-create-dialog>

<p-confirmDialog [style]="{width: '450px'}"
                 dismissableMask="true"/>