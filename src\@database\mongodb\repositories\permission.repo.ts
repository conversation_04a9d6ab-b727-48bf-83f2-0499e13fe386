import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from './generic.repo';
import { PermissionDocument } from '../schemas';
import { MONGO_CONST } from '../mongodb.constants';

@Injectable()
export class PermissionRepository extends GenericRepository<PermissionDocument> {
    private readonly context = PermissionRepository.name;

    constructor(
        @Inject(MONGO_CONST.PERMISSION_COLLECTION)
        private readonly permissionModel: Model<PermissionDocument>,
    ) {
        super(permissionModel);
    }

    async getAllPermission() {
        return await this.permissionModel.aggregate([
            {
                $group: {
                    _id: '$scope',
                    permissions: {
                        $push: {
                            code: '$code',
                            description: '$description',
                        },
                    },
                },
            },
            {
                $project: {
                    _id: 0,
                    scope: '$_id',
                    permissions: 1,
                },
            },
            {
                $sort: {
                    scope: 1,
                },
            },
        ]);
    }

    async checkExistPermissions(permissions: string[]): Promise<boolean> {
        const count = await this.permissionModel
            .find({
                code: { $in: permissions },
            })
            .countDocuments()
            .exec();
        return count == permissions.length;
    }
}
