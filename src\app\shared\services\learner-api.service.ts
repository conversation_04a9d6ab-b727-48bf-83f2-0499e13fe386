import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequestHelper } from '@shared/helpers/request.helper';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class LearnerApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/learners`;
    }

    getHistories(): Observable<any> {
        return this.httpClient.get<any>(`${this.environmentService.getCurrentApiUrl()}/learner-events/history`);
    }

    getHistoriesByLearnerCode(learnerCode: string): Observable<any> {
        return this.httpClient.get<any>(`${this.environmentService.getCurrentApiUrl()}/learner-events/history/${learnerCode}`);
    }

    searchLearner(text: string): Observable<any> {
        return this.httpClient.get<any>(`${this.url}/autocomplete?text=${text}`);
    }

    getLearnerOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }
}
