import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { JwtPayloadDTO, PositionDTO, SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { PositionService } from '@app/services';
import { CustomValidationPipe, PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Get, HttpCode, HttpStatus, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard)
@Controller('positions')
export class PositionController {
    constructor(private positionService: PositionService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.POSITION_VIEW.code)
    @Get()
    async getWithPagination(@Query(new PaginationPipe()) request: PaginationRequest) {
        return await this.positionService.findWithPagination(request);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.POSITION_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.positionService.getPositionOptions(request, body);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.POSITION_EDIT.code)
    @Post()
    async createPosition(@Body(new CustomValidationPipe()) dto: PositionDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.positionService.createPosition(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.POSITION_EDIT.code)
    @Put()
    async updatePosition(@Body(new CustomValidationPipe()) dto: PositionDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.positionService.updatePosition(dto, logged);
    }

    // @HttpCode(HttpStatus.OK)
    // @UseGuards(PermissionsGuard)
    // @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    // @Delete()
    // async deletePosition(@Param('code') code: string, @Logged() logged: JwtPayloadDTO) {
    //     return await this.positionService.deletePosition(code, logged);
    // }
}
