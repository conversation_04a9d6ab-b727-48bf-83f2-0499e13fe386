html {
    height: 100%;
    font-size: 14px;
}

body {
    font-family: <PERSON><PERSON>, <PERSON>;
    color: var(--text-color);
    background-color: var(--surface-ground);
    margin: 0;
    padding: 0;
    min-height: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    line-height: 1.4;
}

a {
    text-decoration: none;
}

.layout-wrapper {
    min-height: 100vh;
}

span.p-button-label, .p-button {
    line-height: 1;
}