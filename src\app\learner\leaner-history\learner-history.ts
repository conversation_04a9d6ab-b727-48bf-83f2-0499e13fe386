import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { LearnerResult } from '@shared/components/learners/learner-result/learner-result';
import { DateHelper } from '@shared/helpers';
import { ErrorHandlerService, LearnerApiService } from '@shared/services';
import moment from 'moment';

@Component({
    imports: [CommonModule, LearnerResult],
    templateUrl: './learner-history.html',
    styleUrls: ['./learner-history.scss']
})
export class LearnerHistory {
    listData: any[] = [];

    constructor(
        private learnerApiService: LearnerApiService,
        private errorHandlerService: ErrorHandlerService
    ) {}

    ngOnInit() {
        this.fetchData();
    }

    fetchData() {
        this.listData = [];
        this.learnerApiService.getHistories().subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.listData = [
                    ...data.map((i: any) => {
                        return {
                            ...i,
                            startDate: moment(i.details[0].eventDate).format(DateHelper.DATE_FORMAT),
                            endDate: i.details.length > 1 ? moment(i.details.at(-1).eventDate).format(DateHelper.DATE_FORMAT) : ''
                        };
                    })
                ];
            }
        });
    }
}
