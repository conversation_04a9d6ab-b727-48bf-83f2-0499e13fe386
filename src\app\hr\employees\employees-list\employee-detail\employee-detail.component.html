<div *ngIf="loading" class="flex justify-center items-center h-64">
    <i class="pi pi-spinner pi-spin text-2xl"></i>
</div>

<div *ngIf="!loading && employee" class="flex flex-col gap-4">
    <!-- Employee Basic Info -->
    <p-card header="Thông tin nhân viên">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="font-semibold text-sm text-gray-600">Mã nhân viên:</label>
                <p class="mt-1">{{ employee.code }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Trạng thái:</label>
                <p class="mt-1">
                    <generic-p-tag [value]="employee.status"
                                   type="employeeStatus">
                    </generic-p-tag>
                </p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600"><PERSON><PERSON><PERSON> bắt đầu:</label>
                <p class="mt-1">{{ employee.fromDate ? (employee.fromDate | date:'dd/MM/yyyy') : 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Ngày kết thúc:</label>
                <p class="mt-1">{{ employee.toDate ? (employee.toDate | date:'dd/MM/yyyy') : 'N/A' }}</p>
            </div>
            <div class="col-span-2">
                <label class="font-semibold text-sm text-gray-600">Ghi chú:</label>
                <p class="mt-1">{{ employee.note || 'Không có ghi chú' }}</p>
            </div>
        </div>
    </p-card>

    <!-- Profile Info -->
    <p-card header="Thông tin cá nhân" *ngIf="employee.profile">
        <div class="grid grid-cols-2 gap-4">
            <div>
                <label class="font-semibold text-sm text-gray-600">Họ tên:</label>
                <p class="mt-1">{{ employee.profile.fullname || 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Nickname:</label>
                <p class="mt-1">{{ employee.profile.nickname || 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Ngày sinh:</label>
                <p class="mt-1">{{ employee.profile.birthday ? (employee.profile.birthday | date:'dd/MM/yyyy') : 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Giới tính:</label>
                <p class="mt-1">{{ employee.profile.gender || 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Điện thoại:</label>
                <p class="mt-1">{{ employee.profile.phone || 'N/A' }}</p>
            </div>
            <div>
                <label class="font-semibold text-sm text-gray-600">Email:</label>
                <p class="mt-1">{{ employee.profile.email || 'N/A' }}</p>
            </div>
            <div class="col-span-2">
                <label class="font-semibold text-sm text-gray-600">Địa chỉ:</label>
                <p class="mt-1">{{ employee.profile.address || 'N/A' }}</p>
            </div>
        </div>
    </p-card>

    <!-- Employee Positions -->
    <p-card header="Lịch sử vị trí công việc">
        <p-table [value]="employee.employeePositions || []"
                 dataKey="id"
                 styleClass="p-datatable-sm">
            <ng-template pTemplate="header">
                <tr>
                    <th>Vị trí</th>
                    <th>Phòng ban</th>
                    <th>Ngày bắt đầu</th>
                    <th>Ngày kết thúc</th>
                    <th>Trạng thái</th>
                    <th>Tình trạng thử việc</th>
                    <th>Hành động</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-employeePosition>
                <tr>
                    <td>{{ employeePosition.position?.name || 'N/A' }}</td>
                    <td>{{ employeePosition.department?.name || 'N/A' }}</td>
                    <td>{{ employeePosition.fromDate ? (employeePosition.fromDate | date:'dd/MM/yyyy') : 'N/A' }}</td>
                    <td>{{ employeePosition.toDate ? (employeePosition.toDate | date:'dd/MM/yyyy') : '' }}</td>
                    <td>
                        <generic-p-tag [value]="employeePosition.status"
                                       type="employeePositionStatus">
                        </generic-p-tag>
                    </td>
                    <td>
                        <div class="flex items-center gap-2">
                            <!-- Display probation statuses -->
                            <div class="flex gap-1 flex-wrap" *ngIf="employeePosition.probations && employeePosition.probations.length > 0; else noProbations">
                                <generic-p-tag *ngFor="let probation of employeePosition.probations; let i = index"
                                               [value]="probation.status"
                                               type="probationStatus"
                                               [ngClass]="{'hidden': i >= 2}">
                                </generic-p-tag>
                                <span *ngIf="employeePosition.probations.length > 2" class="text-gray-500 text-sm">
                                    +{{ employeePosition.probations.length - 2 }}
                                </span>
                            </div>
                            <ng-template #noProbations>
                                <span class="text-gray-400 text-sm">Không có</span>
                            </ng-template>

                            <!-- Ellipsis button for probation details -->
                            <button pButton
                                    icon="pi pi-ellipsis-h"
                                    class="p-button-text p-button-sm p-button-rounded"
                                    pTooltip="Xem chi tiết thử việc"
                                    (click)="showProbationPopover(employeePosition, probationPopover, $event)">
                            </button>
                        </div>
                    </td>
                    <td>
                        <div class="flex gap-2">
                            <button pButton
                                    icon="pi pi-user-minus"
                                    class="p-button-text p-button-sm p-button-warning"
                                    pTooltip="Nghỉ việc"
                                    (click)="showRetirePopover(employeePosition, genericRetirePopover, $event)"></button>
                            <button pButton
                                    icon="pi pi-pencil"
                                    class="p-button-text p-button-sm"
                                    pTooltip="Sửa"
                                    (click)="editEmployeePosition(employeePosition)"></button>
                            <button pButton
                                    icon="pi pi-trash"
                                    class="p-button-text p-button-sm p-button-danger"
                                    pTooltip="Xóa"
                                    (click)="deleteEmployeePosition(employeePosition)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="7" class="text-center p-4">Chưa có vị trí công việc nào</td>
                </tr>
            </ng-template>
        </p-table>
    </p-card>
</div>

<div *ngIf="!loading && !employee" class="flex justify-center items-center h-64">
    <p class="text-gray-500">Chọn một nhân viên để xem chi tiết</p>
</div>

<!-- Employee Position Edit Dialog -->
<edit-create-dialog #editCreateDialog
                    [(visible)]="employeePositionDialog"
                    [editMode]="editMode"
                    [dialogConfig]="employeePositionDialogConfig"
                    [initialData]="selectedEmployeePosition"
                    (onSave)="handleEmployeePositionSave($event.formValue, $event.editMode)"
                    (onCancel)="handleEmployeePositionCancel()">
</edit-create-dialog>

<!-- Probation Edit Dialog -->
<edit-create-dialog #probationEditDialog
                    [(visible)]="probationDialog"
                    [editMode]="probationEditMode"
                    [dialogConfig]="probationDialogConfig"
                    [initialData]="selectedProbation"
                    (onSave)="handleProbationSave($event.formValue, $event.editMode)"
                    (onCancel)="handleProbationCancel()">
</edit-create-dialog>

<!-- Generic Retire Employee Popover -->
<generic-popover #genericRetirePopover
                 [popoverConfig]="retirePopoverConfig"
                 [selectedData]="selectedEmployeePositionForRetire"
                 (onHide)="hideRetirePopover()">
</generic-popover>

<!-- Probation Details Popover -->
<p-popover [style]="{width: '800px'}"
           [dismissable]="true"
           (onHide)="hideProbationPopover()"
           #probationPopover>
    <ng-template pTemplate="content">
        <div class="p-4">
            <div class="flex justify-between items-center mb-3">
                <h4 class="text-lg font-semibold">Thông tin thử việc</h4>
                <p-button pRipple
                          styleClass="h-10"
                          icon="pi pi-plus"
                          label="Thêm mới"
                          (onClick)="addNewProbation(); probationPopover.hide()" />
            </div>

            <p-table [value]="selectedEmployeePositionForProbation?.probations || []"
                     styleClass="p-datatable-sm">
                <ng-template pTemplate="header">
                    <tr>
                        <th>Trạng thái</th>
                        <th>Ngày bắt đầu</th>
                        <th>Ngày kết thúc</th>
                        <th>Hạn chót</th>
                        <th>Thủ công</th>
                        <th>Hành động</th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-probation>
                    <tr>
                        <td>
                            <generic-p-tag [value]="probation.status"
                                           type="probationStatus">
                            </generic-p-tag>
                        </td>
                        <td>{{ probation.fromDate ? (probation.fromDate | date:'dd/MM/yyyy') : 'N/A' }}</td>
                        <td>{{ probation.toDate ? (probation.toDate | date:'dd/MM/yyyy') : 'N/A' }}</td>
                        <td>{{ probation.deadline ? (probation.deadline | date:'dd/MM/yyyy') : 'N/A' }}</td>
                        <td>
                            <i class="pi" [ngClass]="probation.isManual ? 'pi-check text-green-500' : 'pi-times text-red-500'"></i>
                        </td>
                        <td>
                            <div class="flex gap-2">
                                <button pButton
                                        icon="pi pi-pencil"
                                        class="p-button-text p-button-sm"
                                        pTooltip="Sửa"
                                        (click)="editProbation(probation); probationPopover.hide()"></button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td colspan="6" class="text-center p-4">Không có thông tin thử việc</td>
                    </tr>
                </ng-template>
            </p-table>
        </div>
    </ng-template>
</p-popover>