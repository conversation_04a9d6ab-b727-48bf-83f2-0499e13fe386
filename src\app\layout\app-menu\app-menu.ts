import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PERMISSIONS } from '@shared/constants';
import { UserType } from '@shared/enums/user-type.enum';
import { AuthService } from '@shared/services';
import { AppMenuitem } from '../app-menuitem/app-menuitem';

@Component({
    selector: 'app-menu',
    standalone: true,
    imports: [CommonModule, AppMenuitem, RouterModule],
    templateUrl: './app-menu.html'
})
export class AppMenu {
    menuItems: any[] = [];

    // Define menu items with required permissions
    learnersMenu = {
        label: 'Học viên',
        items: [
            {
                label: 'Tra cứu học viên',
                icon: 'pi pi-book',
                routerLink: ['/learners/learner-lookup'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.LEARNER_EVENT_VIEW.code
            }
        ]
    };

    hrMenu = {
        label: 'Nhân sự',
        items: [
            {
                label: 'Nhân viên',
                icon: 'pi pi-users',
                routerLink: ['/hr/employees'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.EMPLOYEE_VIEW.code
            },
            {
                label: 'Vị trí',
                icon: 'pi pi-briefcase',
                routerLink: ['/hr/positions'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.POSITION_VIEW.code
            }
        ]
    };

    systemMenu = {
        label: 'Hệ thống',
        items: [
            {
                label: 'Log lỗi',
                icon: 'pi pi-list',
                routerLink: ['/system/sync-errors'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.SYNC_VIEW.code
            },
            {
                label: 'Cài đặt',
                icon: 'pi pi-cog',
                routerLink: ['/system/settings'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.ROLE_VIEW.code
            },
            {
                label: 'Playground',
                icon: 'pi pi-wrench',
                routerLink: ['/system/playground'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.SYNC_VIEW.code
            }
        ]
    };

    learnerMenu = {
        label: 'Học viên',
        items: [
            {
                label: 'Quá trình học',
                icon: 'pi pi-list',
                routerLink: ['/learner/histories'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.LEARNER_EVENT_VIEW.code
            },
            {
                label: 'Thông tin liên hệ',
                icon: 'pi pi-users',
                routerLink: ['/learner/about'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.LEARNER_EVENT_VIEW.code
            },
            {
                label: 'Danh sách trung tâm',
                icon: 'pi pi-book',
                routerLink: ['/learner/centers'],
                routerLinkActiveOptions: { exact: false },
                permission: PERMISSIONS.LEARNER_EVENT_VIEW.code
            }
        ]
    };

    constructor(private authService: AuthService) { }

    ngOnInit() {
        const userType = this.authService.getUserType();

        if (userType === UserType.EMPLOYEE) {
            // Filter learners menu items based on specific permissions
            const filteredLearnersItems = this.learnersMenu.items.filter((item: any) =>
                this.authService.hasPermission(item.permission)
            );

            if (filteredLearnersItems.length > 0) {
                this.menuItems.push({
                    label: this.learnersMenu.label,
                    items: filteredLearnersItems
                });
            }

            // Filter HR menu items based on specific permissions
            const filteredHrItems = this.hrMenu.items.filter((item: any) =>
                this.authService.hasPermission(item.permission)
            );

            if (filteredHrItems.length > 0) {
                this.menuItems.push({
                    label: this.hrMenu.label,
                    items: filteredHrItems
                });
            }

            // Filter system menu items based on specific permissions (playground uses sync permission in lieu of admin permission)
            const filteredSystemItems = this.systemMenu.items.filter((item: any) =>
                this.authService.hasPermission(item.permission)
            );

            if (filteredSystemItems.length > 0) {
                this.menuItems.push({
                    label: this.systemMenu.label,
                    items: filteredSystemItems
                });
            }

            
        } else if (userType === UserType.LEARNER) {
            // Add learner menu if user has learner access
            this.menuItems.push(this.learnerMenu);
        }
    }
}
