import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { CreateEmployeeDTO, JwtPayloadDTO, SearchEmployeeDTO, SearchOptionsDTO, UpdateEmployeeDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { EmployeeService } from '@app/services/employee.service';
import { CustomValidationPipe, PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard)
@Controller('employees')
export class EmployeeController {
    constructor(private employeeService: EmployeeService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Get('with-profile')
    async getWithPaginationWithProfile(@Query(new PaginationPipe()) request: PaginationRequest) {
        return await this.employeeService.findWithPaginationWithProfile(request);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.employeeService.getEmployeeOptions(request, body);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Get(':id')
    async getOneWithDetails(@Param('id') id: string) {
        return await this.employeeService.findOneWithDetails(id);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Post('search')
    async searchWithPaginationWithProfile(@Query(new PaginationPipe()) request: PaginationRequest, @Body(new CustomValidationPipe()) body: SearchEmployeeDTO) {
        return await this.employeeService.searchEmployees(request, body);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    @Post()
    async createEmployee(@Body(new CustomValidationPipe()) dto: UpdateEmployeeDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.employeeService.createEmployee(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    @Put()
    async updateEmployee(@Body(new CustomValidationPipe()) dto: CreateEmployeeDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.employeeService.updateEmployee(dto, logged);
    }
}
