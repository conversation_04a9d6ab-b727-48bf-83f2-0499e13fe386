# Portal Frontend

A modern, component-driven Angular application built with reusable components, type safety, and comprehensive documentation.

## 🌟 **Key Features**

- **🧩 Generic Components** - Reusable, configurable components for rapid development
- **🔧 Configuration-Driven** - Behavior controlled through type-safe configuration objects
- **📚 Comprehensive Documentation** - Extensive documentation with examples and patterns
- **🧪 Well-Tested** - Unit and integration tests with high coverage
- **🚀 Performance Optimized** - OnPush change detection and lazy loading
- **💪 TypeScript First** - Strong typing throughout the application

## 📚 **Documentation**

### **📖 [Complete Documentation Hub](./docs/README.md)**
Comprehensive documentation covering all aspects of the application:

- **🏗️ [Architecture & Setup](./docs/architecture/project-overview.md)** - Project overview and getting started
- **🧩 [Components](./docs/components/README.md)** - Reusable component library
- **🔧 [Services](./docs/services/README.md)** - Configuration and handler services
- **📋 [Examples & Patterns](./docs/examples/README.md)** - Real-world usage examples
- **🧪 [Testing](./docs/testing/testing-guidelines.md)** - Testing strategies and examples

### **🚀 Quick Navigation**
- **[Interactive Documentation Viewer](./docs/viewer.html)** - Browse docs with visual interface
- **[Task-Based Navigation](./docs/navigation.md)** - Find docs by what you want to do
- **[Complete File Index](./docs/index.md)** - Auto-generated documentation index

## 🧩 **Core Components**

### **Edit-Create Dialog System**
Generic CRUD dialog with dynamic form generation:
```typescript
// Simple usage
this.dialogConfig = this.configService.getDialogConfig('employee');
this.dialogVisible = true;
```
**[📖 Full Documentation](./docs/components/edit-create-dialog.md)**

### **Generic Stepper Component**
Multi-step workflows with form integration:
```typescript
// Multi-step process
this.stepperConfig = this.configService.getStepperConfig('employee-creation');
this.stepperVisible = true;
```
**[📖 Full Documentation](./docs/components/generic-stepper.md)**

### **Generic P-Table Component**
Configurable data tables with lazy loading:
```typescript
// Data table with actions
this.tableConfig = this.configService.getTableConfig('employee');
```
**[📖 Full Documentation](./docs/components/generic-p-table.md)**

### **Form Handler Service**
Unified form submission handling:
```typescript
// Unified save handling
const result = await this.formHandler.handleSave({
  config: handlerConfig,
  formValue: data,
  editMode: false
});
```
**[📖 Full Documentation](./docs/services/form-handler.md)**

## 🔧 **Configuration-Driven Architecture**

Components are configured through centralized services:

```typescript
// Entity-based configuration
const dialogConfig = this.configService.getDialogConfig('employee',
  () => this.onSuccess(),
  () => this.onCancel()
);

// Type-safe configuration objects
interface DialogConfig {
  fields: FieldConfig[];
  actions: DialogAction[];
  handlerConfig: HandlerConfig;
}
```

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 16+
- Angular CLI
- npm or yarn

### **Installation**
```bash
# Clone the repository
git clone <repository-url>
cd portal-fe

# Install dependencies
npm install
```

### **Development**
```bash
# Start development server
npm run start

# Run with watch mode
npm run watch

# Run tests
npm run test
```

### **Documentation**
```bash
# Generate documentation index
npm run docs:index

# Serve documentation as website
npm run docs:serve

# Open main documentation
npm run docs:open
```

## 🏗️ **Project Structure**

```
src/
├── app/
│   ├── shared/
│   │   ├── components/     # Reusable components
│   │   │   ├── edit-create-dialog/
│   │   │   ├── generic-stepper/
│   │   │   ├── generic-p-table/
│   │   │   └── generic-popover/
│   │   ├── services/       # Configuration & handler services
│   │   └── interfaces/     # Type definitions
│   ├── features/           # Feature modules
│   └── core/              # Core services
├── docs/                  # Comprehensive documentation
│   ├── components/        # Component documentation
│   ├── services/          # Service documentation
│   ├── examples/          # Usage examples
│   └── architecture/      # Architecture guides
└── scripts/               # Build and utility scripts
```

## 🧪 **Testing**

```bash
# Unit tests
npm run test

# Test with coverage
npm run test:coverage

# E2E tests
npm run e2e

# Watch mode
npm run test:watch
```

**Testing Coverage:**
- ✅ Component unit tests
- ✅ Service integration tests
- ✅ Configuration validation tests
- ✅ E2E workflow tests

## 🔧 **Built With**

- **[Angular](https://angular.io/)** - Frontend framework
- **[PrimeNG](https://primeng.org/)** - UI component library
- **[TypeScript](https://www.typescriptlang.org/)** - Type-safe JavaScript
- **[Jest](https://jestjs.io/)** - Testing framework
- **[RxJS](https://rxjs.dev/)** - Reactive programming

## 📖 **Documentation Features**

- **📚 Comprehensive Guides** - Complete component and service documentation
- **🎯 Task-Based Navigation** - Find docs by what you want to accomplish
- **💻 Interactive Examples** - Copy-paste ready code examples
- **🔍 Auto-Generated Index** - Always up-to-date file listings
- **🌐 Web Viewer** - Beautiful HTML interface for browsing
- **📱 Mobile-Friendly** - Responsive documentation design

## 🤝 **Contributing**

1. **Read the Documentation** - Start with [Project Overview](./docs/architecture/project-overview.md)
2. **Follow Patterns** - Use established [Component Patterns](./docs/examples/common-patterns.md)
3. **Write Tests** - Follow [Testing Guidelines](./docs/testing/testing-guidelines.md)
4. **Update Docs** - Keep documentation current with changes

## 📄 **License**

This project is licensed under the terms specified in the license file.

---

## 🚀 **Get Started**

1. **📖 [Read the Documentation](./docs/README.md)** - Comprehensive guides and examples
2. **🧩 [Explore Components](./docs/components/README.md)** - See what's available
3. **⚡ [Quick Examples](./docs/examples/configuration-examples.md)** - Get up and running fast
4. **🔧 [Configuration Guide](./docs/services/README.md)** - Learn the configuration patterns

**💡 Pro Tip:** Use the [Interactive Documentation Viewer](./docs/viewer.html) for the best browsing experience!
