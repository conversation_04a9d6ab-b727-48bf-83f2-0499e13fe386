import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EnvironmentService } from './environment.service';
import { PaginationRequest } from '@shared/services';
import { RequestHelper } from '@shared/helpers';

@Injectable({ providedIn: 'root' })
export class ProbationApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/probations`;
    }

    getProbations(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(this.url, { params: options });
    }

    createProbation(probation: any): Observable<any> {
        return this.httpClient.post<any>(this.url, probation);
    }

    updateProbation(probation: any): Observable<any> {
        return this.httpClient.put<any>(this.url, probation);
    }
}
