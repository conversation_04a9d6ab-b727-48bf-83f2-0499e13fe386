# Form Handler Service Documentation

## Overview

The `FormHandlerService` is a unified service that provides centralized form submission handling for both dialog and popover components. It abstracts away the complexity of API calls, data transformation, error handling, and success notifications, providing a consistent interface for all form-based operations.

## Key Features

- **Unified Interface** - Single service for both dialog (CRUD) and popover (single action) operations
- **Type Safety** - Strong TypeScript typing with type guards for configuration validation
- **Data Transformation** - Automatic data transformation before API calls
- **Error Handling** - Integrated error handling with toast notifications
- **Success Callbacks** - Configurable success and cancel callbacks
- **Promise-based** - Returns promises for async operation handling
- **Automatic Messages** - Default success messages with entity-specific customization

## Architecture

### Service Dependencies
```mermaid
graph TB
    A[FormHandlerService] --> B[ToastService]
    A --> C[ErrorHandlerService]
    D[Components] --> A
    A --> E[API Services]
```

### Configuration Types
The service works with two main configuration types:
- `DialogHandlerConfig` - For create/update operations (edit-create-dialog)
- `PopoverHandlerConfig` - For single action operations (generic-popover)

## Core Interfaces

### FormOperationContext
```typescript
interface FormOperationContext {
  config: DialogHandlerConfig | PopoverHandlerConfig;
  formValue: any;
  editMode?: boolean; // Only relevant for dialog operations
}
```

### FormHandlerResult
```typescript
interface FormHandlerResult {
  success: boolean;
  error?: any;
}
```

### DialogHandlerConfig
```typescript
interface DialogHandlerConfig {
  service: any;                    // The service that handles CRUD operations
  createMethod: string;            // Method name for creating
  updateMethod: string;            // Method name for updating
  entityLabel?: string;            // Entity label for generating default messages
  createSuccessMessage?: string;   // Custom success message for create
  updateSuccessMessage?: string;   // Custom success message for update
  errorMessage?: string;           // Custom error message
  // Data transformation functions
  createDataTransform?: (formValue: any) => any;
  updateDataTransform?: (formValue: any) => any;
  initialDataTransform?: (rawData: any) => any;
  // Callback functions
  onSuccess?: () => void;          // Called after successful save
  onCancel?: () => void;           // Called when dialog is cancelled
}
```

### PopoverHandlerConfig
```typescript
interface PopoverHandlerConfig {
  service: any;                    // The service that handles the operation
  method: string;                  // Method name to call
  entityLabel?: string;            // Entity label for generating default messages
  successMessage?: string;         // Custom success message
  errorMessage?: string;           // Custom error message
  dataTransform?: (formValue: any) => any; // Data transformation function
  onSuccess?: () => void;          // Called after successful operation
  onCancel?: () => void;           // Called when operation is cancelled
}
```

## Public Methods

### handleSave(context: FormOperationContext): Promise<FormHandlerResult>

The main method for handling form submissions. It automatically determines the operation type based on the configuration and executes the appropriate logic.

**Parameters:**
- `context` - The operation context containing configuration, form data, and mode

**Returns:**
- `Promise<FormHandlerResult>` - Result indicating success/failure

**Usage Examples:**

#### Dialog Operations (Create/Update)
```typescript
// Create operation
const createContext: FormOperationContext = {
  config: {
    service: this.employeeApiService,
    createMethod: 'createEmployee',
    updateMethod: 'updateEmployee',
    entityLabel: 'nhân viên',
    createDataTransform: (formValue) => ({
      code: formValue.code?.toUpperCase(),
      fullname: formValue.fullname,
      email: formValue.email
    }),
    onSuccess: () => {
      this.loadEmployees();
      this.hideDialog();
    }
  },
  formValue: { code: 'emp001', fullname: 'John Doe', email: '<EMAIL>' },
  editMode: false
};

const result = await this.formHandlerService.handleSave(createContext);
if (result.success) {
  console.log('Employee created successfully');
}
```

#### Popover Operations (Single Action)
```typescript
// Single action operation
const popoverContext: FormOperationContext = {
  config: {
    service: this.employeeApiService,
    method: 'retireEmployee',
    entityLabel: 'nhân viên',
    dataTransform: (formValue) => ({
      employeeId: formValue.id,
      retirementDate: formValue.retirementDate,
      reason: formValue.reason
    }),
    onSuccess: () => {
      this.loadEmployees();
      this.hidePopover();
    }
  },
  formValue: { id: 1, retirementDate: '2024-12-31', reason: 'Voluntary retirement' }
};

const result = await this.formHandlerService.handleSave(popoverContext);
```

### handleCancel(config: UnifiedHandlerConfig): void

Handles cancellation operations by executing the configured cancel callback.

**Parameters:**
- `config` - The handler configuration (dialog or popover)

**Usage:**
```typescript
// In component action handler
onClick: () => {
  this.formHandlerService.handleCancel(this.handlerConfig);
}
```

## Internal Implementation

### Type Guards

The service uses type guards to determine the configuration type and execute appropriate logic:

```typescript
function isDialogHandlerConfig(config: UnifiedHandlerConfig): config is DialogHandlerConfig {
  return 'createMethod' in config && 'updateMethod' in config;
}

function isPopoverHandlerConfig(config: UnifiedHandlerConfig): config is PopoverHandlerConfig {
  return 'method' in config && !('createMethod' in config);
}
```

### Operation Flow

#### Dialog Operations
1. **Type Detection** - Determine if it's a dialog configuration
2. **Mode Check** - Check if it's create or update mode
3. **Data Transformation** - Apply appropriate transformation function
4. **Service Call** - Call create or update method
5. **Result Handling** - Handle success/error responses
6. **Notifications** - Show success toast and execute callbacks

#### Popover Operations
1. **Type Detection** - Determine if it's a popover configuration
2. **Data Transformation** - Apply transformation function if provided
3. **Method Validation** - Verify the service method exists
4. **Service Call** - Call the specified method
5. **Result Handling** - Handle success/error responses
6. **Notifications** - Show success toast and execute callbacks

### Error Handling

The service provides comprehensive error handling:

```typescript
// API call errors
serviceCall.subscribe({
  next: (response) => {
    const { hasError } = this.errorHandlerService.handleInternal(response);
    if (!hasError) {
      // Success handling
    } else {
      // Internal error handling
    }
  },
  error: (error) => {
    // Network/HTTP error handling
  }
});
```

## Default Message Generation

The service automatically generates success messages based on entity labels:

### Dialog Messages
- **Create:** `Tạo ${entityLabel} thành công` (fallback: "Tạo thành công")
- **Update:** `Cập nhật ${entityLabel} thành công` (fallback: "Cập nhật thành công")

### Popover Messages
- **Action:** `Thao tác ${entityLabel} thành công` (fallback: "Thao tác thành công")

## Integration Patterns

### With Edit-Create Dialog
```typescript
// In dialog configuration
const handlerConfig: DialogHandlerConfig = {
  service: this.employeeApiService,
  createMethod: 'createEmployee',
  updateMethod: 'updateEmployee',
  entityLabel: 'nhân viên',
  onSuccess: () => this.onDialogSuccess()
};

// In dialog action
onClick: async (formValue: any, editMode: boolean) => {
  const context: FormOperationContext = {
    config: handlerConfig,
    formValue,
    editMode
  };
  await this.formHandlerService.handleSave(context);
}
```

### With Generic Popover
```typescript
// In popover configuration
const handlerConfig: PopoverHandlerConfig = {
  service: this.employeeApiService,
  method: 'retireEmployee',
  entityLabel: 'nhân viên',
  onSuccess: () => this.onPopoverSuccess()
};

// In popover action
onClick: async (formValue: any) => {
  const context: FormOperationContext = {
    config: handlerConfig,
    formValue
  };
  await this.formHandlerService.handleSave(context);
}
```

## Testing

### Unit Test Structure
```typescript
describe('FormHandlerService', () => {
  let service: FormHandlerService;
  let mockToastService: jasmine.SpyObj<ToastService>;
  let mockErrorHandlerService: jasmine.SpyObj<ErrorHandlerService>;

  // Test dialog operations
  describe('Dialog Operations (Create/Update)', () => {
    it('should handle create operation successfully', async () => {
      // Test implementation
    });
  });

  // Test popover operations
  describe('Popover Operations (Single Action)', () => {
    it('should handle popover operation successfully', async () => {
      // Test implementation
    });
  });

  // Test error scenarios
  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      // Test implementation
    });
  });
});
```

### Test Coverage
- ✅ Create operations with data transformation
- ✅ Update operations with data transformation
- ✅ Popover operations with data transformation
- ✅ Error handling for API failures
- ✅ Error handling for internal errors
- ✅ Cancel operation handling
- ✅ Default message generation
- ✅ Type guard validation

## Best Practices

### Configuration Design
1. **Use Entity Labels** - Provide meaningful entity labels for better user messages
2. **Data Transformation** - Always transform data to match API expectations
3. **Success Callbacks** - Implement success callbacks for UI updates
4. **Error Handling** - Let the service handle errors automatically

### Performance Considerations
1. **Promise-based** - Use async/await for better error handling
2. **Type Safety** - Leverage TypeScript for compile-time validation
3. **Memory Management** - Service handles subscription cleanup automatically

### Error Recovery
1. **Graceful Degradation** - Service continues operation even with partial failures
2. **User Feedback** - Automatic toast notifications for all operations
3. **Debugging Support** - Detailed error information in returned results

## Migration Guide

### From Direct API Calls
```typescript
// Before: Direct API call
this.employeeApiService.createEmployee(formValue).subscribe({
  next: (response) => {
    this.toastService.showSuccess('Employee created');
    this.loadEmployees();
  },
  error: (error) => {
    // Handle error
  }
});

// After: Using FormHandlerService
const context: FormOperationContext = {
  config: {
    service: this.employeeApiService,
    createMethod: 'createEmployee',
    entityLabel: 'nhân viên',
    onSuccess: () => this.loadEmployees()
  },
  formValue,
  editMode: false
};
await this.formHandlerService.handleSave(context);
```

## Troubleshooting

### Common Issues

#### Configuration Type Errors
**Problem:** TypeScript errors about configuration types
**Solution:** Ensure configuration objects match the expected interfaces

#### Method Not Found Errors
**Problem:** Service method doesn't exist
**Solution:** Verify method names in service and configuration match

#### Data Transformation Issues
**Problem:** API receives incorrect data format
**Solution:** Implement proper data transformation functions

#### Success Callbacks Not Executing
**Problem:** onSuccess callback not called
**Solution:** Check error handling and ensure API returns success response

---

**Related Documentation:**
- [Edit-Create Dialog System](../components/edit-create-dialog.md)
- [Generic Popover Component](../components/generic-popover.md)
- [Toast Service](./toast.md)
- [Error Handler Service](./error-handler.md)
