import { Routes } from '@angular/router';
import { PERMISSIONS } from '@shared/constants';
import { PermissionGuard } from '../core/guards/permission.guard';
import { AppLayout } from '../layout/app.layout';
import { LearnerLookupComponent } from './learner-lookup/learner-lookup.component';
import { ByLearnerLookupComponent } from './learner-lookup/by-learner/by-learner-lookup.component';
import { ByCourseLookupComponent } from './learner-lookup/by-course/by-course-lookup.component';

export default [
    {
        path: '',
        component: AppLayout,
        children: [
            { path: '', redirectTo: 'learner-lookup', pathMatch: 'full' },
            {
                path: 'learner-lookup',
                component: LearnerLookupComponent,
                children: [
                    { path: '', redirectTo: 'by-learner', pathMatch: 'full' },
                    {
                        path: 'by-learner',
                        component: ByLearnerLookupComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.LEARNER_EVENT_VIEW.code] }
                    },
                    {
                        path: 'by-course',
                        component: ByCourseLookupComponent,
                        canActivate: [PermissionGuard],
                        data: { permissions: [PERMISSIONS.LEARNER_EVENT_VIEW.code] }
                    },
                ]
            }
        ]
    }
] as Routes;
