export enum ProbationStatus {
    IN_PROGRESS = 'IN_PROGRESS',
    RESULT_PENDING = 'RESULT_PENDING',
    PASS = 'PASS',
    FAIL = 'FAIL',
    CANCELLED = 'CANCELLED',
}

export const PROBATION_STATUS_CONFIG = {
    [ProbationStatus.IN_PROGRESS]: {
        label: '<PERSON>ang thử việc',
        cssClass: 'text-sm !bg-blue-100 !text-blue-700'
    },
    [ProbationStatus.RESULT_PENDING]: {
        label: 'Chờ kết quả',
        cssClass: 'text-sm !bg-yellow-100 !text-yellow-700'
    },
    [ProbationStatus.PASS]: {
        label: 'Pass',
        cssClass: 'text-sm !bg-green-100 !text-green-700'
    },
    [ProbationStatus.FAIL]: {
        label: 'Fail',
        cssClass: 'text-sm !bg-red-100 !text-red-700'
    },
    [ProbationStatus.CANCELLED]: {
        label: 'Hủy',
        cssClass: 'text-sm !bg-gray-100 !text-gray-700'
    }
};

// For p-select options - maintains the same structure as before
export const PROBATION_STATUS_OPTIONS = Object.entries(PROBATION_STATUS_CONFIG).map(([key, value]) => ({
    label: value.label,
    value: key,
    cssClass: value.cssClass
}));
