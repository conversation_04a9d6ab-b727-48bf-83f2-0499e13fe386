import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { EditCreateDialogComponent, EditCreateDialogConfig, employeePositionDialogConfig } from '@shared/components/edit-create-dialog';
import {
    GenericPTableComponent,
    TableConfig,
    TableDataLoadEvent,
    mapFunctions,
    employeePositionTableConfig
} from '@shared/components/generic-p-table';
import { GenericPopoverComponent, PopoverConfig, employeeRetirePopoverConfig } from '@shared/components/generic-popover';
import {
    EmployeePositionApiService,
    ErrorHandlerService,
    ToastService,
} from '@shared/services';
import { ConfirmationService, MessageService } from 'primeng/api';

@Component({
    selector: 'app-employees-positions',
    standalone: true,
    imports: [
        CommonModule,
        GenericPTableComponent,
        EditCreateDialogComponent,
        GenericPopoverComponent,
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './employees-positions.component.html',
    styleUrls: ['./employees-positions.component.scss']
})
export class EmployeesPositionsComponent implements OnInit {
    // Table & dialog configuration - will be initialized in constructor
    employeePositionLazyScrollPTableConfig!: TableConfig;
    dialogConfig!: EditCreateDialogConfig;
    retirePopoverConfig!: PopoverConfig;

    // Data properties - now managed by table component
    employeePositions: any[] = [];
    totalRecords: number = 0;

    // Dialog and form properties
    employeePositionDialog: boolean = false;
    editMode: boolean = false;
    selectedEmployeePosition: any = null;
    selectedEmployeePositionForRetire: any = null;

    // ViewChild for popover and table
    @ViewChild('genericRetirePopover') genericRetirePopover!: GenericPopoverComponent;
    @ViewChild(GenericPTableComponent) tableComponent!: GenericPTableComponent;

    constructor(
        private employeePositionApiService: EmployeePositionApiService,
        private errorHandlerService: ErrorHandlerService,
        private toastService: ToastService,
        private confirmationService: ConfirmationService,
    ) {
        // Initialize table configuration using direct import
        this.employeePositionLazyScrollPTableConfig = mapFunctions(employeePositionTableConfig(), {
            OPEN_NEW: () => this.openNew(),
            EDIT: (item: any) => this.editEmployeePosition(item),
            DELETE: (item: any) => this.deleteEmployeePosition(item),
            RETIRE: (item: any, event?: Event) => this.showRetirePopover(item, event)
        });

        // Initialize retire popover configuration using direct import
        this.retirePopoverConfig = employeeRetirePopoverConfig(
            () => this.onPopoverSuccess(),
            () => this.onPopoverCancel()
        );

        // Load dialog configuration in constructor - synchronous and immediate
        this.dialogConfig = employeePositionDialogConfig(
            () => this.onDialogSuccess(),
            () => this.onDialogCancel()
        );
    }

    ngOnInit() {
    }

    // Table functions
    onTableDataLoaded(data: TableDataLoadEvent) {
        this.employeePositions = data.items;
        this.totalRecords = data.totalRecords;
    }

    openNew() {
        this.editMode = false;
        this.selectedEmployeePosition = null;
        this.employeePositionDialog = true;
    }

    editEmployeePosition(employeePosition: any) {
        this.editMode = true;
        this.selectedEmployeePosition = employeePosition;
        this.employeePositionDialog = true;
    }

    deleteEmployeePosition(employeePosition: any) {
        this.confirmationService.confirm({
            message: 'Bạn có chắc chắn muốn xóa lịch sử công tác này?',
            header: 'Xác nhận',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.employeePositionApiService.deleteEmployeePosition(employeePosition.id).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Xóa lịch sử công tác thành công');
                            this.tableComponent.reload();
                        }
                    }
                });
            }
        });
    }

    showRetirePopover(employeePosition: any, event?: Event) {
        this.selectedEmployeePositionForRetire = employeePosition;
        this.genericRetirePopover.show(event);
    }

    // Edit-create-dialog functions
    onDialogSuccess() {
        this.employeePositionDialog = false;
        this.selectedEmployeePosition = null;
        this.tableComponent.reload();
    }

    onDialogCancel() {
        this.employeePositionDialog = false;
        this.selectedEmployeePosition = null;
    }

    // Popover functions
    onPopoverSuccess() {
        this.genericRetirePopover.hide();
        this.selectedEmployeePositionForRetire = null;
        this.tableComponent.reload();
    }

    onPopoverCancel() {
        this.genericRetirePopover.hide();
        this.selectedEmployeePositionForRetire = null;
    }
}