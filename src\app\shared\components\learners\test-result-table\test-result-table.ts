import { Component, Input, Renderer2 } from '@angular/core';
import { DialogModule } from 'primeng/dialog';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button'

@Component({
    selector: 'test-result-table',
    imports: [CommonModule, DialogModule, ButtonModule],
    templateUrl: './test-result-table.html',
    styleUrls: ['./test-result-table.scss']
})
export class TestResultTable {
    @Input() result: any;
    @Input() note: string = '';
    colNames: string[] = [];
    values: any[] = [];
    pairs: any[] = [];
    hasComment: boolean = true;
    visible: boolean = false;

    colDef: any = {
        rRaw: 'Read (raw)',
        rBand: 'Read (band)',
        lRaw: 'Lis (raw)',
        lBand: 'Lis (band)',
        wT1: 'Wri (T1)',
        wT2: 'Wri (T2)',
        wRaw: 'Wri (raw)',
        wBand: 'Wri (band)',
        sRaw: 'Spk (raw)',
        sBand: 'Spk (band)',
        ovrRaw: 'Overall (raw)',
        ovrRnd: 'Overall (làm tròn)',
        rScore: 'Read',
        lScore: 'Lis',
        graWriScore: 'Gra/Wri',
        pronSpkScore: 'Pronun/Spk',
        total: 'Total',
        midScore: 'Mid score',
        courseTotal: 'Course total'
    };
    constructor(private render: Renderer2) { }

    ngOnInit() {
        if (!this.result) {
            return;
        }
        this.pairs = Object.entries(this.result)
            .filter(([key]) => this.colDef[key])
            .map(([key, value]) => ({
                header: this.colDef[key],
                value: value
            }));
    }

    openPopupComment() {
        setTimeout(() => {
            this.render.removeClass(document.body, 'p-overflow-hidden');
        }, 0);
        this.visible = true;
    }
}
