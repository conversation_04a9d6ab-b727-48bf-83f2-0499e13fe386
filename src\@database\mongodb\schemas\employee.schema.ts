import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';
import { EmployeeStatusEnum } from '@app/enums';

@Schema({ versionKey: false, timestamps: true })
export class EmployeeDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ enum: EmployeeStatusEnum, index: true, default: EmployeeStatusEnum.NO_POSITION })
    status: EmployeeStatusEnum;
    @Prop({ default: null })
    note: string;
    @Prop({ default: null })
    fromDate: Date;
    @Prop({ default: null })
    toDate: Date;
}

export const EmployeeSchema = SchemaFactory.createForClass(EmployeeDocument);
