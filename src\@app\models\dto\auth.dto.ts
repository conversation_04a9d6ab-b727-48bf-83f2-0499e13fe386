import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Validate } from 'class-validator';
import { IsPasswordValid } from './custom-validators';
import { Transform } from 'class-transformer';

export class SignInDTO {
    @ApiProperty({ description: 'username' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toUpperCase())
    readonly username: string;

    @ApiProperty({ description: 'password' })
    @IsString()
    @IsNotEmpty()
    readonly password: string;
}

export class ChangePasswordDTO {
    @ApiProperty({ description: 'oldPassword' })
    @Validate(IsPasswordValid)
    readonly oldPassword: string;

    @ApiProperty({ description: 'newPassword' })
    @Validate(IsPasswordValid)
    readonly newPassword: string;
}

export class ForgotPasswordDTO {
    @ApiProperty({ description: 'username' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toUpperCase())
    readonly username: string;

    @ApiProperty({ description: 'email' })
    @IsEmail()
    @Transform(({ value }) => value?.toLowerCase())
    readonly email: string;
}

export class ResetPasswordDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    readonly code: string;

    @ApiProperty({ description: 'newPassword' })
    @Validate(IsPasswordValid)
    readonly newPassword: string;
}
