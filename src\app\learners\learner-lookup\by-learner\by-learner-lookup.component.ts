import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { LearnerResult } from '@shared/components/learners/learner-result/learner-result';
import { <PERSON><PERSON><PERSON><PERSON>, StringHelper } from '@shared/helpers';
import { LearnerApiService, ErrorHandlerService } from '@shared/services';
import moment from 'moment';
import { AutoCompleteCompleteEvent, AutoCompleteModule } from 'primeng/autocomplete';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { ChipModule } from 'primeng/chip';

@Component({
    selector: 'app-by-learner-lookup',
    imports: [CommonModule, FormsModule, LearnerResult, AutoCompleteModule, InputGroupModule, InputGroupAddonModule, ChipModule],
    templateUrl: './by-learner-lookup.component.html',
    styleUrl: './by-learner-lookup.component.scss',
    host: {
        class: 'flex flex-col w-full'
    }
})
export class ByLearnerLookupComponent {
    suggestions: any[] = [];
    value: any;
    learnerData: any[] = [];

    constructor(
        private learnerApiService: LearnerApiService,
        private errorHandlerService: ErrorHandlerService
    ) { }

    search(event: AutoCompleteCompleteEvent) {
        if (event && StringHelper.isEmpty(event.query)) {
            this.suggestions = [];
            return;
        }
        this.learnerApiService.searchLearner(event.query).subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.suggestions = data.map((i: any) => {
                    return {
                        ...i,
                        label: `${i.code} - ${i.fullname} - ${i.ec}`
                    };
                });
            }
        });
    }

    selectLearner(event: any) {
        if (event && !event.value && StringHelper.isEmpty(event.value.code)) {
            this.learnerData = [];
            return;
        }
        if (Array.isArray(this.value)) {
            this.value = [event.value];
        }
        this.learnerData = [];
        this.learnerApiService.getHistoriesByLearnerCode(event.value.code).subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.learnerData = [
                    ...data.map((i: any) => {
                        return {
                            ...i,
                            startDate: moment(i.details[0].eventDate).format(DateHelper.DATE_FORMAT),
                            endDate: i.details.length > 1 ? moment(i.details.at(-1).eventDate).format(DateHelper.DATE_FORMAT) : ''
                        };
                    })
                ];
            }
        });
    }
}
