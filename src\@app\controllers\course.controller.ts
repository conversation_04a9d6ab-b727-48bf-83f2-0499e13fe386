import { PERMISSIONS } from '@app/constants';
import { Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { CourseService } from '@app/services';
import { PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, HttpCode, HttpStatus, Post, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard, PermissionsGuard)
@Controller('courses')
export class CourseController {
    constructor(private readonly courseService: CourseService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.COURSE_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.courseService.getCourseOptions(request, body);
    }
}
