import { Directive, OnInit } from '@angular/core';
import { DatePicker } from 'primeng/datepicker';

@Directive({
    selector: 'p-datepicker[defaultConfig]'
})
export class DatepickerDefaultsDirective implements OnInit {
    constructor(
        private datePicker: DatePicker
    ) { }

    ngOnInit() {
        // Set default properties if they haven't been explicitly set
        if (this.datePicker.showIcon === undefined) {
            this.datePicker.showIcon = true;
        }

        if (this.datePicker.appendTo === undefined) {
            this.datePicker.appendTo = 'body';
        }

        if (this.datePicker.styleClass === undefined) {
            this.datePicker.styleClass = 'w-full';
        }

        if (this.datePicker.inputStyleClass === undefined) {
            this.datePicker.inputStyleClass = 'w-full';
        }
    }
}
