<!-- Reusable Table Component -->
<generic-p-table [config]="employeePositionLazyScrollPTableConfig"
                    [data]="employeePositions"
                    [totalRecords]="totalRecords"
                    (onDataLoad)="onTableDataLoaded($event)">
</generic-p-table>
<!-- Reusable Edit Create Dialog -->
<edit-create-dialog #editCreateDialog
                    [(visible)]="employeePositionDialog"
                    [editMode]="editMode"
                    [dialogConfig]="dialogConfig"
                    [initialData]="selectedEmployeePosition">
</edit-create-dialog>

<!-- Generic Retire Employee Popover -->
<generic-popover #genericRetirePopover
                 [popoverConfig]="retirePopoverConfig"
                 [selectedData]="selectedEmployeePositionForRetire">
</generic-popover>