# Edit Create Form Config Service - Usage Examples

## Overview

The `EditCreateFormConfigService` provides centralized configuration for edit-create-form components, similar to how `EditCreateDialogConfigService` works for dialogs. This service eliminates code duplication by providing reusable form configurations.

## Basic Usage

### 1. Import the Service

```typescript
import { EditCreateFormConfigService } from '@shared/services';
```

### 2. Component Setup

**Before (Manual Configuration):**
```typescript
export class EmployeesListComponent implements OnInit {
  employeeInfoConfig!: FormConfig;
  employeePositionConfig!: FormConfig;
  employeeProbationConfig!: FormConfig;

  ngOnInit() {
    // Manually define each configuration (lots of code duplication)
    this.employeeInfoConfig = {
      fields: [
        {
          key: 'code', label: 'Mã nhân viên', required: true, width: 'half',
          type: 'text', validators: [Validators.pattern(/^[A-Z0-9-]+$/)],
          // ... many more lines of configuration
        },
        // ... more fields
      ]
    };
    
    this.employeePositionConfig = {
      // ... another large configuration object
    };
    
    this.employeeProbationConfig = {
      // ... yet another large configuration object
    };
  }
}
```

**After (Using Configuration Service):**
```typescript
export class EmployeesListComponent implements OnInit {
  employeeInfoConfig!: FormConfig;
  employeePositionConfig!: FormConfig;
  employeeProbationConfig!: FormConfig;

  constructor(
    private editCreateFormConfigService: EditCreateFormConfigService
  ) {}

  ngOnInit() {
    // Clean, centralized configuration
    this.employeeInfoConfig = this.editCreateFormConfigService.getFormConfig('employeeInfo');
    this.employeePositionConfig = this.editCreateFormConfigService.getFormConfig('employeePosition');
    this.employeeProbationConfig = this.editCreateFormConfigService.getFormConfig('employeeProbation');
  }
}
```

### 3. Template Usage (Unchanged)

```html
<!-- Step 1: Employee Info -->
<edit-create-form
  [editMode]="editMode"
  [initialData]="employeeData"
  [isDisabled]="isDisabled"
  [formConfig]="employeeInfoConfig"
  (formChange)="onEmployeeFormChange($event)">
</edit-create-form>

<!-- Step 2: Employee Position -->
<edit-create-form
  [editMode]="editMode"
  [initialData]="employeePositionData"
  [isDisabled]="isDisabled"
  [formConfig]="employeePositionConfig"
  (formChange)="onPositionFormChange($event)">
</edit-create-form>

<!-- Step 3: Employee Probation -->
<edit-create-form
  [editMode]="editMode"
  [initialData]="probationData"
  [isDisabled]="isDisabled"
  [formConfig]="employeeProbationConfig"
  (formChange)="onProbationFormChange($event)">
</edit-create-form>
```

## Available Form Configurations

The service currently provides three form configurations:

1. **`employeeInfo`** - Employee basic information form
   - Code, nickname, fullname, phone, birthday, email, identification, note
   - Includes validation for employee code, name, phone, email, and SSN

2. **`employeePosition`** - Employee position assignment form
   - Root department, position, start date, team/subdept
   - Includes dependency tracking between fields

3. **`employeeProbation`** - Employee probation setup form
   - End date, evaluation deadline, status, manual evaluation flag
   - Includes probation status options

## Benefits

1. **Code Reusability**: Form configurations are centralized and reusable across components
2. **Consistency**: All forms using the same entity type will have identical field configurations
3. **Maintainability**: Changes to form configurations only need to be made in one place
4. **Type Safety**: TypeScript ensures correct entity types are used
5. **Clean Components**: Components focus on business logic rather than form configuration

## Error Handling

The service includes built-in error handling for unsupported entity types:

```typescript
// This will throw a descriptive error
const config = this.editCreateFormConfigService.getFormConfig('invalidType');
// Error: Unsupported entity type: invalidType. Available types: employeeInfo, employeePosition, employeeProbation
```

## Extending the Service

To add new form configurations:

1. Add the new entity type to `FormEntityType`
2. Add the configuration factory to the `configMap`
3. Implement the private configuration method

```typescript
export type FormEntityType = 'employeeInfo' | 'employeePosition' | 'employeeProbation' | 'newEntityType';

private readonly configMap: Record<string, FormConfigFactory> = {
  // ... existing configs
  newEntityType: () => this.getNewEntityFormConfig()
};

private getNewEntityFormConfig(): FormConfig {
  return {
    fields: [
      // ... field configurations
    ]
  };
}
```
