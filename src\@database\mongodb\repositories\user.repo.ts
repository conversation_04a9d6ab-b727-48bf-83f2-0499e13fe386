import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { UserDocument } from '../schemas';
import { ArrayHelper } from '@app/shared/helpers';

@Injectable()
export class UserRepository extends GenericRepository<UserDocument> {
    private readonly context = UserRepository.name;

    constructor(
        @Inject(MONGO_CONST.USER_COLLECTION)
        private readonly userModel: Model<UserDocument>,
    ) {
        super(userModel);
    }

    async getUserInfo(username: string): Promise<any> {
        const results = await this.userModel.aggregate([
            {
                $match: { username },
            },
            {
                $lookup: {
                    from: MONGO_CONST.PROFILE_COLLECTION,
                    localField: 'id',
                    foreignField: 'id',
                    as: 'profile',
                },
            },
            {
                $unwind: { path: '$profile', preserveNullAndEmptyArrays: true },
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    username: 1,
                    permissionsKey: 1,
                    fullname: '$profile.fullname',
                    email: '$profile.email',
                    isAdmin: 1,
                },
            },
        ]);
        return ArrayHelper.isEmpty(results) ? null : results[0];
    }
}
