.score-table {
    width: 100%;
    /* Use CSS columns for vertical flow */
    column-width: 180px;
    column-gap: 0.5rem;
    /* Ensure there's space between rows */
    line-height: 1.5;

    .pair {
        display: flex;
        flex-direction: row;
        margin-bottom: 0.25rem;
        /* Prevent column breaks within a pair */
        break-inside: avoid;
        page-break-inside: avoid;

        .header {
            font-weight: bold;
            padding: 8px;
            background-color: var(--p-datatable-header-cell-background);
            width: 75%;
            display: flex;
            align-items: center;
        }

        .value {
            padding: 8px;
            text-align: center;
            width: 25%;
            display: flex;
            align-items: center;
            justify-content: center;
            // background-color: #ffffff;
        }
    }
}