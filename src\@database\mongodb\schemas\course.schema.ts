import { SchemaConfig } from '@database/mongodb';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';
import { CourseType } from '@app/enums';

@Schema({ versionKey: false, timestamps: true, toJSON: SchemaConfig.ToJSON, toObject: SchemaConfig.ToObject })
export class CourseDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ index: true, required: true })
    name: string;
    @Prop({})
    level: string;
    @Prop({ index: true, required: true })
    type: CourseType;
    @Prop({})
    teacher: string;
}

export const CourseSchema = SchemaFactory.createForClass(CourseDocument);
