import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { InputSearchText } from '@shared/components/input-search-text/input-search-text';
import { ErrorHandlerService } from '@shared/services';
import { CenterLocationApiService } from '@shared/services';
import * as L from 'leaflet';

interface ICenterLocation {
    name: string;
    address: string;
    lat: number;
    lng: number;
    gMapLink: string;
}

@Component({
    selector: 'app-center-location',
    standalone: true,
    imports: [CommonModule, InputSearchText],
    templateUrl: './center-location.html',
    styleUrls: ['./center-location.scss']
})
export class CenterLocation {
    listData: ICenterLocation[] = [];
    filteredData: ICenterLocation[] = [];
    selectedCenter: ICenterLocation | null = null;

    private map!: L.Map;
    private markers: L.Marker[] = [];
    constructor(
        private centerLocationApiService: CenterLocationApiService,
        private errorHandlerService: ErrorHandlerService
    ) {}

    ngOnInit() {
        this.fetchData();
    }

    fetchData() {
        this.listData = [];
        this.filteredData = [];

        this.centerLocationApiService.getAll().subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.listData = data;
                this.filteredData = data;
                this.initMap();
                this.addMarkers();
            }
        });
    }

    ngOnDestroy() {
        this.map.remove();
    }

    onSearch(searchTerm: string) {
        if (searchTerm && searchTerm.trim() !== '') {
            const lowerTerm = searchTerm.toLowerCase();
            this.filteredData = this.listData.filter((center) => center.name.toLowerCase().includes(lowerTerm) || center.address.toLowerCase().includes(lowerTerm));
        } else {
            this.filteredData = this.listData;
        }
        this.updateMarkers();
    }

    selectCenter(center: ICenterLocation) {
        this.selectedCenter = center;
        this.focusOnCenter(center);
    }

    private initMap() {
        this.map = L.map('map').setView([10.762622, 106.660172], 13); // Mặc định ở TP.HCM

        L.tileLayer('https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
            maxZoom: 19,
            attribution: '© Google Maps'
        }).addTo(this.map);
    }

    private addMarkers() {
        const customIcon = L.icon({
            iconUrl: 'assets/layout/images/marker.png',
            iconSize: [50, 50],
            iconAnchor: [25, 50],
            popupAnchor: [0, -45]
        });

        this.filteredData.forEach((store) => {
            const marker = L.marker([store.lat, store.lng], {
                icon: customIcon
            }).bindPopup(`<b>${store.name}</b><br>${store.address}`);
            marker.addTo(this.map);
            marker.on('click', () => {
                this.selectedCenter = store;
            });
            this.markers.push(marker);
        });
    }

    private updateMarkers() {
        // Xóa tất cả marker hiện tại
        this.markers.forEach((marker) => {
            this.map.removeLayer(marker);
        });
        this.markers = [];

        // Thêm lại marker mới dựa trên filteredCenters
        this.addMarkers();
    }

    private focusOnCenter(store: ICenterLocation) {
        this.map.flyTo([store.lat, store.lng], this.map.getZoom(), {
            animate: true,
            duration: 0.25,
            easeLinearity: 0.1
        });
        const marker = this.markers.find((m) => {
            const latLng = m.getLatLng();
            return latLng.lat === store.lat && latLng.lng === store.lng;
        });
        if (marker) {
            marker.openPopup();
        }
    }
}
