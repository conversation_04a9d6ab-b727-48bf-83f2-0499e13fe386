# Generic P-Table Component Usage Guide

This document provides comprehensive examples and usage patterns for the `GenericPTableComponent`.

## Overview

The `GenericPTableComponent` is a highly configurable, reusable table component built on top of PrimeNG's p-table. It supports both virtual scrolling and pagination, with built-in action handling, selection management, and custom templating capabilities.

## Quick Start

### 1. Basic Setup

```typescript
import { GenericPTableComponent, TableConfig } from '@shared/components/generic-p-table';
import { GenericPTableConfigService } from '@shared/services';

@Component({
  imports: [GenericPTableComponent, ...],
  // ...
})
export class MyComponent {
  tableConfig!: TableConfig;
  data: any[] = [];
  totalRecords: number = 0;

  constructor(
    private tableConfigService: GenericPTableConfigService
  ) {}

  ngOnInit() {
    // Using config service (recommended)
    this.tableConfig = this.tableConfigService.getTableConfigWithFunctions('employee', {
      OPEN_NEW: () => this.openNewDialog(),
      EDIT: (item: any) => this.editItem(item),
      DELETE: (item: any) => this.deleteItem(item)
    });
  }

  onTableDataLoaded(event: any) {
    this.data = event.items;
    this.totalRecords = event.totalRecords;
  }
}
```

### 2. Template Usage

```html
<generic-p-table [config]="tableConfig"
                 [data]="data"
                 [totalRecords]="totalRecords"
                 (onDataLoad)="onTableDataLoaded($event)">
</generic-p-table>
```

## Configuration Patterns

### Using GenericPTableConfigService (Recommended)

The service provides pre-configured templates for common entity types:

```typescript
// Available entity types: 'employee', 'probation', 'employeePosition'
const config = this.tableConfigService.getTableConfigWithFunctions('employee', {
  OPEN_NEW: () => this.openCreateDialog(),
  EDIT: (item: Employee) => this.openEditDialog(item),
  DELETE: (item: Employee) => this.deleteEmployee(item),
  ON_SELECT: (item: Employee) => this.onEmployeeSelect(item),
  ON_UNSELECT: (item: Employee) => this.onEmployeeUnselect(item)
});
```

### Manual Configuration

For custom tables, create configuration manually:

```typescript
const customConfig: TableConfig = {
  title: 'Custom Data Table',
  entityName: 'items',
  service: this.myApiService,
  method: 'getItems',
  columns: [
    { field: 'id', header: 'ID', width: 'min-w-[80px]', sortable: true, type: 'text' },
    { field: 'name', header: 'Name', width: 'min-w-[200px]', sortable: true, type: 'text' },
    { field: 'createdAt', header: 'Created', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy HH:mm' },
    { field: 'status', header: 'Status', width: 'min-w-[120px]', sortable: true, type: 'tag', tagType: 'status' }
  ],
  features: {
    virtualScroll: true,
    toolbar: true,
    selection: {
      mode: 'single',
      dataKey: 'id'
    }
  },
  actions: [
    {
      location: 'toolbar',
      useDefaultStyle: 'add',
      onClick: () => this.openCreateDialog()
    },
    {
      location: 'row',
      useDefaultStyle: 'edit',
      onClick: (item: any) => this.editItem(item)
    },
    {
      location: 'row',
      useDefaultStyle: 'delete',
      confirmMessage: 'Are you sure you want to delete this item?',
      onClick: (item: any) => this.deleteItem(item)
    }
  ]
};
```

## Column Types

### Text Columns
```typescript
{ field: 'name', header: 'Name', type: 'text', sortable: true }
```

### Date Columns
```typescript
{ 
  field: 'createdAt', 
  header: 'Created Date', 
  type: 'date', 
  dateFormat: 'dd/MM/yyyy HH:mm',
  sortable: true 
}
```

### Tag Columns
```typescript
{ 
  field: 'status', 
  header: 'Status', 
  type: 'tag', 
  tagType: 'employeeStatus',
  sortable: true 
}
```

### Custom Columns
```typescript
{ 
  field: 'customField', 
  header: 'Custom', 
  type: 'custom',
  sortable: false 
}
```

## Features Configuration

### Virtual Scrolling
```typescript
features: {
  virtualScroll: true,
  toolbar: true
}
```

### Pagination
```typescript
features: {
  pagination: true,
  toolbar: true
},
itemsPerPage: 25,
itemsPerPageOptions: [10, 25, 50, 100]
```

### Selection
```typescript
features: {
  selection: {
    mode: 'single', // or 'multiple'
    metaKeySelection: false,
    dataKey: 'id',
    onSelect: (item: any, event: Event) => this.handleSelect(item),
    onUnselect: (item: any, event: Event) => this.handleUnselect(item)
  }
}
```

## Action Configuration

### Toolbar Actions
```typescript
actions: [
  {
    location: 'toolbar',
    label: 'Add New',
    icon: 'pi pi-plus',
    severity: 'primary',
    onClick: () => this.openCreateDialog()
  },
  {
    location: 'toolbar',
    useDefaultStyle: 'add', // Uses default styling
    onClick: () => this.openCreateDialog()
  }
]
```

### Row Actions
```typescript
actions: [
  {
    location: 'row',
    useDefaultStyle: 'edit',
    tooltip: 'Edit item',
    onClick: (item: any) => this.editItem(item)
  },
  {
    location: 'row',
    useDefaultStyle: 'delete',
    confirmMessage: 'Are you sure you want to delete this item?',
    onClick: (item: any) => this.deleteItem(item)
  },
  {
    location: 'row',
    icon: 'pi pi-eye',
    tooltip: 'View details',
    styleClass: 'p-button-info',
    onClick: (item: any) => this.viewItem(item)
  }
]
```

### Custom Actions
```typescript
actions: [
  {
    location: 'row',
    icon: 'pi pi-user-minus',
    tooltip: 'Retire Employee',
    styleClass: 'p-button-warning',
    disabled: (item: any) => item.status === 'retired',
    onClick: (item: any) => this.retireEmployee(item)
  }
]
```

## Advanced Features

### Custom Query Builder
```typescript
queryBuilder: (params: any) => {
  return {
    search: this.searchTerm,
    departmentId: this.selectedDepartmentId,
    status: this.selectedStatus
  };
}
```

### Response Transformation
```typescript
transformResponse: (response: any) => {
  return {
    items: response.data.map((item: any) => ({
      ...item,
      displayName: `${item.firstName} ${item.lastName}`
    })),
    total: response.totalCount
  };
}
```

### Additional Parameters
```typescript
additionalParams: {
  includeInactive: true,
  departmentId: this.currentDepartmentId
}
```

## Custom Templates

### Custom Column Template
```html
<generic-p-table [config]="tableConfig"
                 [data]="data"
                 [totalRecords]="totalRecords">

  <ng-template #customColumnTemplate let-item let-field="field" let-column="column">
    <div *ngIf="field === 'employeePositions'" class="flex flex-col gap-2">
      <div *ngFor="let position of item.employeePositions" class="flex gap-2">
        <span class="font-medium">{{ position.position.name }}</span>
        <span class="text-gray-500">{{ position.department.name }}</span>
      </div>
    </div>
  </ng-template>
</generic-p-table>
```

### Custom Actions Template
```html
<generic-p-table [config]="tableConfig"
                 [data]="data"
                 [totalRecords]="totalRecords">

  <ng-template #customActions let-item>
    <button pButton
            icon="pi pi-download"
            class="p-button-text p-button-sm"
            (click)="downloadReport(item)">
    </button>
  </ng-template>
</generic-p-table>
```

### Custom Toolbar Actions
```html
<generic-p-table [config]="tableConfig"
                 [data]="data"
                 [totalRecords]="totalRecords">

  <ng-template #toolbarActions>
    <p-button icon="pi pi-upload"
              label="Import"
              severity="secondary"
              (onClick)="importData()">
    </p-button>
    <p-button icon="pi pi-download"
              label="Export"
              severity="secondary"
              (onClick)="exportData()">
    </p-button>
  </ng-template>
</generic-p-table>
```

## Complete Component Example

```typescript
import { Component, OnInit } from '@angular/core';
import { GenericPTableComponent, TableConfig, TableDataLoadEvent } from '@shared/components/generic-p-table';
import { GenericPTableConfigService } from '@shared/services';
import { EmployeeApiService } from '@shared/services';

@Component({
  selector: 'app-employee-list',
  standalone: true,
  imports: [GenericPTableComponent],
  template: `
    <generic-p-table [config]="employeeTableConfig"
                     [data]="employees"
                     [totalRecords]="totalRecords"
                     [(selection)]="selectedEmployee"
                     (onDataLoad)="onTableDataLoaded($event)"
                     (selectionChange)="onSelectionChange($event)">

      <!-- Custom column template for complex data -->
      <ng-template #customColumnTemplate let-employee let-field="field">
        <div *ngIf="field === 'employeePositions'" class="flex flex-col gap-1">
          <div *ngFor="let position of employee.employeePositions"
               class="text-sm">
            {{ position.position.name }} - {{ position.department.name }}
          </div>
        </div>
      </ng-template>

      <!-- Custom actions for specific operations -->
      <ng-template #customActions let-employee>
        <button pButton
                icon="pi pi-id-card"
                class="p-button-text p-button-sm p-button-info"
                pTooltip="View Profile"
                (click)="viewProfile(employee)">
        </button>
      </ng-template>
    </generic-p-table>

    <!-- Additional components -->
    <edit-create-dialog #editDialog
                        [(visible)]="dialogVisible"
                        [editMode]="editMode"
                        [dialogConfig]="dialogConfig"
                        [initialData]="selectedEmployee">
    </edit-create-dialog>
  `
})
export class EmployeeListComponent implements OnInit {
  employeeTableConfig!: TableConfig;
  employees: any[] = [];
  totalRecords: number = 0;
  selectedEmployee: any = null;

  dialogVisible = false;
  editMode = false;
  dialogConfig: any;

  constructor(
    private tableConfigService: GenericPTableConfigService,
    private employeeApiService: EmployeeApiService
  ) {}

  ngOnInit() {
    this.setupTableConfig();
    this.setupDialogConfig();
  }

  private setupTableConfig() {
    this.employeeTableConfig = this.tableConfigService.getTableConfigWithFunctions('employee', {
      OPEN_NEW: () => this.openCreateDialog(),
      EDIT: (employee: any) => this.openEditDialog(employee),
      DELETE: (employee: any) => this.deleteEmployee(employee),
      ON_SELECT: (employee: any) => this.onEmployeeSelect(employee),
      ON_UNSELECT: (employee: any) => this.onEmployeeUnselect(employee)
    });
  }

  private setupDialogConfig() {
    // Configure your edit-create-dialog here
    this.dialogConfig = {
      // ... dialog configuration
    };
  }

  onTableDataLoaded(event: TableDataLoadEvent) {
    this.employees = event.items;
    this.totalRecords = event.totalRecords;
  }

  onSelectionChange(selection: any) {
    this.selectedEmployee = selection;
  }

  onEmployeeSelect(employee: any) {
    console.log('Employee selected:', employee);
  }

  onEmployeeUnselect(employee: any) {
    console.log('Employee unselected:', employee);
  }

  openCreateDialog() {
    this.editMode = false;
    this.selectedEmployee = null;
    this.dialogVisible = true;
  }

  openEditDialog(employee: any) {
    this.editMode = true;
    this.selectedEmployee = employee;
    this.dialogVisible = true;
  }

  deleteEmployee(employee: any) {
    this.employeeApiService.deleteEmployee(employee.id).subscribe({
      next: () => {
        // Refresh table data
        this.refreshTable();
      },
      error: (error) => {
        console.error('Delete failed:', error);
      }
    });
  }

  viewProfile(employee: any) {
    // Navigate to profile or open profile dialog
    console.log('View profile for:', employee);
  }

  refreshTable() {
    // Trigger table refresh
    // The table will automatically reload data
  }
}
```

## Event Handling

### Data Load Events
```typescript
onTableDataLoaded(event: TableDataLoadEvent) {
  this.data = event.items;
  this.totalRecords = event.totalRecords;

  if (!event.loading) {
    // Data loading completed
    console.log('Table data loaded successfully');
  }
}
```

### Lazy Load Events
```typescript
onLazyLoad(event: TableLazyLoadParams) {
  // Handle custom lazy loading logic if needed
  console.log('Lazy load params:', event);
}
```

### Selection Events
```typescript
onSelectionChange(selection: any) {
  this.selectedItem = selection;

  // Enable/disable actions based on selection
  this.updateActionStates();
}
```

## Real-World Examples

### Employee Table with Selection
```typescript
// employees-list.component.ts
export class EmployeesListComponent implements OnInit {
  employeeLazyScrollPTableConfig!: TableConfig;
  employees: any[] = [];
  totalRecords: number = 0;
  selectedEmployeeRow: any = null;

  constructor(
    private tableConfigService: GenericPTableConfigService
  ) {}

  ngOnInit() {
    this.employeeLazyScrollPTableConfig = this.tableConfigService.getTableConfigWithFunctions('employee', {
      OPEN_NEW: () => this.openCreateEmployeeDialog(),
      EDIT: (employee: any) => this.openEditEmployeeDialog(employee),
      DELETE: (employee: any) => this.deleteEmployee(employee),
      ON_SELECT: (employee: any) => this.onEmployeeSelect(employee),
      ON_UNSELECT: (employee: any) => this.onEmployeeUnselect(employee)
    });
  }

  onTableDataLoaded(event: TableDataLoadEvent) {
    this.employees = event.items;
    this.totalRecords = event.totalRecords;
  }

  onEmployeeSelect(employee: any) {
    this.selectedEmployeeRow = employee;
    // Enable employee-specific actions
  }

  onEmployeeUnselect(employee: any) {
    this.selectedEmployeeRow = null;
    // Disable employee-specific actions
  }
}
```

### Probation Table (Simple)
```typescript
// probation.component.ts
export class ProbationComponent implements OnInit {
  probationLazyScrollPTableConfig!: TableConfig;
  probations: any[] = [];
  totalRecords: number = 0;

  constructor(
    private tableConfigService: GenericPTableConfigService
  ) {}

  ngOnInit() {
    this.probationLazyScrollPTableConfig = this.tableConfigService.getTableConfigWithFunctions('probation', {
      OPEN_NEW: () => this.openCreateProbationDialog(),
      EDIT: (probation: any) => this.openEditProbationDialog(probation)
    });
  }

  onTableDataLoaded(event: TableDataLoadEvent) {
    this.probations = event.items;
    this.totalRecords = event.totalRecords;
  }
}
```

### Employee Positions with Custom Actions
```typescript
// employees-positions.component.ts
export class EmployeesPositionsComponent implements OnInit {
  employeePositionLazyScrollPTableConfig!: TableConfig;
  employeePositions: any[] = [];
  totalRecords: number = 0;

  constructor(
    private tableConfigService: GenericPTableConfigService
  ) {}

  ngOnInit() {
    this.employeePositionLazyScrollPTableConfig = this.tableConfigService.getTableConfigWithFunctions('employeePosition', {
      OPEN_NEW: () => this.openCreateEmployeePositionDialog(),
      EDIT: (position: any) => this.openEditEmployeePositionDialog(position),
      RETIRE: (position: any) => this.openRetireEmployeePopover(position)
    });
  }

  onTableDataLoaded(event: TableDataLoadEvent) {
    this.employeePositions = event.items;
    this.totalRecords = event.totalRecords;
  }

  openRetireEmployeePopover(position: any) {
    // Open retire popover for employee position
    this.selectedEmployeePositionForRetire = position;
    this.genericRetirePopover.show();
  }
}
```

## Best Practices

### 1. Use Configuration Service
Always prefer using `GenericPTableConfigService` for standard entity types:

```typescript
// Good
const config = this.tableConfigService.getTableConfigWithFunctions('employee', functionMap);

// Avoid manual configuration for standard entities
```

### 2. Handle Loading States
```typescript
onTableDataLoaded(event: TableDataLoadEvent) {
  this.data = event.items;
  this.totalRecords = event.totalRecords;

  // Update UI based on loading state
  if (event.loading) {
    this.showLoadingIndicator();
  } else {
    this.hideLoadingIndicator();
  }
}
```

### 3. Error Handling in Actions
```typescript
actions: [
  {
    location: 'row',
    useDefaultStyle: 'delete',
    confirmMessage: 'Are you sure?',
    onClick: async (item: any) => {
      try {
        await this.deleteItem(item);
        this.refreshTable();
      } catch (error) {
        this.handleError(error);
      }
    }
  }
]
```

### 4. Responsive Column Widths
```typescript
columns: [
  { field: 'id', header: 'ID', width: 'min-w-[80px]' },
  { field: 'name', header: 'Name', width: 'min-w-[200px] max-w-[300px]' },
  { field: 'description', header: 'Description', width: 'min-w-[250px]' }
]
```

### 5. Conditional Actions
```typescript
actions: [
  {
    location: 'row',
    icon: 'pi pi-lock',
    tooltip: 'Lock Account',
    disabled: (item: any) => item.status === 'locked',
    onClick: (item: any) => this.lockAccount(item)
  }
]
```

## Virtual Scroll Configuration

### Basic Virtual Scroll
```typescript
features: {
  virtualScroll: true,
  toolbar: true
},
virtualScrollItemSize: 46.75, // Height of each row in pixels
virtualScrollOptions: {
  numToleratedItems: 5 // Number of items to render outside viewport
}
```

### Performance Optimization
```typescript
// For large datasets
virtualScrollItemSize: 38, // Smaller row height
virtualScrollOptions: {
  numToleratedItems: 10, // More buffer items
  delay: 150 // Delay before loading new items
}
```

## Troubleshooting

### Common Issues

1. **Table not loading data**:
   - Ensure service method exists and returns proper response format
   - Check console for API errors
   - Verify `method` name matches service method

2. **Actions not working**:
   - Check function mapping in configuration service
   - Verify onClick functions are properly bound
   - Check for JavaScript errors in browser console

3. **Virtual scroll performance**:
   - Adjust `virtualScrollItemSize` to match actual row height
   - Increase `numToleratedItems` for smoother scrolling
   - Consider pagination for very large datasets

4. **Selection not working**:
   - Verify `dataKey` matches your data structure
   - Check if selection mode is properly configured
   - Ensure data items have unique identifiers

### Debug Tips

```typescript
// Enable debug logging
ngOnInit() {
  console.log('Table config:', this.tableConfig);
  console.log('Service method:', this.tableConfig.service[this.tableConfig.method]);
}

// Monitor data loading
onTableDataLoaded(event: TableDataLoadEvent) {
  console.log('Data loaded:', event);
  console.log('Items count:', event.items.length);
  console.log('Total records:', event.totalRecords);
}

// Debug action execution
executeAction(action: any, item: any) {
  console.log('Executing action:', action, 'on item:', item);
}
```

## Migration from Legacy Tables

### From p-table to generic-p-table

1. **Extract column configuration**:
```typescript
// Old p-table columns
const oldColumns = [
  { field: 'name', header: 'Name' },
  { field: 'email', header: 'Email' }
];

// New generic-p-table columns
const newColumns: ColumnConfig[] = [
  { field: 'name', header: 'Name', type: 'text', sortable: true },
  { field: 'email', header: 'Email', type: 'text', sortable: true }
];
```

2. **Convert action handlers**:
```typescript
// Old action methods
editItem(item: any) { /* ... */ }
deleteItem(item: any) { /* ... */ }

// New action configuration
actions: [
  { location: 'row', useDefaultStyle: 'edit', onClick: (item) => this.editItem(item) },
  { location: 'row', useDefaultStyle: 'delete', onClick: (item) => this.deleteItem(item) }
]
```

3. **Update template**:
```html
<!-- Old -->
<p-table [value]="data" [lazy]="true" (onLazyLoad)="loadData($event)">
  <!-- Complex template structure -->
</p-table>

<!-- New -->
<generic-p-table [config]="tableConfig"
                 [data]="data"
                 [totalRecords]="totalRecords"
                 (onDataLoad)="onTableDataLoaded($event)">
</generic-p-table>
```

## API Reference

### Component Inputs
```typescript
@Input() config!: TableConfig<T>;           // Table configuration
@Input() data: T[] = [];                    // Table data array
@Input() totalRecords: number = 0;          // Total records for pagination
@Input() selection: T | T[] | null = null;  // Selected items
```

### Component Outputs
```typescript
@Output() onDataLoad = new EventEmitter<TableDataLoadEvent>();     // Data load events
@Output() onLazyLoad = new EventEmitter<TableLazyLoadParams>();    // Lazy load events
@Output() selectionChange = new EventEmitter<T | T[] | null>();    // Selection changes
```

### Content Projection Templates
```typescript
@ContentChild('customActions') customActionsTemplate?: TemplateRef<any>;      // Custom row actions
@ContentChild('toolbarActions') toolbarActionsTemplate?: TemplateRef<any>;   // Custom toolbar actions
@ContentChild('customColumnTemplate') customColumnTemplate?: TemplateRef<any>; // Custom column content
```

### Public Methods
```typescript
reload()    // Reload table from first page
refresh()   // Refresh current page
```

### TableConfig Interface
```typescript
interface TableConfig<T = any> {
  // Data source
  service: any;                    // API service instance
  method: string;                  // Service method name

  // Metadata
  title: string;                   // Table title
  entityName: string;              // Entity name for messages

  // Structure
  columns: ColumnConfig[];         // Column definitions
  actions?: TableAction<T>[];      // Action buttons

  // Features
  features?: TableFeatures;        // Feature toggles

  // Customization
  queryBuilder?: (params: any) => any;        // Custom query builder
  transformResponse?: (response: any) => any; // Response transformer
  additionalParams?: any;                     // Extra API parameters

  // Virtual scroll
  virtualScrollItemSize?: number;             // Row height
  virtualScrollOptions?: any;                 // Scroll options

  // Pagination
  itemsPerPage?: number;                      // Items per page
  itemsPerPageOptions?: number[];             // Page size options
}
```

### ColumnConfig Interface
```typescript
interface ColumnConfig {
  field: string;                   // Data field path
  header: string;                  // Column header text
  width?: string;                  // CSS width class
  sortable?: boolean;              // Enable sorting
  type?: 'text' | 'date' | 'tag' | 'custom'; // Column type
  tagType?: TagType;               // Tag type for tag columns
  dateFormat?: string;             // Date format string
}
```

### TableAction Interface
```typescript
interface TableAction<T = any> {
  location: 'row' | 'toolbar';     // Action location
  onClick: (item: T, event?: Event) => void | Promise<void>; // Click handler

  // Styling
  label?: string;                  // Button label
  icon?: string;                   // Button icon
  tooltip?: string;                // Tooltip text
  severity?: 'primary' | 'secondary' | 'success' | 'info' | 'warn' | 'danger';
  styleClass?: string;             // Custom CSS classes
  useDefaultStyle?: 'add' | 'edit' | 'delete'; // Use default styling

  // Behavior
  disabled?: (item: T) => boolean; // Disable condition
  confirmMessage?: string;         // Confirmation dialog message
}
```

This comprehensive guide covers all aspects of using the `GenericPTableComponent`. For specific use cases not covered here, refer to the existing implementations in the codebase or consult the component interfaces for additional configuration options.
