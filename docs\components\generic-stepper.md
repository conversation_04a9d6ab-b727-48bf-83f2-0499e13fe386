# Generic Stepper Component - Usage Examples

## Overview

The `GenericPStepperComponent` is a reusable, configurable stepper component that provides a scalable solution for multi-step workflows. It integrates seamlessly with the `edit-create-form` component and follows a clean separation of concerns between configuration, business logic, and UI.

## Key Features

- **Form Integration**: Works with `edit-create-form` component for consistent field rendering
- **State Management**: Maintains form data, step modes ('create', 'view', 'edit'), and navigation state
- **Flexible Actions**: Dynamic buttons with conditional visibility, labels, and icons
- **API Integration**: Automatic API calls with data transformation through `stepper-handler.service`
- **Validation**: Built-in form validation with step navigation control
- **Exit Confirmation**: Configurable confirmation dialogs when exiting with partial data

## Basic Usage

### 1. Component Setup

```typescript
import { GenericPStepperComponent, StepperConfig } from '@shared/components/generic-p-stepper';
import { StepperConfigService } from '@shared/services';

@Component({
  standalone: true,
  imports: [GenericPStepperComponent, ...],
  // ...
})
export class MyComponent {
  stepperVisible = false;
  stepperConfig!: StepperConfig;
  editMode = false;
  selectedItem: any = null;

  constructor(private stepperConfigService: StepperConfigService) {
    // Initialize stepper configuration
    this.stepperConfig = this.stepperConfigService.getStepperConfig(
      'employee-creation',
      () => this.onStepperSuccess(),
      () => this.onStepperCancel()
    );
  }

  openStepper() {
    this.editMode = false;
    this.selectedItem = null;
    this.stepperVisible = true;
  }

  onStepperSuccess() {
    this.stepperVisible = false;
    this.tableComponent?.refresh(); // Refresh data if needed
    this.toastService.showSuccess('Process completed successfully');
  }

  onStepperCancel() {
    this.stepperVisible = false;
  }
}
```

### 2. Template Usage

```html
<!-- Employee Creation Stepper -->
<generic-p-stepper [(visible)]="employeeDialog"
                 [stepperConfig]="employeeCreationStepperConfig"
                 [initialData]="selectedEmployee"
                 [editMode]="editMode"
                 (onComplete)="onStepperSuccess()"
                 (onCancel)="onStepperCancel()">
</generic-p-stepper>
```

## Architecture

### Component Structure

```
GenericPStepperComponent
├── StepperConfig (configuration)
├── StepperHandlerService (business logic)
├── EditCreateFormComponent (form rendering)
└── StepperState (state management)
```

### Data Flow

1. **Configuration**: `StepperConfigService` provides step configurations and actions
2. **State Management**: Component maintains `StepperState` with forms, data, and modes
3. **Business Logic**: `StepperHandlerService` handles navigation, API calls, and validation
4. **Form Integration**: Each step uses `edit-create-form` with field configurations

## Configuration

### Creating a New Stepper Type

To create a new stepper configuration, add it to the function map in `StepperConfigService`:

```typescript
// In stepper-config.service.ts
private readonly configMap: Record<string, StepperConfigFactory> = {
  'employee-creation': (onSuccess, onCancel) => this.getEmployeeCreationStepperConfig(onSuccess, onCancel),
  'your-new-type': (onSuccess, onCancel) => this.getYourNewTypeStepperConfig(onSuccess, onCancel),
  'project-setup': (onSuccess, onCancel) => this.getProjectSetupStepperConfig(onSuccess, onCancel)
};

### Example Configuration Function

```typescript
private getProjectSetupStepperConfig(
  onSuccess: () => void,
  onCancel: () => void
): StepperConfig {

  // General configuration object
  const generalConfig = {
    entityType: 'project-setup',
    width: '700px',
    header: 'Project Setup Wizard',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid h-[600px]',
    linear: true,
    onComplete: onSuccess,
    onCancel: onCancel,
    confirmOnClose: true,
    hasPartialData: (stepperState: StepperState) => !!(stepperState.stepData[1]?.id),
    exitConfirmationMessage: () => 'You have unsaved changes. Are you sure you want to exit?'
  };

  // Step configurations with form and handler configs
  const steps: StepConfig[] = [
    {
      value: 1,
      label: 'Project Info',
      formConfig: {
        ...this.editCreateFormConfigService.getFormConfig('projectInfo'),
        editMode: (stepperState: StepperState) => stepperState.stepModes[1] === 'edit',
        isDisabled: (stepperState: StepperState) => stepperState.stepModes[1] === 'view'
      },
      handlerConfig: {
        entityLabel: 'project',
        service: this.projectService,
        createMethod: 'createProject',
        updateMethod: 'updateProject',
        createDataTransform: (formValue: any) => ({
          name: formValue.name,
          description: formValue.description,
          startDate: formValue.startDate
        }),
        stepDataTransform: (apiResponse: any, formValue: any) => ({
          id: apiResponse.id,
          name: formValue.name,
          description: formValue.description,
          startDate: new Date(formValue.startDate)
        })
      }
    },
    {
      value: 2,
      label: 'Team Assignment',
      formConfig: {
        ...this.editCreateFormConfigService.getFormConfig('projectTeam'),
        editMode: (stepperState: StepperState) => stepperState.stepModes[2] === 'edit',
        isDisabled: (stepperState: StepperState) => stepperState.stepModes[2] === 'view'
      },
      handlerConfig: {
        entityLabel: 'team assignment',
        service: this.projectTeamService,
        createMethod: 'assignTeam',
        createDataTransform: (formValue: any, stepperState: StepperState) => ({
          projectId: stepperState.stepData[1]?.id,
          teamMembers: formValue.teamMembers,
          leaderId: formValue.leaderId
        })
      }
    }
  ];

  // Action configurations with simple function name mapping
  const actions: StepAction[] = [
    {
      label: 'Back',
      icon: 'pi pi-arrow-left',
      severity: 'secondary',
      location: 'left',
      hideOnSteps: [1],
      disabled: (stepperState) => stepperState.isLoading,
      onClick: 'goBack'
    },
    {
      label: 'Edit',
      icon: 'pi pi-pencil',
      severity: 'secondary',
      location: 'left',
      hidden: (stepperState) => stepperState.stepModes[stepperState.currentStep] !== 'view',
      onClick: 'enableEdit'
    },
    {
      label: (stepperState) => stepperState.currentStep === steps.length ? 'Complete' : 'Next',
      icon: (stepperState) => stepperState.currentStep === steps.length ? 'pi pi-check' : 'pi pi-arrow-right',
      location: 'right',
      disabled: (stepperState) => {
        const form = stepperState.forms[stepperState.currentStep];
        return stepperState.isLoading || !form || form.invalid;
      },
      loading: (stepperState) => stepperState.isLoading,
      onClick: 'goNext'
    }
  ];

  return {
    ...generalConfig,
    steps,
    actions
  };
}
```

## Real-World Example: Employee Creation

The employee creation stepper demonstrates the full capabilities of the generic stepper:

**Template (employees-list.component.html):**
```html
<!-- Employee Creation Stepper -->
<generic-p-stepper [(visible)]="employeeDialog"
                 [stepperConfig]="employeeCreationStepperConfig"
                 [initialData]="selectedEmployee"
                 [editMode]="editMode"
                 (onComplete)="onStepperSuccess()"
                 (onCancel)="onStepperCancel()">
</generic-p-stepper>
```

**Component (employees-list.component.ts):**
```typescript
// Initialize stepper configuration
this.employeeCreationStepperConfig = this.stepperConfigService.getStepperConfig(
    'employee-creation',
    () => this.onStepperSuccess(),
    () => this.onStepperCancel()
);

onStepperSuccess() {
    this.employeeDialog = false;
    this.tableComponent?.refresh();
    this.toastService.showSuccess('Tạo nhân viên thành công');
}
```

## Key Features

### 1. **Step Modes and Navigation**
- **Create Mode**: Initial mode for new data entry
- **View Mode**: Read-only mode after going back to completed steps
- **Edit Mode**: Activated by "Edit" button to modify completed steps
- Smart navigation: API calls only in create/edit modes, direct navigation in view mode

### 2. **Form Integration**
- Leverages existing `edit-create-form-config.service` configurations
- Dynamic form state based on step modes (disabled in view mode)
- Automatic form validation with step navigation control

### 3. **Data Transformation**
- **commonDataTransform**: Applied first to all operations (create, update, stepData)
- **createDataTransform**: Additional transforms for API creation calls (applied after common)
- **updateDataTransform**: Additional transforms for API update calls (applied after common)
- **stepDataTransform**: Additional transforms for internal state storage (applied after common)
- Preserves data relationships between steps (e.g., employeeId from step 1 used in step 2)

### 4. **Flexible Actions**
- Dynamic button labels and icons based on stepper state
- Conditional visibility (hideOnSteps, showOnSteps, hidden functions)
- Loading states and disabled states based on form validation
- Clean separation: actions delegate to `stepperHandlerService` methods

### 5. **Exit Confirmation**
- Configurable confirmation when exiting with partial data
- Custom warning messages based on created data
- Preserves user work and provides clear next steps

## Interface Reference

### StepperConfig
```typescript
interface StepperConfig {
  entityType: string;                    // Unique identifier for stepper type
  width?: string;                        // Dialog width (e.g., '600px')
  header?: string;                       // Dialog header text
  modal?: boolean;                       // Modal behavior (default: true)
  dismissableMask?: boolean;             // Click outside to close (default: true)
  styleClass?: string;                   // CSS classes for styling
  linear?: boolean;                      // Linear navigation (default: true)
  steps: StepConfig[];                   // Array of step configurations
  actions: StepAction[];                 // Array of action buttons
  onComplete?: () => void;               // Success callback
  onCancel?: () => void;                 // Cancel callback
  confirmOnClose?: boolean;              // Show confirmation on exit
  hasPartialData?: (stepperState: StepperState) => boolean;
  exitConfirmationMessage?: (stepperState: StepperState) => string;
}
```

### StepConfig
```typescript
interface StepConfig {
  value: number;                         // Step number (1, 2, 3...)
  label: string;                         // Step label in stepper header
  entityLabel?: string;                  // Entity name for success messages
  formConfig?: StepFormConfig;           // Form configuration for this step
  handlerConfig?: StepHandlerConfig;     // API handler configuration
  disabled?: boolean | ((stepperState: StepperState) => boolean);
  hidden?: boolean | ((stepperState: StepperState) => boolean);
}
```

### StepHandlerConfig
```typescript
interface StepHandlerConfig {
  service: any;                          // Angular service for API calls
  createMethod?: string;                 // Service method for creation
  updateMethod?: string;                 // Service method for updates
  commonDataTransform?: (formValue: any, stepperState: StepperState) => any;
  createDataTransform?: (formValue: any, stepperState: StepperState) => any;
  updateDataTransform?: (formValue: any, stepperState: StepperState) => any;
  stepDataTransform?: (apiResponse: any, formValue: any, stepperState: StepperState) => any;
}
```

## Default Behaviors and Configuration

### Default Functions in StepperConfigService

The `StepperConfigService` provides several default functions that are commonly used across stepper configurations:

#### 1. Default Edit Mode Function
```typescript
defaultEditMode = (stepperState: StepperState) =>
  stepperState.stepModes[stepperState.currentStep] === 'edit';
```
**Purpose**: Determines if a form should be in edit mode based on the current step's mode.
**Usage**: Applied to `formConfig.editMode` in step configurations.
**Behavior**: Returns `true` when the current step is in 'edit' mode, `false` otherwise.

#### 2. Default Is Disabled Function
```typescript
defaultIsDisabled = (stepperState: StepperState) =>
  stepperState.stepModes[stepperState.currentStep] === 'view';
```
**Purpose**: Determines if a form should be disabled based on the current step's mode.
**Usage**: Applied to `formConfig.isDisabled` in step configurations.
**Behavior**: Returns `true` when the current step is in 'view' mode, `false` otherwise.

#### 3. Default Has Partial Data Function
```typescript
defaultHasPartialData = (stepperState: StepperState) =>
  !!(stepperState.stepData[1] && stepperState.stepData[1].id);
```
**Purpose**: Determines if there's partial data that would be lost on exit.
**Usage**: Applied to `StepperConfig.hasPartialData`.
**Behavior**: Returns `true` if the first step has data with an ID, indicating a partially completed workflow.

#### 4. Default Exit Confirmation Message Function
```typescript
defaultExitConfirmationMessage = (
  stepperState: StepperState,
  steps: StepConfig[],
  fieldSelectors: Record<number, (data: any) => string>,
  customMissingMessages?: Record<number, string>
) => string
```
**Purpose**: Generates a dynamic confirmation message when exiting with partial data.
**Usage**: Applied to `StepperConfig.exitConfirmationMessage`.
**Behavior**:
- Lists existing data from completed steps
- Shows what data would be missing if exiting
- Provides the employee ID for future reference
- Uses `entityLabel` from each step for descriptive messages

### Default Actions Array

The service provides a comprehensive set of default actions that handle common stepper navigation:

#### 1. Back Button
```typescript
{
  label: 'Quay lại',
  icon: 'pi pi-arrow-left',
  severity: 'secondary',
  location: 'left',
  hideOnSteps: [1],
  disabled: (stepperState) => stepperState.isLoading,
  onClick: (stepperState, activateCallback) =>
    this.stepperHandlerService.goBack(stepperState, activateCallback)
}
```
**Behavior**:
- Hidden on step 1 (first step)
- Disabled during loading
- Navigates to previous step
- Sets previous step to 'view' mode if it has data

#### 2. Edit Button
```typescript
{
  label: 'Chỉnh sửa',
  icon: 'pi pi-pencil',
  severity: 'secondary',
  location: 'left',
  hidden: (stepperState) => stepperState.stepModes[stepperState.currentStep] !== 'view',
  disabled: (stepperState) => stepperState.isLoading,
  onClick: (stepperState) => this.stepperHandlerService.enableEdit(stepperState)
}
```
**Behavior**:
- Only visible when current step is in 'view' mode
- Disabled during loading
- Changes current step mode from 'view' to 'edit'

#### 3. Skip Probation Button
```typescript
{
  label: 'Bỏ qua thử việc',
  icon: 'pi pi-times',
  severity: 'secondary',
  location: 'right',
  showOnSteps: [3],
  disabled: (stepperState) => stepperState.isLoading,
  onClick: (stepperState, stepperConfig) =>
    this.stepperHandlerService.skipStep(stepperState, stepperConfig)
}
```
**Behavior**:
- Only shown on step 3 (probation step)
- Disabled during loading
- Completes the stepper without processing the current step

#### 4. Next/Complete Button
```typescript
{
  label: (stepperState) => stepperState.currentStep === 3 ? 'Hoàn thành' : 'Tiếp theo',
  icon: (stepperState) => stepperState.currentStep === 3 ? 'pi pi-check' : 'pi pi-arrow-right',
  location: 'right',
  disabled: (stepperState) => {
    const form = stepperState.forms[stepperState.currentStep];
    return stepperState.isLoading || !form || form.invalid;
  },
  loading: (stepperState) => stepperState.isLoading,
  onClick: (stepperState, stepperConfig, activateCallback, currentStepHandlerConfig) =>
    this.stepperHandlerService.goNext(stepperState, stepperConfig, activateCallback, currentStepHandlerConfig)
}
```
**Behavior**:
- Dynamic label: "Tiếp theo" for steps 1-2, "Hoàn thành" for step 3
- Dynamic icon: arrow-right for steps 1-2, check for step 3
- Disabled when loading, no form exists, or form is invalid
- Shows loading spinner during API calls
- Processes current step and navigates to next or completes stepper

### Default Component Behaviors

#### Step Mode Management
The stepper automatically manages three modes for each step:

1. **'create' Mode**:
   - Initial mode for all steps
   - Form is enabled and in create mode
   - Next button triggers create API call
   - Applied when first entering a step

2. **'view' Mode**:
   - Applied after successful API call completion
   - Form is disabled (read-only)
   - Edit button becomes visible
   - Next button navigates without API call
   - Applied when returning to a completed step

3. **'edit' Mode**:
   - Applied when Edit button is clicked in 'view' mode
   - Form is enabled and in edit mode
   - Next button triggers update API call
   - Allows modification of previously saved data

#### Default Dialog Configuration
```typescript
// Applied when not specified in StepperConfig
dialogHeader: 'Stepper'                 // Default header text
dialogWidth: '650px'                     // Default dialog width
isLinear: true                           // Default linear navigation (unless explicitly set to false)
modal: true                              // Default modal behavior
dismissableMask: true                    // Default mask dismissal
styleClass: 'p-fluid'                    // Default PrimeNG fluid styling
```

#### Default Confirmation Behavior
```typescript
// Default partial data check (when hasPartialData not provided)
hasPartialData(): boolean {
  return this.stepperState.currentStep > 1 &&
         Object.keys(this.stepperState.stepData).length > 0;
}

// Default confirmation message (when exitConfirmationMessage not provided)
getConfirmationMessage(): string {
  return 'Bạn có chắc chắn muốn thoát? Dữ liệu đã nhập sẽ bị mất.';
}
```

### Default Handler Service Behaviors

#### Navigation Logic
```typescript
// goBack() behavior
- Decrements currentStep by 1 (minimum 1)
- Sets previous step to 'view' mode if it has data
- Calls activateCallback if provided

// goNext() behavior
- In 'view' mode: navigates without API call
- In 'create'/'edit' mode: makes API call then navigates
- Sets current step to 'view' mode after successful API call
- Sets next step to 'create' mode
- Completes stepper if on last step

// enableEdit() behavior
- Changes current step mode from 'view' to 'edit'
- Triggers form re-resolution to enable editing
```

#### Default Success/Error Messages
```typescript
// Success messages (using entityLabel from step)
Create: `Tạo ${entityLabel} thành công` or 'Tạo thành công'
Update: `Cập nhật ${entityLabel} thành công` or 'Cập nhật thành công'

// Error messages (using entityLabel from step)
Create: `Có lỗi xảy ra khi tạo ${entityLabel}` or 'Có lỗi xảy ra khi tạo'
Update: `Có lỗi xảy ra khi cập nhật ${entityLabel}` or 'Có lỗi xảy ra khi cập nhật'
```

### Default Form Integration

#### Form Resolution Process
1. **Initial Resolution**: All step configs resolved at stepper initialization
2. **Dynamic Resolution**: Functions (editMode, isDisabled) evaluated with current stepperState
3. **Re-resolution Triggers**:
   - Step navigation (all steps re-resolved)
   - Mode changes (current step re-resolved)
   - Initial data changes (all steps re-resolved)

#### Form State Management
```typescript
// Default form behaviors
getStepEditMode(): boolean {
  // Returns resolved editMode value or false
  return (resolvedConfig.editMode as boolean) || false;
}

getStepIsDisabled(): boolean {
  // Returns resolved isDisabled value or false
  return (resolvedConfig.isDisabled as boolean) || false;
}

getStepInitialData(): any {
  // Returns step-specific data or fallback to initial data
  return this.stepperState.stepData[step.value] || this.stepperState.initialData;
}
```

### Default Action Visibility Logic

#### Action Filtering Process
1. **Step-based Filtering**:
   ```typescript
   // showOnSteps: only show on specified steps
   if (action.showOnSteps && !action.showOnSteps.includes(currentStep)) return false;

   // hideOnSteps: hide on specified steps
   if (action.hideOnSteps && action.hideOnSteps.includes(currentStep)) return false;
   ```

2. **Dynamic Visibility**:
   ```typescript
   // hidden function: dynamic visibility based on stepperState
   if (action.hidden && action.hidden(stepperState)) return false;
   ```

3. **Location-based Grouping**:
   ```typescript
   // Left actions: back, edit buttons
   getLeftActions(): action.location === 'left'

   // Right actions: next, complete, skip buttons
   getRightActions(): action.location === 'right'
   ```

### Default Data Transformation Patterns

#### Transform Hierarchy
The stepper applies transforms in this order:
1. **commonDataTransform** (if exists) - applied to all operations
2. **Specific transform** (create/update/stepData) - applied on top of common

```typescript
// Common transform applied to all operations
commonDataTransform: (formValue: any, stepperState: StepperState) => ({
  code: formValue.code?.toUpperCase(),
  fullname: formValue.fullname,
  birthday: formValue.birthday ? new Date(formValue.birthday) : null,
  // ... other common fields
}),

// Create transform - only additional fields needed for creation
createDataTransform: (formValue: any, stepperState: StepperState) => ({
  // Only fields that differ from common transform
  status: 'active'
}),

// Update transform - only additional fields needed for updates
updateDataTransform: (formValue: any, stepperState: StepperState) => ({
  id: stepperState.stepData[1]?.id,
  // No need to spread commonDataTransform - it's applied automatically
}),

// Step data transform - only additional fields for internal storage
stepDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => ({
  id: apiResponse.id || apiResponse,
  status: apiResponse.status
  // No need to spread commonDataTransform - it's applied automatically
})
```

### Default Error Handling

#### Form Validation
- Next button automatically disabled when form is invalid
- Loading state prevents multiple submissions
- Form validation errors displayed by edit-create-form component

#### API Error Handling
- Errors caught and displayed via ToastService
- Loading state reset on error
- Stepper remains on current step for error correction

#### Confirmation Dialog
- Automatic confirmation when exiting with partial data
- Customizable confirmation messages
- Preserves stepper state when user cancels exit

## Overriding Defaults

### Custom Dialog Configuration
```typescript
const customStepperConfig: StepperConfig = {
  entityType: 'custom-entity',
  width: '800px',                        // Override default 650px
  header: 'Custom Process',              // Override default 'Stepper'
  modal: false,                          // Override default true
  dismissableMask: false,                // Override default true
  styleClass: 'custom-stepper-class',    // Override default 'p-fluid'
  linear: false,                         // Override default true
  // ... other config
};
```

### Custom Form Behavior Functions
```typescript
// Custom edit mode logic
const customEditMode = (stepperState: StepperState) => {
  // Custom logic instead of default mode-based check
  return stepperState.currentStep === 2 && someCustomCondition;
};

// Custom disabled logic
const customIsDisabled = (stepperState: StepperState) => {
  // Custom logic instead of default view mode check
  return stepperState.stepData[stepperState.currentStep]?.locked === true;
};

// Apply to step config
{
  value: 1,
  label: 'Custom Step',
  formConfig: {
    fields: [...],
    editMode: customEditMode,           // Override default
    isDisabled: customIsDisabled        // Override default
  }
}
```

### Custom Confirmation Logic
```typescript
// Custom partial data detection
const customHasPartialData = (stepperState: StepperState) => {
  // Custom logic instead of default step 1 ID check
  return stepperState.stepData[2]?.importantField !== undefined;
};

// Custom confirmation message
const customExitMessage = (stepperState: StepperState) => {
  // Custom message instead of default generic message
  const completedSteps = Object.keys(stepperState.stepData).length;
  return `You have completed ${completedSteps} steps. Exit anyway?`;
};

// Apply to stepper config
const stepperConfig: StepperConfig = {
  // ... other config
  hasPartialData: customHasPartialData,
  exitConfirmationMessage: customExitMessage
};
```

### Custom Actions
```typescript
// Add custom action to default actions
const customActions: StepAction[] = [
  ...this.stepperConfigService.defaultActions,  // Include defaults
  {
    label: 'Save Draft',
    icon: 'pi pi-save',
    severity: 'info',
    location: 'left',
    showOnSteps: [1, 2],                         // Only on specific steps
    disabled: (stepperState) => !stepperState.forms[stepperState.currentStep]?.dirty,
    onClick: (stepperState) => this.saveDraft(stepperState)
  }
];

// Or completely replace default actions
const customActionsOnly: StepAction[] = [
  {
    label: 'Previous',
    icon: 'pi pi-chevron-left',
    location: 'left',
    onClick: 'customGoBack'
  },
  {
    label: 'Next',
    icon: 'pi pi-chevron-right',
    location: 'right',
    onClick: 'customGoNext'
  }
];
```

## Best Practices

### 1. Leverage Defaults When Possible
```typescript
// ✅ Good: Use default functions for common behaviors
formConfig: {
  fields: [...],
  editMode: this.defaultEditMode,        // Reuse default
  isDisabled: this.defaultIsDisabled     // Reuse default
}

// ❌ Avoid: Reimplementing default logic
formConfig: {
  fields: [...],
  editMode: (stepperState) => stepperState.stepModes[stepperState.currentStep] === 'edit',
  isDisabled: (stepperState) => stepperState.stepModes[stepperState.currentStep] === 'view'
}
```

### 2. Consistent EntityLabel Usage
```typescript
// ✅ Good: Descriptive entity labels for clear messages
{
  value: 1,
  label: 'Employee',
  entityLabel: 'nhân viên',              // Clear, descriptive
}

// ✅ Good: Consistent with form purpose
{
  value: 2,
  label: 'Position',
  entityLabel: 'vị trí công tác',        // Matches step purpose
}

// ❌ Avoid: Generic or missing entity labels
{
  value: 3,
  label: 'Step 3',                       // Not descriptive
  entityLabel: 'data',                   // Too generic
}
```

### 3. Proper Data Transformation
```typescript
// ✅ Good: Use commonDataTransform for shared logic
commonDataTransform: (formValue: any, stepperState: StepperState) => ({
  // Transform all common fields consistently
  code: formValue.code?.toUpperCase(),
  date: formValue.date ? new Date(formValue.date) : null,
  // ... other common fields
}),

// ✅ Good: Only specify what's different from common
createDataTransform: (formValue: any, stepperState: StepperState) => ({
  status: 'active'  // Only additional fields for creation
}),

updateDataTransform: (formValue: any, stepperState: StepperState) => ({
  id: stepperState.stepData[currentStep]?.id,  // Only additional fields for updates
}),

stepDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => ({
  id: apiResponse.id || apiResponse,           // Only additional fields for storage
  status: apiResponse.status
})
```

### 4. Action Configuration
```typescript
// ✅ Good: Use default actions as base
actions: [
  ...this.defaultActions,                      // Include proven defaults
  // Add only necessary custom actions
]

// ✅ Good: Specific step targeting
{
  label: 'Skip Optional Step',
  showOnSteps: [3],                           // Only where relevant
  onClick: (stepperState, stepperConfig) => this.skipStep(stepperState, stepperConfig)
}

// ❌ Avoid: Overly complex action logic
{
  label: (stepperState) => {
    // Complex logic in config - move to service
    if (stepperState.currentStep === 1 && stepperState.stepData[1]?.type === 'special') {
      return stepperState.stepModes[1] === 'edit' ? 'Update Special' : 'Create Special';
    }
    return 'Default Action';
  }
}
```

### 5. Error Handling
```typescript
// ✅ Good: Let defaults handle common cases
// Default error messages work for most scenarios

// ✅ Good: Custom handling only when needed
stepDataTransform: (apiResponse: any, formValue: any, stepperState: StepperState) => {
  if (apiResponse.specialCase) {
    // Handle special response format
    return { id: apiResponse.data.id, ...formValue };
  }
  // Fall back to standard handling
  return { id: apiResponse.id, ...formValue };
}

// ❌ Avoid: Overriding error handling unnecessarily
// Let the service handle standard error cases
```

### 6. Configuration Organization
```typescript
// ✅ Good: Clear separation of concerns
private getCustomStepperConfig(): StepperConfig {
  const generalConfig = {
    entityType: 'custom-type',
    width: '700px',
    // ... dialog config
  };

  const steps = [
    this.buildStep1Config(),
    this.buildStep2Config(),
    this.buildStep3Config()
  ];

  return {
    ...generalConfig,
    steps,
    actions: this.defaultActions
  };
}

private buildStep1Config(): StepConfig {
  return {
    value: 1,
    label: 'Step 1',
    entityLabel: 'entity 1',
    formConfig: {
      ...this.editCreateFormConfigService.getFormConfig('step1'),
      editMode: this.defaultEditMode,
      isDisabled: this.defaultIsDisabled
    },
    handlerConfig: this.buildStep1Handler()
  };
}
```
```

### StepAction
```typescript
interface StepAction {
  label?: string | ((stepperState: StepperState) => string);
  icon?: string | ((stepperState: StepperState) => string);
  severity?: "success" | "info" | "warn" | "danger" | "help" | "primary" | "secondary" | "contrast";
  location: 'left' | 'right';           // Button position
  hideOnSteps?: number[];                // Steps where button is hidden
  showOnSteps?: number[];                // Steps where button is shown
  disabled?: (stepperState: StepperState) => boolean;
  hidden?: (stepperState: StepperState) => boolean;
  loading?: (stepperState: StepperState) => boolean;
  onClick: (stepperState, stepperConfig, activateCallback?, currentStepHandlerConfig?) => void;
}
```

## Function Map Pattern Benefits

The service uses a **function map pattern** for scalability:

```typescript
// ✅ Function Map Pattern (Current)
private readonly configMap: Record<string, StepperConfigFactory> = {
  'employee-creation': (onSuccess, onCancel) => this.getEmployeeCreationStepperConfig(onSuccess, onCancel),
  'project-creation': (onSuccess, onCancel) => this.getProjectCreationStepperConfig(onSuccess, onCancel)
};

// ❌ Switch-Case Pattern (Avoided)
getStepperConfig(type: string): StepperConfig {
  switch (type) {
    case 'employee-creation': return this.getEmployeeCreationStepperConfig();
    case 'project-creation': return this.getProjectCreationStepperConfig();
    default: throw new Error(`Unknown type: ${type}`);
  }
}
```

### Advantages:
- **No manual registration**: Just add to the map
- **Type safety**: TypeScript ensures all functions match the signature
- **Scalability**: Easily handles 10-20+ configurations
- **Clean error handling**: Automatic validation of supported types

## Benefits

1. **Scalability**: Easy to add new stepper workflows with function map pattern
2. **Maintainability**: Centralized configuration with clean separation of concerns
3. **Consistency**: Same UX patterns and behavior across all steppers
4. **Testability**: Isolated, configurable components with clear interfaces
5. **Flexibility**: Supports various field types, validation rules, and data transformations
6. **Form Integration**: Leverages existing `edit-create-form-config.service` configurations

## Best Practices

### Configuration Design
- **Keep configs pure**: No business logic in configuration objects
- **Use generalConfig**: Place common properties in a config object and spread them
- **Delegate to handlers**: Actions should call `stepperHandlerService` methods
- **Leverage existing forms**: Reuse `edit-create-form-config.service` configurations

### Data Transformation
- **Use commonDataTransform**: Place shared transformation logic in commonDataTransform
- **Specific transforms only**: Only include fields that differ from common in create/update/stepData transforms
- **Preserve relationships**: Use stepperState.stepData to access previous step data
- **Handle dates carefully**: Use Date objects for internal state, ISO strings for API calls
- **Transform at boundaries**: Transform data when entering/leaving the stepper

### Step Modes
- **Start in create mode**: New steppers begin in 'create' mode
- **Auto-transition to view**: Completed steps automatically become 'view' mode
- **Explicit edit activation**: Use "Edit" button to switch from 'view' to 'edit' mode
- **Form state follows mode**: Disable forms in 'view' mode, enable in 'create'/'edit' modes

### Action Configuration
- **Use functions for dynamic content**: Labels and icons can be functions of stepperState
- **Proper visibility control**: Use hideOnSteps/showOnSteps and hidden functions
- **Loading and disabled states**: Tie to form validation and API call states
- **Clean onClick handlers**: Keep them concise, delegate complex logic to services

## Migration from Legacy Steppers

The generic stepper replaces manual stepper implementations, providing:

### Code Reduction
- **From ~400 lines to ~50 lines** in component files
- **Eliminated manual step navigation logic**
- **Removed duplicate form handling code**

### Improved Maintainability
- **Centralized stepper logic** in reusable services
- **Consistent error handling** across all steppers
- **Standardized data transformation patterns**

### Enhanced UX
- **Consistent step navigation behavior**
- **Proper form validation integration**
- **Smart exit confirmation with partial data warnings**

## Common Patterns

### Multi-Entity Creation
Employee creation demonstrates the pattern for creating related entities across steps:
1. **Step 1**: Create employee → store ID in stepData[1]
2. **Step 2**: Create position using employeeId from stepData[1]
3. **Step 3**: Create probation using employeePositionId from stepData[2]

### Data Preservation
- **stepData**: Stores API responses and transformed data for cross-step access
- **forms**: Maintains form instances for validation and data access
- **stepModes**: Tracks whether each step is in 'create', 'view', or 'edit' mode

### Error Handling
- **API errors**: Handled by `stepperHandlerService` with toast notifications
- **Form validation**: Prevents navigation when forms are invalid
- **Exit confirmation**: Warns users about partial data loss with specific entity information
