import { CommonModule } from '@angular/common';
import { Component, ContentChild, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, TemplateRef } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { DefaultCancelButtonDirective, DefaultConfirmButtonDirective } from '@shared/directives';
import { EditCreateFormComponent, FormConfig } from '@shared/components/edit-create-form';
import { EditCreateDialogConfig, DialogAction, DialogHandlerConfig } from './edit-create-dialog.interfaces';
import { FormHandlerService } from '@shared/services';

@Component({
  selector: 'edit-create-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    DialogModule,
    ButtonModule,
    EditCreateFormComponent,
    DefaultCancelButtonDirective,
    DefaultConfirmButtonDirective
  ],
  templateUrl: './edit-create-dialog.component.html'
})
export class EditCreateDialogComponent implements OnInit, OnChanges {
  @Input() visible: boolean = false;
  @Input() editMode: boolean = false;
  @Input() dialogConfig: EditCreateDialogConfig = { formConfig: { fields: [] }, actions: [] };
  @Input() initialData: any = null;
  @Input() showDefaultActions: boolean = false; // Default to false since actions are now required in config

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() onSave = new EventEmitter<{ formValue: any, editMode: boolean }>();
  @Output() onCancel = new EventEmitter<void>();
  @Output() onFieldChange = new EventEmitter<{ fieldKey: string, value: any, formValue: any }>();

  // Template references for custom field templates
  @ContentChild('customFieldTemplate') customFieldTemplate?: TemplateRef<any>;

  formConfig: FormConfig = { fields: [] };
  transformedInitialData: any = null;
  form: FormGroup | null = null; // Reference to the form from edit-create-form

  // Function map for action onClick handlers
  actionFunctionMap: Record<string, (handlerConfig: DialogHandlerConfig, formValue: any, editMode: boolean) => void | Promise<any>> = {};

  constructor(
    private formHandlerService: FormHandlerService
  ) {
    this.actionFunctionMap = {
      'cancel': (handlerConfig) => this.formHandlerService.handleCancel(handlerConfig),
      'save': (handlerConfig, formValue, editMode) => this.formHandlerService.handleSave({
        config: handlerConfig,
        formValue,
        editMode
      })
    };
  }

  ngOnInit() {
    this.loadFormConfig();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['dialogConfig'] && !changes['dialogConfig'].firstChange) {
      this.loadFormConfig();
    }
    if (changes['initialData']) {
      this.transformInitialData();
    }
  }

  private loadFormConfig() {
    this.formConfig = this.dialogConfig.formConfig;
    this.transformInitialData();
  }

  private transformInitialData() {
    if (!this.initialData) {
      this.transformedInitialData = null;
      return;
    }
    this.transformedInitialData = this.dialogConfig.handlerConfig?.commonDataTransform ? this.dialogConfig.handlerConfig.commonDataTransform(this.initialData) : this.initialData;
    this.transformedInitialData = this.dialogConfig.handlerConfig?.initialDataTransform ? this.dialogConfig.handlerConfig.initialDataTransform(this.initialData) : this.initialData;
  }

  onFormChange(form: FormGroup) {
    this.form = form;
    this.onFieldChange.emit({
      fieldKey: '',
      value: null,
      formValue: form.value
    });
  }

  onDialogHide() {
    this.visible = false;
    this.visibleChange.emit(false);
    this.onCancel.emit();
  }

  executeAction(action: DialogAction) {
    if (!this.form) {
      console.error('Form not available');
      return;
    }
    let config = this.dialogConfig.handlerConfig;
    if (!config) {
      console.error('Handler configuration is required');
      return
    }
    const actionFunction = this.actionFunctionMap[action.onClick];
    if (actionFunction) {
      const result = actionFunction(config, this.form.getRawValue(), this.editMode);
      if (result instanceof Promise) {
        result.catch(error => {
          console.error('Action failed:', error);
        });
      }
    }
  }

  getDefaultSaveAction(): DialogAction {
    return {
      onClick: 'save',
      disabled: (formValue, formValid) => !formValid,
      type: 'submit',
      useDefaultStyle: 'confirm'
    };
  }

  getDefaultCancelAction(): DialogAction {
    return {
      onClick: 'cancel',
      useDefaultStyle: 'cancel'
    };
  }



  // Not used right now - Method to get field component reference by templateRef name
  // getFieldComponent(templateRef: string): any {
  //   // This allows parent components to access specific field components
  //   // when they need to call methods like clearSelection(), etc.
  //   const selectComponent = this.selectComponents.find((_, index) => {
  //     const field = this.dialogConfig.fields.filter((f: FieldConfig) => f.type === 'select')[index];
  //     return field?.templateRef === templateRef;
  //   });

  //   if (selectComponent) return selectComponent;

  //   const treeSelectComponent = this.treeSelectComponents.find((_, index) => {
  //     const field = this.dialogConfig.fields.filter((f: FieldConfig) => f.type === 'treeselect')[index];
  //     return field?.templateRef === templateRef;
  //   });

  //   return treeSelectComponent;
  // }

  get dialogHeader(): string {
    const entityLabel = this.dialogConfig.handlerConfig?.entityLabel;

    if (this.editMode) {
      return this.dialogConfig.editHeader ||
        (entityLabel ? `Sửa ${entityLabel}` : 'Chỉnh sửa');
    }
    return this.dialogConfig.createHeader ||
      (entityLabel ? `Thêm ${entityLabel}` : 'Thêm mới');
  }

  get dialogWidth(): string {
    return this.dialogConfig.width || '650px';
  }


}
