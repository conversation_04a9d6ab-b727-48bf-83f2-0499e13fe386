import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { FULL_ROUTES } from '@shared/constants';
import { UserType } from '@shared/enums/user-type.enum';
import { jwtDecode } from 'jwt-decode';
import { Observable, Subject, tap, firstValueFrom } from 'rxjs';
import { ErrorHandlerService } from './error-handler.service';
import { LocalStorageService, STORAGE_KEY } from './local-storage.service';
import { EnvironmentService } from './environment.service';

export interface JwtTokenData {
    exp?: number;
    iat?: number;
    sub?: string;
    perKey?: string;
    type?: UserType;
}

@Injectable({ providedIn: 'root' })
export class AuthService {
    private userSubject = new Subject<any>();
    user$ = this.userSubject.asObservable();

    constructor(
        private httpClient: HttpClient,
        private errorHandlerService: ErrorHandlerService,
        private localStorageService: LocalStorageService,
        private router: Router,
        private environmentService: EnvironmentService
    ) { }

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/auth`;
    }

    private get userUrl(): string {
        return `${this.environmentService.getCurrentApiUrl()}/users`;
    }

    login(username: string, password: string): Observable<void> {
        return this.httpClient.post<any>(`${this.url}/login`, { username, password }).pipe(
            tap((response: any) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.localStorageService.saveData(STORAGE_KEY.TOKEN, data?.token);

                    // Redirect based on user type
                    const userType = this.getUserType();
                    if (userType === UserType.EMPLOYEE) {
                        this.router.navigateByUrl(FULL_ROUTES.ADMIN);
                    } else if (userType === UserType.LEARNER) {
                        this.router.navigateByUrl(FULL_ROUTES.LEARNER);
                    } else {
                        this.router.navigateByUrl('/');
                    }
                }
            })
        );
    }

    logout() {
        this.localStorageService.clearData();
        this.router.navigateByUrl(FULL_ROUTES.LOGIN);
    }

    isTokenExpired(): boolean {
        const authToken = this.localStorageService.getData(STORAGE_KEY.TOKEN);
        if (!authToken) {
            return true;
        }
        try {
            const decodedToken: JwtTokenData = jwtDecode(authToken);
            return !decodedToken.exp || decodedToken.exp * 1000 < Date.now();
        } catch (e) {
            return true;
        }

    }

    getPermissionsKey(): string | null {
        const authToken = this.localStorageService.getData(STORAGE_KEY.TOKEN);
        if (!authToken) {
            return null;
        }
        try {
            const decodedToken: JwtTokenData = jwtDecode(authToken);
            return decodedToken.perKey || null;
        } catch (e) {
            return null;
        }
    }

    hasPermission(permissionKey: string): boolean {
        const permissionsKey = this.getPermissionsKey();
        if (!permissionsKey) {
            return false;
        }
        const permissionRegex = new RegExp(`(^|,)${permissionKey}($|,)`);
        return permissionRegex.test(permissionsKey);
    }

    hasAnyPermission(permissions: string[]): boolean {
        return permissions.some(permission => this.hasPermission(permission));
    }

    getUserType(): UserType | null {
        const authToken = this.localStorageService.getData(STORAGE_KEY.TOKEN);
        if (!authToken) {
            return null;
        }
        try {
            const decodedToken: JwtTokenData = jwtDecode(authToken);
            return decodedToken.type || null;
        } catch (e) {
            return null;
        }
    }

    getUserInfo() {
        return this.httpClient.get<any>(`${this.userUrl}/me`);
    }

    async hasAdminRole(): Promise<boolean> {
        try {
            const response = await firstValueFrom(this.getUserInfo());
            return response?.data?.isAdmin || false;
        } catch (error) {
            return false;
        }
    }

    forgotPassword(username: string, email: string): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/forgot-password`, { username, email });
    }

    resetPassword(code: string, newPassword: any): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/reset-password`, { code, newPassword });
    }

    changePassword(oldPassword: any, newPassword: any): Observable<any> {
        return this.httpClient.put<any>(`${this.userUrl}/change-password`, { oldPassword, newPassword });
    }
}
