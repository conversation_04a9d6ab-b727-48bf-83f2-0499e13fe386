import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

@Schema({ versionKey: false, timestamps: true })
export class EmployeePositionHistoryDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ index: true, required: true })
    employeePositionId: string;
    @Prop({ index: true, required: true })
    departmentId: string;
    @Prop({ index: true, required: true })
    fromDate: Date;
    @Prop({ index: true, default: null })
    toDate: Date;
}
export const EmployeePositionHistorySchema = SchemaFactory.createForClass(EmployeePositionHistoryDocument);
