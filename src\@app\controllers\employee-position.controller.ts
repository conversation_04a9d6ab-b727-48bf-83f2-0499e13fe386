import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { AssignEmployeePositionDTO, JwtPayloadDTO, RetireDTO, SearchOptionsDTO, UpdateEmployeePositionDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { EmployeePositionService } from '@app/services';
import { CustomValidationPipe, PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard)
@Controller('employee-positions')
export class EmployeePositionController {
    constructor(private employeePositionService: EmployeePositionService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Get()
    async getWithPagination(@Query(new PaginationPipe()) request: PaginationRequest) {
        return await this.employeePositionService.findWithPagination(request);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.employeePositionService.getEmployeePositionOptions(request, body);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    @Post()
    async assignPositionToEmployee(@Body(new CustomValidationPipe()) dto: AssignEmployeePositionDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.employeePositionService.assignPositionToEmployee(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    @Put()
    async reassignEmployeeToTeam(@Body(new CustomValidationPipe()) dto: UpdateEmployeePositionDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.employeePositionService.updateEmployeePosition(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    @Put('retire')
    async retireEmployeePosition(@Body(new CustomValidationPipe()) dto: RetireDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.employeePositionService.retireEmployeePosition(dto, logged);
    }
}
