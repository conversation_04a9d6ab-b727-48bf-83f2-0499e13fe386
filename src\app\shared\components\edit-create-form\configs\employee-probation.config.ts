import { FormConfig } from '../edit-create-form.interfaces';
import { PROBATION_STATUS_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';

export const employeeProbationFormConfig = (): FormConfig => {
  return {
    fields: [
      {
        key: 'status',
        label: 'Trạng thái',
        type: 'select',
        required: true,
        width: 'full',
        config: { ...PROBATION_STATUS_SELECT_CONFIG },
        customErrorMessage: 'Vui lòng chọn trạng thái'
      },
      {
        key: 'isManual',
        label: null,
        labelPosition: 'inline',
        type: 'toggleSwitch',
        width: 'full',
        placeholder: 'Set kết thúc thử việc bằng tay'
      },
      {
        key: 'toDate',
        label: '<PERSON><PERSON>y kết thúc thử việc',
        type: 'datepicker',
        required: true,
        width: 'half',
        placeholder: 'Chọn ngày kết thúc thử việc',
        disabled: (editMode: boolean, formValue: any) => !formValue?.isManual,
        customErrorMessage: 'Vui lòng chọn ngày kết thúc thử việc'
      },
      {
        key: 'deadline',
        label: 'Hạn đánh giá',
        type: 'datepicker',
        required: true,
        width: 'half',
        placeholder: 'Chọn hạn đánh giá',
        disabled: (editMode: boolean, formValue: any) => !formValue?.isManual,
        customErrorMessage: 'Vui lòng chọn hạn đánh giá'
      }
    ]
  };
};
