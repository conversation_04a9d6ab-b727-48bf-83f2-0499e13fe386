import { Logged } from '@app/decorators';
import { JwtPayloadDTO, ProfileDTO } from '@app/models/dto';
import { TeleBotService } from '@app/shared/services';
import { ProfileRepository } from '@database/mongodb/repositories';
import { IProfileDocument } from '@database/mongodb/schemas';
import { Injectable, Logger } from '@nestjs/common';
@Injectable()
export class ProfileService {
    private readonly logger = new Logger(ProfileService.name);

    constructor(
        private profileRepository: ProfileRepository,
        private teleBotService: TeleBotService,
    ) {}

    async createProfiles(data: IProfileDocument[], @Logged() logged: JwtPayloadDTO) {
        this.logger.debug(`Create createProfiles`);
        await this.profileRepository.insertMany(
            data.map(i => {
                return { ...i, createdBy: logged?.username, updatedBy: logged?.username };
            }),
        );
    }

    async updateProfile(data: ProfileDTO, id: string, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug(`Update profile ${id}`);
        await this.profileRepository.updateOne(
            { id },
            {
                ...data,
                updatedBy: logged.username,
            },
        );
    }

    async deleteProfile(id: string, @Logged() logged: JwtPayloadDTO) {
        this.logger.debug(`Delete profile ${id}`);
        await this.profileRepository.updateOne(
            { id },
            {
                deletedAt: new Date(),
                deletedBy: logged.username,
            },
        );
    }
}
