import { Injectable } from '@angular/core';
import { PopoverConfig, PopoverConfigFactory, PopoverEntityType } from '@shared/components/generic-popover';
@Injectable({
  providedIn: 'root'
})
export class PopoverConfigService {
  // Lightweight registry for dynamic imports - only loads configs when actually used
  private readonly configRegistry: Record<PopoverEntityType, () => Promise<PopoverConfigFactory>> = {
    'employeeRetire': () => import('../components/generic-popover').then(m => m.employeeRetirePopoverConfig),
    // Add more configs here as needed - each will be lazy loaded
  };

  /**
   * Gets popover configuration for a specific entity type with lazy loading
   * @param entityType The type of entity popover to create
   * @param onSuccess Callback function called after successful operation
   * @param onCancel Callback function called when popover is cancelled
   * @returns Promise that resolves to complete popover configuration
   */
  async getPopoverConfig(
    entityType: PopoverEntityType,
    onSuccess: () => void,
    onCancel: () => void
  ): Promise<PopoverConfig> {
    const configLoader = this.configRegistry[entityType];
    if (!configLoader) {
      throw new Error(`Unsupported entity type: ${entityType}. Available types: ${Object.keys(this.configRegistry).join(', ')}`);
    }

    const configFactory = await configLoader();
    return configFactory(onSuccess, onCancel);
  }

}