import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

@Schema({ versionKey: false, timestamps: true })
export class PositionDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ required: true })
    name: string;
    @Prop({ required: true })
    departmentId: string;
    @Prop({ index: true, required: true })
    roleId: string;
}
export const PositionSchema = SchemaFactory.createForClass(PositionDocument);
