#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
    tsConfigPath: './tsconfig.spec.json',
    outDir: './out-tsc/spec',
    specPattern: 'src/**/*.spec.ts',
    browser: 'ChromeHeadless'
};

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
}

function runCommand(command, args, options = {}) {
    return new Promise((resolve, reject) => {
        log(`Running: ${command} ${args.join(' ')}`, colors.cyan);
        
        const child = spawn(command, args, {
            stdio: 'inherit',
            shell: true,
            ...options
        });

        child.on('close', (code) => {
            if (code === 0) {
                resolve();
            } else {
                reject(new Error(`Command failed with exit code ${code}`));
            }
        });

        child.on('error', (error) => {
            reject(error);
        });
    });
}

async function compileTypeScript() {
    log('📦 Compiling TypeScript...', colors.blue);
    
    try {
        await runCommand('npx', ['tsc', '-p', config.tsConfigPath]);
        log('✅ TypeScript compilation successful', colors.green);
    } catch (error) {
        log('❌ TypeScript compilation failed', colors.red);
        throw error;
    }
}

async function runTests() {
    log('🧪 Running tests...', colors.blue);
    
    try {
        // Use karma to run the compiled tests
        await runCommand('npx', [
            'karma', 'start',
            '--single-run',
            '--browsers', config.browser,
            '--reporters', 'progress'
        ]);
        log('✅ Tests completed successfully', colors.green);
    } catch (error) {
        log('❌ Tests failed', colors.red);
        throw error;
    }
}

async function runTestsWithCoverage() {
    log('🧪 Running tests with coverage...', colors.blue);
    
    try {
        await runCommand('npx', [
            'karma', 'start',
            '--single-run',
            '--browsers', config.browser,
            '--reporters', 'progress,coverage',
            '--code-coverage'
        ]);
        log('✅ Tests with coverage completed successfully', colors.green);
    } catch (error) {
        log('❌ Tests with coverage failed', colors.red);
        throw error;
    }
}

async function watchTests() {
    log('👀 Running tests in watch mode...', colors.blue);
    
    try {
        await runCommand('npx', [
            'karma', 'start',
            '--browsers', config.browser,
            '--auto-watch'
        ]);
    } catch (error) {
        log('❌ Watch mode failed', colors.red);
        throw error;
    }
}

async function main() {
    const args = process.argv.slice(2);
    const mode = args[0] || 'test';

    try {
        // Always compile first
        await compileTypeScript();

        switch (mode) {
            case 'test':
                await runTests();
                break;
            case 'coverage':
                await runTestsWithCoverage();
                break;
            case 'watch':
                await watchTests();
                break;
            case 'compile-only':
                log('✅ Compilation only completed', colors.green);
                break;
            default:
                log(`Unknown mode: ${mode}`, colors.red);
                log('Available modes: test, coverage, watch, compile-only', colors.yellow);
                process.exit(1);
        }
    } catch (error) {
        log(`Error: ${error.message}`, colors.red);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { compileTypeScript, runTests, runTestsWithCoverage, watchTests };
