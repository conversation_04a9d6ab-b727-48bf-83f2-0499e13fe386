<!-- Main table container -->
<div class="flex flex-col w-full">
    <generic-p-table [config]="employeeLazyScrollPTableConfig"
                     [data]="employees"
                     [totalRecords]="totalRecords"
                     [(selection)]="selectedEmployeeRow"
                     (onDataLoad)="onTableDataLoaded($event)">
        <!-- Custom template for any custom column -->
        <ng-template #customColumnTemplate let-employee let-field="field" let-column="column">
            <!-- Employee positions -->
            <div *ngIf="field === 'employeePositions'" class="flex flex-col gap-2">
                <div *ngIf="employee.employeePositions && employee.employeePositions.length > 0; else noPositions" class="flex flex-col gap-2">
                    <div *ngFor="let position of employee.employeePositions" class="flex flex-col gap-1">
                        <div class="flex gap-2">
                            <span class="pt-[2px]">{{ position.position[0].name || '' }}</span>
                            <generic-p-tag [value]="position.department[0].name || ''"
                                           [styleClass]="'w-fit max-w-[160px]'" />
                            <generic-p-tag [value]="position.status"
                                           type="employeePositionStatus"
                                           [styleClass]="'w-fit max-w-[120px]'" />
                        </div>
                    </div>
                </div>
                <ng-template #noPositions>
                    <span class="text-gray-500 text-sm">Chưa có vị trí</span>
                </ng-template>
            </div>
        </ng-template>
    </generic-p-table>
</div>

<!-- Drawer for employee details -->
<p-drawer [(visible)]="drawerVisible"
          position="right"
          [style]="{ width: '50%' }"
          header="Chi tiết nhân viên"
          (onHide)="closeDrawer()">
    <app-employee-detail [employeeId]="selectedEmployeeRow?.id"></app-employee-detail>
</p-drawer>

<!-- Employee Creation Stepper -->
<generic-p-stepper [(visible)]="employeeCreationStepper"
                   [stepperConfig]="employeeCreationStepperConfig"
                   [initialData]="selectedEmployee"
                   [editMode]="editMode"
                   (onComplete)="onStepperSuccess()"
                   (onCancel)="onStepperCancel()">
</generic-p-stepper>

<!-- Edit Employee Dialog -->
<edit-create-dialog [(visible)]="editEmployeeDialog"
                    [editMode]="true"
                    [dialogConfig]="editEmployeeDialogConfig"
                    [initialData]="selectedEmployeeForEdit">
</edit-create-dialog>