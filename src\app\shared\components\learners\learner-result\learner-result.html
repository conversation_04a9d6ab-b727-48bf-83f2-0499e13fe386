<ng-container *ngFor="let item of listData">
    <div class="card timeline-item shadow" [style.borderColor]="colorLevelTag(item?.courseLevel)?.bgColor">
        <div class="timeline-item-header">
            <div class="title-container">
                <ng-container *ngIf="item?.courseLevel">
                    <span class="level-tag" [style.backgroundColor]="colorLevelTag(item?.courseLevel)?.bgColor" [style.color]="colorLevelTag(item?.courseLevel).textColor">{{ item?.courseLevel }}</span>
                </ng-container>

                {{ item?.courseName }}
                <generic-p-tag [value]="item?.courseInfo?.teacher || ''"/>
            </div>
            <div class="time-container">
                {{ getStartDate(item) | date: dateFormat }}
                <ng-container *ngIf="getEndDate(item)"> – {{ getEndDate(item) | date: dateFormat }}</ng-container>
            </div>
        </div>
        <div class="timeline-item-body">
            <div class="event-item" *ngFor="let i of item.details">
                <div class="flex items-center gap-4">
                    <strong>{{ i.eventDate | date: dateFormat }}: </strong>
                    {{ mappingEventLabel(i.eventType) }}
                    <span *ngIf="i?.outcome" class="outcome-tag" [style.backgroundColor]="outcomeSetting(i?.outcome)?.bgColor" [style.color]="outcomeSetting(i?.outcome)?.textColor">{{ outcomeSetting(i?.outcome)?.name }}</span>
                    <generic-p-tag *ngIf="hasTestViewPermission() && i.testCode !== null" [value]="i.testCode || ''"/>                          
                </div>
                <ng-container *ngIf="isEventTest(i.eventType)">
                    
                    <test-result-table [result]="i.result" [note]="i.note"></test-result-table>
                </ng-container>
            </div>
        </div>
    </div>
</ng-container>
