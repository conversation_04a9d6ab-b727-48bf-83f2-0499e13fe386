import { Directive, Input, OnInit, Optional } from '@angular/core';
import { Table } from 'primeng/table';
import { TrackByFunction } from '@angular/core';

/**
 * Directive for configuring common p-table properties with virtual scrolling
 * Usage: <p-table defaultLazyScrollPTable [virtualScrollItemSize]="46.75" [scrollHeight]="calc(100vh - 20rem)" />
 */
@Directive({
    selector: 'p-table[defaultLazyScrollPTable]',
    host: {
        'class': 'flex flex-col flex-1 min-h-0'
    }
})
export class DefaultLazyScrollTableDirective<T> implements OnInit {
    @Input() scrollable: boolean = true;
    @Input() virtualScroll: boolean = true;
    @Input() virtualScrollItemSize: number = 46.75;
    @Input() scrollHeight: string = 'calc(100vh - 20rem)';
    @Input() styleClass: string = 'flex flex-col';
    @Input() virtualScrollOptions: {} = { numToleratedItems: 5 };

    // Special to get lazy from host p-table
    private _lazy = true;
    private lazyWasBound = false;
    @Input()
    set lazy(v: boolean) {
        this._lazy = v;
        this.lazyWasBound = true;
    }
    get lazy(): boolean {
        return this._lazy;
    }

    // Special to get rowTrackBy from host p-table
    private _rowTrackBy: TrackByFunction<T> = this.defaultTrackByFn;
    private rowTrackByWasBound = false;
    @Input()
    set rowTrackBy(fn: TrackByFunction<T>) {
        this._rowTrackBy = fn;
        this.rowTrackByWasBound = true;
    }
    get rowTrackBy(): TrackByFunction<T> {
        return this._rowTrackBy;
    }

    constructor(
        @Optional() private table: Table
    ) { }

    private defaultTrackByFn(index: number, item: any): any {
        return item ? item.id : index;
    }

    ngOnInit() {
        if (!this.table) {
            console.warn('virtualScrollTable directive used outside of a p-table');
            return;
        }

        if (!this.lazyWasBound) {
            this.table.lazy = this._lazy;  // = true
        }

        if (this.table.virtualScrollOptions === undefined) {
            this.table.virtualScrollOptions = this.virtualScrollOptions;
        }

        if (this.table.scrollable === undefined) {
            this.table.scrollable = this.scrollable;
        }

        if (this.table.virtualScroll === undefined) {
            this.table.virtualScroll = this.virtualScroll;
        }

        if (this.table.virtualScrollItemSize === undefined) {
            this.table.virtualScrollItemSize = this.virtualScrollItemSize;
        }

        if (!this.rowTrackByWasBound) {
            this.table.rowTrackBy = this._rowTrackBy;
        }

        if (this.table.scrollHeight === undefined) {
            this.table.scrollHeight = this.scrollHeight;
        }

        if (this.table.styleClass === undefined) {
            this.table.styleClass = this.styleClass;
        }
    }
}