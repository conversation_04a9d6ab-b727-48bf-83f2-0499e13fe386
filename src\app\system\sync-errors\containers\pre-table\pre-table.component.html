<p-table [value]="data" [scrollable]="true" scrollHeight="400px" responsiveLayout="scroll">
    <ng-template pTemplate="header">
        <tr>
            <th pSortableColumn="eventDate">
                <div class="flex items-center gap-1">
                    eventDate
                    <p-sortIcon field="eventDate"/>
                </div>
            </th>
            <th pSortableColumn="testType">
                <div class="flex items-center gap-1">
                    testType
                    <p-sortIcon field="testType"/>
                </div>
            </th>
            <th pSortableColumn="courseCodeLevel">
                <div class="flex items-center gap-1">
                    courseCodeLevel
                    <p-sortIcon field="courseCodeLevel"/>
                </div>
            </th>
            <th pSortableColumn="test">
                <div class="flex items-center gap-1">
                    test
                    <p-sortIcon field="test"/>
                </div>
            </th>
            <th pSortableColumn="testCode">
                <div class="flex items-center gap-1">
                    testCode
                    <p-sortIcon field="testCode"/>
                </div>
            </th>
            <th pSortableColumn="learnerCode">
                <div class="flex items-center gap-1">
                    learnerCode
                    <p-sortIcon field="learnerCode"/>
                </div>
            </th>
            <th pSortableColumn="rRaw">
                <div class="flex items-center gap-1">
                    rRaw
                    <p-sortIcon field="rRaw"/>
                </div>
            </th>
            <th pSortableColumn="rBand">
                <div class="flex items-center gap-1">
                    rBand
                    <p-sortIcon field="rBand"/>
                </div>
            </th>
            <th pSortableColumn="lRaw">
                <div class="flex items-center gap-1">
                    lRaw
                    <p-sortIcon field="lRaw"/>
                </div>
            </th>
            <th pSortableColumn="lBand">
                <div class="flex items-center gap-1">
                    lBand
                    <p-sortIcon field="lBand"/>
                </div>
            </th>
            <th pSortableColumn="wRaw">
                <div class="flex items-center gap-1">
                    wRaw
                    <p-sortIcon field="wRaw"/>
                </div>
            </th>
            <th pSortableColumn="wBand">
                <div class="flex items-center gap-1">
                    wBand
                    <p-sortIcon field="wBand"/>
                </div>
            </th>
            <th pSortableColumn="sRaw">
                <div class="flex items-center gap-1">
                    sRaw
                    <p-sortIcon field="sRaw"/>
                </div>
            </th>
            <th pSortableColumn="sBand">
                <div class="flex items-center gap-1">
                    sBand
                    <p-sortIcon field="sBand"/>
                </div>
            </th>
            <th pSortableColumn="ovrRaw">
                <div class="flex items-center gap-1">
                    ovrRaw
                    <p-sortIcon field="ovrRaw"/>
                </div>
            </th>
            <th pSortableColumn="ovrRnd">
                <div class="flex items-center gap-1">
                    ovrRnd
                    <p-sortIcon field="ovrRnd"/>
                </div>
            </th>
            <th pSortableColumn="outcome">
                <div class="flex items-center gap-1">
                    outcome
                    <p-sortIcon field="outcome"/>
                </div>
            </th>
            <th style="min-width: 15rem" alignFrozen="right" pSortableColumn="error" pFrozenColumn [frozen]="true">
                error
                <p-sortIcon field="error"/>
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-childItem>
        <tr>
            <td>
                {{ childItem.eventDate }}
            </td>
            <td>
                {{ childItem.testType }}
            </td>
            <td>
                {{ childItem.courseCodeLevel }}
            </td>
            <td>
                {{ childItem.test }}
            </td>
            <td>
                {{ childItem.testCode }}
            </td>
            <td>
                {{ childItem.learnerCode }}
            </td>
            <td>
                {{ childItem.rRaw }}
            </td>
            <td>
                {{ childItem.rBand }}
            </td>
            <td>
                {{ childItem.lRaw }}
            </td>
            <td>
                {{ childItem.lBand }}
            </td>
            <td>
                {{ childItem.wRaw }}
            </td>
            <td>
                {{ childItem.wBand }}
            </td>
            <td>
                {{ childItem.sRaw }}
            </td>
            <td>
                {{ childItem.sBand }}
            </td>
            <td>
                {{ childItem.ovrRaw }}
            </td>
            <td>
                {{ childItem.ovrRnd }}
            </td>
            <td>
                {{ childItem.outcome }}
            </td>
            <td style="min-width: 20rem" alignFrozen="right" pFrozenColumn [frozen]="true">
                {{ childItem.error }}
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="numberOfCols">Không có dữ liệu</td>
        </tr>
    </ng-template>
</p-table>