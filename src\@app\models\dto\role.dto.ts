import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsNotEmpty, IsString } from 'class-validator';

export class RoleDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toLowerCase())
    readonly code: string;

    @ApiProperty({ description: 'name' })
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @ApiProperty({ description: 'permissions' })
    @IsArray()
    readonly permissions: string[];
}
