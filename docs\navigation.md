# Documentation Navigation Guide

This guide helps you quickly find the documentation you need based on your current task or role.

## 🎯 Quick Navigation by Task

### I want to...

#### 🆕 Get Started with the Project
1. [Project Overview](./architecture/project-overview.md)
2. [Getting Started](./architecture/getting-started.md)
3. [Project Structure](./architecture/project-structure.md)

#### 🧩 Use an Existing Component
1. [Components Overview](./components/README.md)
2. Find your component:
   - [Edit-Create Dialog](./components/edit-create-dialog.md) - For CRUD dialogs
   - [Generic Stepper](./components/generic-stepper.md) - For multi-step workflows
   - [Generic P-Table](./components/generic-p-table.md) - For data tables
   - [Generic Popover](./components/generic-popover.md) - For quick forms
3. [Configuration Examples](./examples/configuration-examples.md)

#### 🔧 Configure a Component
1. [Services Overview](./services/README.md)
2. Find the relevant config service:
   - [Edit-Create Form Config](./services/edit-create-form-config.md)
   - [Popover Config](./services/popover-config.md)
3. [Common Patterns](./examples/common-patterns.md)

#### 🐛 Fix an Issue
1. [Troubleshooting Guide](./deployment/troubleshooting.md)
2. Component-specific troubleshooting sections
3. [Testing Guidelines](./testing/testing-guidelines.md)

#### 🧪 Write Tests
1. [Testing Guidelines](./testing/testing-guidelines.md)
2. [Unit Testing Examples](./testing/unit-testing.md)
3. [Integration Testing](./testing/integration-testing.md)

#### 🚀 Deploy or Build
1. [Build & Deployment](./deployment/build-deployment.md)
2. [Performance Optimization](./deployment/performance.md)

## 👥 Navigation by Role

### Frontend Developer
**Daily Tasks:**
- [Components Overview](./components/README.md)
- [Common Patterns](./examples/common-patterns.md)
- [Configuration Examples](./examples/configuration-examples.md)

**When Stuck:**
- [Troubleshooting Guide](./deployment/troubleshooting.md)
- Component-specific documentation
- [Best Practices](./examples/best-practices.md)

### New Team Member
**Week 1:**
1. [Project Overview](./architecture/project-overview.md)
2. [Getting Started](./architecture/getting-started.md)
3. [Development Guidelines](./architecture/development-guidelines.md)

**Week 2:**
1. [Components Overview](./components/README.md)
2. [Services Overview](./services/README.md)
3. [Common Patterns](./examples/common-patterns.md)

### Tech Lead / Architect
**Architecture Review:**
- [Project Structure](./architecture/project-structure.md)
- [Component Architecture](./components/README.md#component-architecture)
- [Service Architecture](./services/README.md#service-architecture)

**Code Quality:**
- [Best Practices](./examples/best-practices.md)
- [Testing Guidelines](./testing/testing-guidelines.md)
- [Performance Optimization](./deployment/performance.md)

### QA Engineer
**Testing Focus:**
- [Testing Guidelines](./testing/testing-guidelines.md)
- [Component Testing](./testing/unit-testing.md)
- [Integration Testing](./testing/integration-testing.md)

**Understanding Components:**
- [Components Overview](./components/README.md)
- [Usage Examples](./examples/configuration-examples.md)

## 🔍 Search by Topic

### Forms & Dialogs
- [Edit-Create Dialog System](./components/edit-create-dialog.md)
- [Edit-Create Form Component](./components/edit-create-form.md)
- [Form Handler Service](./services/form-handler.md)
- [Form Configuration](./services/edit-create-form-config.md)

### Data Display
- [Generic P-Table Component](./components/generic-p-table.md)
- [Generic Popover Component](./components/generic-popover.md)
- [Data Display Patterns](./examples/common-patterns.md#data-display)

### Multi-Step Workflows
- [Generic Stepper Component](./components/generic-stepper.md)
- [Stepper Configuration](./services/stepper-config.md)
- [Workflow Patterns](./examples/common-patterns.md#workflows)

### Configuration Management
- [Services Overview](./services/README.md)
- [Configuration Patterns](./examples/common-patterns.md#configuration)
- [Best Practices](./examples/best-practices.md#configuration)

### Testing & Quality
- [Testing Guidelines](./testing/testing-guidelines.md)
- [Unit Testing](./testing/unit-testing.md)
- [Integration Testing](./testing/integration-testing.md)
- [Performance](./deployment/performance.md)

## 📱 Mobile-Friendly Navigation

### Quick Links
- [📚 Main Index](./README.md)
- [🧩 Components](./components/README.md)
- [🔧 Services](./services/README.md)
- [📋 Examples](./examples/README.md)
- [🧪 Testing](./testing/README.md)

### Emergency Links
- [🚨 Troubleshooting](./deployment/troubleshooting.md)
- [🔧 Quick Fixes](./deployment/troubleshooting.md#quick-fixes)
- [📞 Support Contacts](./deployment/troubleshooting.md#support)

## 🔗 External Resources

### Angular Resources
- [Angular Documentation](https://angular.io/docs)
- [Angular CLI](https://angular.io/cli)
- [Angular Testing](https://angular.io/guide/testing)

### PrimeNG Resources
- [PrimeNG Documentation](https://primeng.org/)
- [PrimeNG Components](https://primeng.org/components)
- [PrimeNG Themes](https://primeng.org/theming)

### Development Tools
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [RxJS Documentation](https://rxjs.dev/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)

---

**💡 Tip:** Bookmark this page for quick access to all documentation sections!

**🔄 Last Updated:** 2025-07-17
