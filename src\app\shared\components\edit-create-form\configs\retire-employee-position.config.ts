import { FormConfig } from '../edit-create-form.interfaces';

export const retireEmployeePositionFormConfig = (): FormConfig => {
  return {
    fields: [
      {
        key: 'id', label: 'ID', type: 'text', width: 'full', hidden: true
      },
      {
        key: 'employee', label: 'Nhân viên', labelPosition: 'inline', width: 'full',
        type: 'static', value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
      },
      {
        key: 'position', label: 'Vị trí', labelPosition: 'inline', width: 'full',
        type: 'static', value: (data: any) => data.position?.name || ''
      },
      {
        key: 'toDate', label: 'Ngày nghỉ việc', required: true, width: 'full',
        type: 'datepicker', placeholder: 'Chọn ngày nghỉ việc'
      }
    ]
  };
};
