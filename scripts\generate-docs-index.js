#!/usr/bin/env node

/**
 * Documentation Index Generator
 * 
 * This script generates an index of all documentation files in the docs/ directory
 * and creates cross-reference links between related documents.
 */

const fs = require('fs');
const path = require('path');

const DOCS_DIR = path.join(__dirname, '..', 'docs');
const OUTPUT_FILE = path.join(DOCS_DIR, 'index.md');

/**
 * Recursively find all markdown files in a directory
 */
function findMarkdownFiles(dir, basePath = '') {
  const files = [];
  const items = fs.readdirSync(dir);

  for (const item of items) {
    const fullPath = path.join(dir, item);
    const relativePath = path.join(basePath, item);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      files.push(...findMarkdownFiles(fullPath, relativePath));
    } else if (item.endsWith('.md')) {
      files.push({
        name: item,
        path: relativePath.replace(/\\/g, '/'),
        fullPath: fullPath,
        directory: basePath || 'root'
      });
    }
  }

  return files;
}

/**
 * Extract title from markdown file
 */
function extractTitle(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    
    for (const line of lines) {
      if (line.startsWith('# ')) {
        return line.substring(2).trim();
      }
    }
    
    // Fallback to filename
    return path.basename(filePath, '.md')
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  } catch (error) {
    console.warn(`Could not read file: ${filePath}`);
    return path.basename(filePath, '.md');
  }
}

/**
 * Generate the documentation index
 */
function generateIndex() {
  console.log('🔍 Scanning documentation files...');
  
  const files = findMarkdownFiles(DOCS_DIR);
  const filesByDirectory = {};

  // Group files by directory
  files.forEach(file => {
    if (!filesByDirectory[file.directory]) {
      filesByDirectory[file.directory] = [];
    }
    filesByDirectory[file.directory].push({
      ...file,
      title: extractTitle(file.fullPath)
    });
  });

  // Generate index content
  let indexContent = `# Documentation Index

This is an automatically generated index of all documentation files in the project.

**Generated on:** ${new Date().toISOString().split('T')[0]}  
**Total Files:** ${files.length}

## 📚 Documentation Structure

`;

  // Sort directories for consistent output
  const sortedDirectories = Object.keys(filesByDirectory).sort();

  for (const directory of sortedDirectories) {
    const dirFiles = filesByDirectory[directory];
    
    if (directory === 'root') {
      indexContent += `### 📁 Root Documentation\n\n`;
    } else {
      const dirName = directory.split('/').pop();
      const displayName = dirName.charAt(0).toUpperCase() + dirName.slice(1);
      indexContent += `### 📁 ${displayName}\n\n`;
    }

    // Sort files within directory
    dirFiles.sort((a, b) => {
      // README files first
      if (a.name === 'README.md') return -1;
      if (b.name === 'README.md') return 1;
      return a.title.localeCompare(b.title);
    });

    for (const file of dirFiles) {
      const link = file.path.startsWith('./') ? file.path : `./${file.path}`;
      indexContent += `- [${file.title}](${link})\n`;
    }
    
    indexContent += '\n';
  }

  // Add quick navigation section
  indexContent += `## 🚀 Quick Navigation

### By Category
- **Components:** [Components Overview](./components/README.md)
- **Services:** [Services Overview](./services/README.md)
- **Examples:** [Examples & Patterns](./examples/README.md)
- **Testing:** [Testing Guidelines](./testing/README.md)

### By Task
- **Getting Started:** [Project Overview](./architecture/project-overview.md)
- **Component Usage:** [Component Integration](./examples/component-integration.md)
- **Configuration:** [Configuration Examples](./examples/configuration-examples.md)
- **Troubleshooting:** [Troubleshooting Guide](./deployment/troubleshooting.md)

## 🔧 Maintenance

This index is automatically generated by running:
\`\`\`bash
node scripts/generate-docs-index.js
\`\`\`

To update the index after adding new documentation:
1. Add your new markdown files to the appropriate directory
2. Run the index generation script
3. Commit the updated index.md file

---

**💡 Tip:** Use the [Navigation Guide](./navigation.md) for task-based documentation discovery.
`;

  // Write the index file
  fs.writeFileSync(OUTPUT_FILE, indexContent);
  
  console.log(`✅ Documentation index generated: ${OUTPUT_FILE}`);
  console.log(`📊 Indexed ${files.length} files across ${sortedDirectories.length} directories`);
  
  return files.length;
}

/**
 * Validate documentation structure
 */
function validateStructure() {
  console.log('🔍 Validating documentation structure...');
  
  const requiredFiles = [
    'README.md',
    'components/README.md',
    'services/README.md'
  ];

  const missingFiles = [];
  
  for (const file of requiredFiles) {
    const filePath = path.join(DOCS_DIR, file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  }

  if (missingFiles.length > 0) {
    console.warn('⚠️  Missing required files:');
    missingFiles.forEach(file => console.warn(`   - ${file}`));
    return false;
  }

  console.log('✅ Documentation structure is valid');
  return true;
}

// Main execution
if (require.main === module) {
  console.log('📚 Documentation Index Generator');
  console.log('================================\n');
  
  try {
    validateStructure();
    const fileCount = generateIndex();
    
    console.log('\n🎉 Documentation index generation completed successfully!');
    console.log(`📖 View the index at: docs/index.md`);
    
  } catch (error) {
    console.error('❌ Error generating documentation index:', error.message);
    process.exit(1);
  }
}

module.exports = { generateIndex, validateStructure, findMarkdownFiles };
