// Generic Stepper Component Styles
.generic-p-stepper {
  .p-stepper {
    .p-step-list {
      .p-step {
        &.p-disabled {
          opacity: 0.6;
          pointer-events: none;
        }
      }
    }
    
    .p-step-panels {
      .p-step-panel {
        .step-content {
          min-height: 300px;
        }
        
        .step-actions {
          border-top: 1px solid var(--surface-border);
          padding-top: 1rem;
          margin-top: 1rem;
        }
      }
    }
  }
}

// Loading state styles
.step-loading {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1000;
  }
}
