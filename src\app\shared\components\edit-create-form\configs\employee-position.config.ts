import { inject } from '@angular/core';
import { FormConfig } from '../edit-create-form.interfaces';
import { DepartmentApiService, PositionApiService } from '@shared/services';
import { ROOT_DEPARTMENT_SELECT_CONFIG, POSITION_BY_DEPARTMENT_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';
import { DEPARTMENT_BY_PARENT_TREE_SELECT_CONFIG } from '@shared/components/generic-treeselect/generic-treeselect.configs';

export const employeePositionFormConfig = (): FormConfig => {
  const departmentApiService = inject(DepartmentApiService);
  const positionApiService = inject(PositionApiService);

  return {
    fields: [
      {
        key: 'rootDepartmentId', label: 'Phòng ban gốc', required: true, width: 'full',
        type: 'select', config: { ...ROOT_DEPARTMENT_SELECT_CONFIG, service: departmentApiService },
        customErrorMessage: 'Vui lòng chọn phòng ban gốc trước'
      },
      {
        key: 'positionId', label: 'Vị trí', required: true, width: 'half',
        type: 'select', config: { ...POSITION_BY_DEPARTMENT_SELECT_CONFIG, service: positionApiService },
        dependsOn: 'rootDepartmentId',
        customErrorMessage: 'Vui lòng chọn vị trí',
        disabled: (editMode: boolean, formValue: any) => !formValue?.rootDepartmentId,
      },
      {
        key: 'fromDate', label: 'Ngày nhận việc', required: true, width: 'half',
        type: 'datepicker', placeholder: 'Chọn ngày nhận việc',
        dependsOn: 'positionId',
        customErrorMessage: 'Vui lòng chọn ngày nhận việc',
        disabled: (editMode: boolean, formValue: any) => !formValue?.positionId
      },
      {
        key: 'departmentId', label: 'Team/Subdept', required: true, width: 'full',
        type: 'treeselect', config: { ...DEPARTMENT_BY_PARENT_TREE_SELECT_CONFIG, service: departmentApiService },
        dependsOn: 'rootDepartmentId',
        customErrorMessage: 'Vui lòng chọn team/subdept',
        disabled: (editMode: boolean, formValue: any) => !formValue?.rootDepartmentId,
      }
    ]
  };
};
