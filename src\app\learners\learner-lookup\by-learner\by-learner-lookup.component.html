<div class="flex flex-col gap-2 w-full">
    <label class="block text-surface-900 dark:text-surface-0 font-medium">T<PERSON><PERSON> kiếm</label>
    <!-- <span class="w-[8 0px]">T<PERSON><PERSON> kiếm</span> -->
    <!-- <h3><PERSON>ra c<PERSON>u học viên</h3> -->
    <p-inputgroup class="h-10">
        <p-inputgroup-addon>
            <i class="pi pi-search"></i>
        </p-inputgroup-addon>
        <p-autocomplete placeholder="Tì<PERSON> theo họ tên, ID học viên, EC, ID EC"
                        [(ngModel)]="value"
                        [suggestions]="suggestions"
                        [showClear]="true"
                        multiple="true"
                        (onSelect)="selectLearner($event)"
                        (completeMethod)="search($event)"
                        class="w-[500px] lg:w-[600px]"
                        styleClass="!h-10 !w-full"
                        inputStyleClass="!flex-none"
                        optionLabel="label"
                        appendTo="body">
            <ng-template let-item
                         pTemplate="selectedItem">
                <div class="flex items-center gap-2 justify-between w-full">
                    <div><span class="font-semibold">{{item.code}}</span> - {{ item.fullname }}</div>
                    <p-chip [label]="item.ec"
                            styleClass="text-sm !py-1" />
                </div>
            </ng-template>
            <ng-template let-item
                         #item>
                <div class="flex justify-between w-full">
                    <div><span class="font-semibold">{{item.code}}</span> - {{ item.fullname }}</div>
                    <p-chip [label]="item.ec"
                            styleClass="text-sm !py-1" />
                </div>
            </ng-template>
        </p-autocomplete>
    </p-inputgroup>
</div>
<hr>
<span class="opacity-80 italic"
      *ngIf="learnerData.length == 0">(Tìm kiếm & chọn học viên để hiển thị lịch sử người học)</span>
<div class="flex flex-col flex-1 min-h-0 overflow-y-auto">
    <learner-result [listData]="learnerData"
                    *ngIf="learnerData.length > 0"></learner-result>
</div>