import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '@shared/services';
import { MenuItem } from 'primeng/api';
import { MenubarModule } from 'primeng/menubar';
import { RippleModule } from 'primeng/ripple';

@Component({
    selector: 'app-employees',
    standalone: true,
    imports: [CommonModule, RouterModule, MenubarModule, RippleModule],
    templateUrl: './employees.component.html',
    styleUrl: './employees.component.scss'
})
export class EmployeesComponent {
    employeesMenu: MenuItem[] = [
        {
            key: 'employees',
            label: 'Danh sách',
            icon: 'pi pi-users',
            routerLink: ['/hr/employees/list']
        },
        {
            key: 'employees-positions',
            label: 'Lịch sử công tác',
            icon: 'pi pi-history',
            routerLink: ['/hr/employees/positions']
        },
        {
            key: 'employees-departments',
            label: 'Phân team',
            icon: 'pi pi-sitemap',
            routerLink: ['/hr/employees/departments']
        },
        {
            key: 'employees-probation',
            label: 'Thử việc',
            icon: 'pi pi-clock',
            routerLink: ['/hr/employees/probation']
        }
    ];

    constructor(private router: Router, private authService: AuthService) {}
}
