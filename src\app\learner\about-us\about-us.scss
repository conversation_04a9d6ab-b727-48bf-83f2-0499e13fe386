.about-us {
  background-color: #f9f9f9;
  line-height: 1.8;

  .header {
    position: relative;
    background: linear-gradient(45deg, #01255b 20%, #fcd60d 110%);
    color: #ffff;
    text-align: center;
    padding: 70px 20px 110px;
    overflow: hidden; /* So wave won't overflow horizontally */
    font-size: 1.1rem;

    &-title {
      font-size: 2.5rem;
      font-weight: 700;
      letter-spacing: 1px;
      margin-bottom: 10px;
      text-transform: uppercase;
    }

    /* Decorative wave at the bottom of the hero */
    &::after {
      content: "";
      display: block;
      width: 100%;
      height: 90px;
      position: absolute;
      bottom: 0;
      left: 0;
      background: url("data:image/svg+xml,%3Csvg viewBox='0 0 1440 320' preserveAspectRatio='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23f9f9f9' fill-opacity='1' d='M0,160L60,170.7C120,181,240,203,360,224C480,245,600,267,720,256C840,245,960,203,1080,197.3C1200,192,1320,224,1380,240L1440,256L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center;
      background-size: cover;
    }

  /* Optional subtle decorative overlay in hero (diagonal shape) */
    &::before {
      content: "";
      position: absolute;
      top: -60px;
      right: -60px;
      width: 200px;
      height: 200px;
      background-color: #b72227;
      opacity: 0.25;
      transform: rotate(45deg);
      border-radius: 20%;
    }
  }

  .body-container {
    max-width: 1000px;
    margin: -60px auto 85px; /* Pull the container up over the wave */
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    padding: 24px;
    position: relative;
    z-index: 1; /* Ensure above wave */

    .body-title {
      text-align: center;
      font-weight: 700;
      font-size: 2rem;
      color: #01255b;
      margin-bottom: 10px;
    }

    .body-subtitle {
      text-align: center;
      color: #01255b;
      font-weight: 600;
      letter-spacing: 1px;
    }

    .two-columns {
      display: flex;
      flex-wrap: wrap;
      gap: 24px;

      .column {
        flex: 1 1 0;
        min-width: 280px;
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 20px;
        border: 2px solid #eee;
        transition: border-color 0.3s, box-shadow 0.3s;
        
        &-title {
          font-size: 1.3rem;
          font-weight: 700;
          color: #01255b;
          margin-bottom: 15px;
          position: relative;

          &::after {
            content: "";
            display: block;
            width: 50px;
            height: 4px;
            background-color: #fcd60d;
            border-radius: 4px;
            margin-top: 2px;
          }
        }

        &-content {
          margin-top: 10px;
          margin-bottom: 15px;
          color: #000000;
        }

        &:hover {
          border-color: #fcd60d;
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }
      }
    }

    article-list {
      list-style: none;
      margin-left: 0;
      padding-left: 0;
    }
    .article-list li {
      margin-bottom: 5px;
      padding-left: 20px;
      position: relative;
    }
    /* Custom bullet using ::before */
    .article-list li::before {
      content: "•";
      position: absolute;
      left: 0;
      color: #b72227;
      font-weight: bold;
    }


  }

  /*-----------------------------
    LINKS & FEEDBACK BUTTONS
  ------------------------------*/
  a {
    color: #b72227;
  }
  a:hover {
    color: #01255b;
  }

  .feedback-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;
  }

  .feedback-link {
    display: inline-block;
    padding: 10px 20px;
    background-color: #fcd60d;
    color: #01255b;
    border-radius: 20px;
    font-weight: bold;
    transition: background-color 0.3s, transform 0.3s;
  }

  .feedback-link:hover {
    background-color: #01255b;
    color: #ffffff;
    transform: translateY(-3px);
  }

  /*-----------------------------
    SOCIAL ICONS
  ------------------------------*/
  .social-container {
    display: flex;
    gap: 12px;
    justify-content: lefft;
    margin-top: 10px;
  }

  .social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    color: #01255b;
    border: 2px solid #01255b;
    font-size: 1.2rem;
    transition: background-color 0.3s, color 0.3s, transform 0.3s;

    i {
      font-size: 2rem;
    }
  }

  .social-icon:hover {
    background-color: #01255b;
    color: #fcd60d;
    transform: translateY(-3px);
  }

  /*-----------------------------
    RESPONSIVE DESIGN
  ------------------------------*/
  @media (max-width: 768px) {
    .two-columns {
      flex-direction: column;
    }
  }

  /*-----------------------------
    FOOTER / BOTTOM WAVE
  ------------------------------*/
  .footer-wave {
    position: relative;
    margin-top: -70px;
    background-color: #01255b;
    color: #fcd60d;
    text-align: center;
    padding: 50px 20px 90px;

    &::before {
      content: "";
      display: block;
      width: 100%;
      height: 90px;
      background: url("data:image/svg+xml,%3Csvg viewBox='0 0 1440 320' preserveAspectRatio='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23f9f9f9' fill-opacity='1' d='M0,320L60,282.7C120,245,240,171,360,165.3C480,160,600,224,720,208C840,192,960,96,1080,96C1200,96,1320,160,1380,192L1440,224L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z'%3E%3C/path%3E%3C/svg%3E");
      background-repeat: no-repeat;
      background-position: center top;
      background-size: cover;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
    }
  }

  .custom-carousel {
    margin-top: 10px;
    cursor: grab;

    .p-carousel-items-container {
      display: flex;
      overflow: hidden;
      scroll-behavior: smooth;

      &:active {
        cursor: grabbing;
      }
    }

    .carousel-item {
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center !important;
      width: 160px;
      height: 160px;
    }
    
    .carousel-img {
      -webkit-user-drag: none;
      object-fit: contain;
    }
  }
}

