import { StepAction, StepperState, StepConfig } from '../stepper.interfaces';

// Default parameters
export const defaultEditMode = (stepperState: StepperState) => stepperState.stepModes[stepperState.currentStep] === 'edit';
export const defaultIsDisabled = (stepperState: StepperState) => stepperState.stepModes[stepperState.currentStep] === 'view';
export const defaultHasPartialData = (stepperState: StepperState) => !!(stepperState.stepData[1] && stepperState.stepData[1].id);

export const defaultExitConfirmationMessage = (
  stepperState: StepperState,
  steps: StepConfig[],
  fieldSelectors: Record<number, (data: any) => string>,
  customMissingMessages?: Record<number, string>
) => {
  const existingSteps: number[] = [];
  const missingSteps: number[] = [];

  steps.forEach(step => {
    if (stepperState.stepData[step.value]) {
      existingSteps.push(step.value);
    } else {
      missingSteps.push(step.value);
    }
  });

  const firstStepData = stepperState.stepData[1];
  let message = `Bạn mới tạo ${steps[0].label.toLowerCase()} <strong>${fieldSelectors[1](firstStepData)}</strong>`;

  if (existingSteps.length > 1) {
    message += `, với:`;
    existingSteps.slice(1).forEach(stepNum => {
      const step = steps.find(s => s.value === stepNum);
      const stepData = stepperState.stepData[stepNum];
      const label = step?.label;
      const dataDisplay = fieldSelectors[stepNum] ? fieldSelectors[stepNum](stepData) : '';
      message += `<br>- ${label}${dataDisplay ? `: <strong>${dataDisplay}</strong>` : ''}`;
    });
  }

  if (missingSteps.length > 0) {
    const missingLabels = missingSteps.map(stepNum => {
      const step = steps.find(s => s.value === stepNum);
      return customMissingMessages?.[stepNum] || step?.entityLabel;
    }).join(', ');
    message += `<br>Nếu thoát, nhân viên này sẽ không có ${missingLabels}. Tiếp tục?`;
  }

  message += `<br><br>Vui lòng lưu ID nhân viên lại để xử lý tiếp sau: <br><strong>${fieldSelectors[1](firstStepData)}</strong>`;

  return message;
};

// Default actions
export const defaultBackAction: StepAction = {
  label: 'Quay lại', 
  icon: 'pi pi-arrow-left', 
  severity: 'secondary', 
  location: 'left', 
  hideOnSteps: [1],
  disabled: (stepperState) => stepperState.isLoading,
  onClick: 'goBack'
};

export const defaultEditAction: StepAction = {
  label: 'Chỉnh sửa', 
  icon: 'pi pi-pencil', 
  severity: 'secondary', 
  location: 'left',
  hidden: (stepperState) => stepperState.stepModes[stepperState.currentStep] !== 'view',
  disabled: (stepperState) => stepperState.isLoading,
  onClick: 'enableEdit'
};

export const defaultSkipAction: StepAction = {
  label: 'Bỏ qua thử việc', 
  icon: 'pi pi-times', 
  severity: 'secondary', 
  location: 'right', 
  showOnSteps: [3],
  disabled: (stepperState) => stepperState.isLoading,
  onClick: 'skipStep'
};

export const defaultNextAction: StepAction = {
  label: (stepperState) => stepperState.currentStep === 3 ? 'Hoàn thành' : 'Tiếp theo',
  icon: (stepperState) => stepperState.currentStep === 3 ? 'pi pi-check' : 'pi pi-arrow-right',
  location: 'right',
  disabled: (stepperState) => {
    const form = stepperState.forms[stepperState.currentStep];
    return stepperState.isLoading || !form || form.invalid;
  },
  loading: (stepperState) => stepperState.isLoading,
  onClick: 'goNext'
};
