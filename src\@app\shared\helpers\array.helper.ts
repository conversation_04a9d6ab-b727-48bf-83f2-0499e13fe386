import _ from 'lodash';
import { StringHelper } from './string.helper';

export class ArrayHelper {
    public static isEmpty(val: Array<any>): boolean {
        return !val || val.length == 0;
    }

    public static partitionArray(array: any[], size: number): any[] {
        const result = [];
        for (let i = 0; i < array.length; i += size) {
            result.push(array.slice(i, i + size));
        }
        return result;
    }

    public static buildArrayKey(uniqList: string[]) {
        if (ArrayHelper.isEmpty(uniqList)) return StringHelper.EMPTY;
        return _.sortBy(uniqList).join(StringHelper.COMMA);
    }
}
