# PopoverConfigService Documentation

A centralized service for managing popover configurations that provides scalable, reusable, and maintainable popover configurations for common operations. This service follows the same architectural pattern as `EditCreateDialogConfigService` and integrates seamlessly with the `GenericPopoverComponent` and `PopoverHandlerService`.

## Architecture Overview

The popover system consists of three main components:

1. **PopoverConfigService** - Centralized configuration management
2. **GenericPopoverComponent** - Reusable popover UI component
3. **PopoverHandlerService** - Generic operation handling with automatic API calls, error handling, and success notifications

## Key Features

- **Handler-Based Pattern**: Automatic API calls, error handling, and success notifications
- **Configuration-Driven**: Declarative popover definitions with minimal boilerplate
- **Type Safety**: Full TypeScript support with proper interfaces
- **Scalable Architecture**: Easy to add new popover types without code duplication
- **Consistent API**: Same pattern as EditCreateDialogConfigService for familiarity
- **Reusable Templates**: Share configurations across multiple components
- **Integrated Error Handling**: Built-in error handling and user feedback
- **Data Transformation**: Flexible data transformation before API calls

## Available Popover Types

### 1. Employee Retire (`employeeRetire`)
- **Purpose**: Handle employee retirement process with automatic API integration
- **API Integration**: Calls `EmployeePositionApiService.retireEmployeePosition()`
- **Fields**:
  - Employee info (static display)
  - Position (static display)
  - Retirement date (required date picker)
- **Actions**: Cancel, Confirm (with form validation)
- **Data Transformation**: Combines selected employee data with form values
- **Success Handling**: Shows success toast and triggers callback

## Quick Start Guide

### 1. Handler-Based Pattern (Recommended)

The recommended approach uses the service's built-in handler pattern for automatic API integration:

```typescript
import { PopoverConfigService } from '@shared/services';
import { GenericPopoverComponent } from '@shared/components/generic-popover';

@Component({
  imports: [GenericPopoverComponent],
  // ...
})
export class EmployeeListComponent {
  retirePopoverConfig!: PopoverConfig;
  selectedEmployee: any = null;

  @ViewChild('retirePopover') retirePopover!: GenericPopoverComponent;

  constructor(private popoverConfigService: PopoverConfigService) {
    // Service handles all API calls, error handling, and success notifications
    this.retirePopoverConfig = this.popoverConfigService.getPopoverConfig(
      'employeeRetire',
      () => this.onRetireSuccess(),
      () => this.onRetireCancel()
    );
  }

  showRetirePopover(employee: any, event?: Event) {
    this.selectedEmployee = employee;
    this.retirePopover.show(event);
  }

  onRetireSuccess() {
    this.selectedEmployee = null;
    this.reloadEmployeeTable(); // Refresh your data
  }

  onRetireCancel() {
    this.selectedEmployee = null;
  }
}
```

### 2. Template Usage

```html
<!-- Trigger button -->
<p-button label="Retire Employee"
          icon="pi pi-user-minus"
          severity="warn"
          (click)="showRetirePopover(employee, $event)">
</p-button>

<!-- Popover component -->
<generic-popover #retirePopover
                 [popoverConfig]="retirePopoverConfig"
                 [selectedData]="selectedEmployee">
</generic-popover>
```

### 3. Integration with Table Actions

Seamlessly integrate with `GenericPTableConfigService` for table row actions:

```typescript
// In your table configuration
const tableTemplate = this.genericPTableConfigService.getTableConfigTemplate('employeePosition');
this.tableConfig = this.genericPTableConfigService.mapFunctions(tableTemplate, {
  // ... other actions
  RETIRE: (item: any, event?: Event) => this.showRetirePopover(item, event)
});
```

## Service API Reference

### Core Methods

#### `getPopoverConfig(entityType, onSuccess, onCancel): PopoverConfig`

Gets a complete popover configuration with integrated handler for the specified entity type.

**Parameters:**
- `entityType: string` - The type of popover configuration ('employeeRetire', etc.)
- `onSuccess: () => void` - Callback executed after successful API operation
- `onCancel: () => void` - Callback executed when popover is cancelled

**Returns:** Complete `PopoverConfig` with integrated handlers

**Example:**
```typescript
const config = this.popoverConfigService.getPopoverConfig(
  'employeeRetire',
  () => this.refreshData(),
  () => this.clearSelection()
);
```

## Configuration Structure

### PopoverConfig Interface

```typescript
interface PopoverConfig {
  width?: string;                    // Popover width (default: '350px')
  title?: string;                    // Popover title
  dismissable?: boolean;             // Allow dismissing by clicking outside (default: true)
  styleClass?: string;               // Custom CSS classes
  formConfig: FormConfig;            // Form configuration object
  actions: PopoverAction[];          // Action buttons
  handlerConfig?: PopoverHandlerConfig; // Optional automatic operation handling
}
```

### FieldConfig Interface

```typescript
interface FieldConfig {
  key: string;                       // Field identifier and form control name
  label: string;                     // Display label
  type: 'text' | 'textarea' | 'date' | 'checkbox' | 'toggleSwitch' | 'select' | 'treeselect' | 'static' | 'custom';
  required?: boolean;                // Field validation
  disabled?: boolean | ((formValue: any) => boolean); // Dynamic disable logic
  width?: 'full' | 'half';          // Field width
  rows?: number;                     // Textarea rows
  placeholder?: string;              // Input placeholder
  validators?: any[];                // Additional validators
  config?: any;                      // Configuration for select/treeselect
  showDividerAfter?: boolean;        // Show divider after field
  value?: (data: any) => string;     // Value function for static fields
}
```

### PopoverAction Interface

```typescript
interface PopoverAction {
  label?: string;                    // Button label
  icon?: string;                     // PrimeNG icon class
  severity?: 'success' | 'info' | 'warn' | 'danger' | 'help' | 'primary' | 'secondary' | 'contrast';
  disabled?: (formValue: any, formValid: boolean) => boolean; // Dynamic disable logic
  onClick: (popoverConfig: PopoverConfig, formValue: any) => void | Promise<void>; // Action handler
  type?: 'button' | 'submit';        // Button type
  useDefaultStyle?: 'cancel' | 'confirm'; // Use predefined button styles
}
```

### PopoverHandlerConfig Interface

```typescript
interface PopoverHandlerConfig {
  service: any;                      // API service instance
  method: string;                    // Service method name
  entityLabel?: string;              // Entity label for default messages
  successMessage?: string;           // Custom success message
  errorMessage?: string;             // Custom error message
  dataTransform?: (formValue: any) => any; // Data transformation (formValue includes hidden fields)
  onSuccess?: () => void;            // Success callback
  onCancel?: () => void;             // Cancel callback
}
```

## Field Types and Examples

### Static Display Fields

Display read-only information about the selected item:

```typescript
{
  key: 'employee',
  label: 'Nhân viên',
  type: 'static',
  value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
}
```

### Form Input Fields

#### Text Input
```typescript
{
  key: 'reason',
  label: 'Lý do',
  type: 'text',
  required: true,
  placeholder: 'Nhập lý do'
}
```

#### Textarea
```typescript
{
  key: 'notes',
  label: 'Ghi chú',
  type: 'textarea',
  rows: 3,
  placeholder: 'Nhập ghi chú'
}
```

#### Date Picker
```typescript
{
  key: 'toDate',
  label: 'Ngày nghỉ việc',
  type: 'date',
  required: true,
  placeholder: 'Chọn ngày nghỉ việc'
}
```

#### Checkbox
```typescript
{
  key: 'sendNotification',
  label: 'Gửi thông báo',
  type: 'checkbox'
}
```

#### Generic Select
```typescript
{
  key: 'departmentId',
  label: 'Phòng ban',
  type: 'select',
  required: true,
  config: DEPARTMENT_SELECT_CONFIG
}
```

#### Generic Tree Select
```typescript
{
  key: 'positionId',
  label: 'Vị trí',
  type: 'treeselect',
  required: true,
  config: POSITION_TREE_SELECT_CONFIG
}
```
