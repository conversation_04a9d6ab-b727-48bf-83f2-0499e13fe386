import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '@shared/services';
import { MenuItem } from 'primeng/api';
import { MenubarModule } from 'primeng/menubar';
import { RippleModule } from 'primeng/ripple';

@Component({
    selector: 'app-learner-lookup',
    standalone: true,
    imports: [CommonModule, RouterModule, MenubarModule, RippleModule],
    templateUrl: './learner-lookup.component.html',
    styleUrl: './learner-lookup.component.scss'
})
export class LearnerLookupComponent {
    learnerLookupMenu: MenuItem[] = [
        {
            key: 'by-learner',
            label: '<PERSON> học viên',
            icon: 'pi pi-users',
            routerLink: ['/learners/learner-lookup/by-learner']
        },
        {
            key: 'by-course',
            label: '<PERSON>',
            icon: 'pi pi-search',
            routerLink: ['/learners/learner-lookup/by-course']
        }
    ];

    constructor(private router: Router, private authService: AuthService) {}
}
