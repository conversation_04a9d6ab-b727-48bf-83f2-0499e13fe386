import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { GenericSelectComponent, SelectConfig } from '@shared/components/generic-select/generic-select.component';
import { COURSE_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';
import { LearnerResult } from '@shared/components/learners/learner-result/learner-result';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';
import { CourseApiService, LearnerApiService } from '@shared/services';
import { ErrorHandlerService } from '@shared/services';
import { PaginationRequest } from '@shared/services';
import { ListboxModule } from 'primeng/listbox';

@Component({
    selector: 'app-by-course-lookup',
    standalone: true,
    imports: [CommonModule, FormsModule, GenericSelectComponent, ListboxModule, LearnerResult, GenericTagComponent],
    templateUrl: './by-course-lookup.component.html',
    styleUrl: './by-course-lookup.component.scss',
    host: {
        class: 'flex w-full'
    }
})
export class ByCourseLookupComponent implements OnInit {
    courseSelectConfig: SelectConfig;
    selectedCourse: any = null;
    learners: any[] = [];
    selectedLearner: any = null;
    learnerData: any[] = [];
    loading = false;

    constructor(
        private courseApiService: CourseApiService,
        private learnerApiService: LearnerApiService,
        private errorHandlerService: ErrorHandlerService
    ) {
        // Initialize course select config
        this.courseSelectConfig = {
            ...COURSE_SELECT_CONFIG,
            service: this.courseApiService
        };
    }

    ngOnInit() {
        // Component initialization
    }

    onCourseChange(courseCode: any) {
        this.selectedCourse = courseCode;
        this.selectedLearner = null;
        this.learnerData = [];

        if (courseCode) {
            this.learners = [];
            this.loadLearners(courseCode);
        } else {
            this.learners = [];
        }
    }

    loadLearners(courseCode: string) {
        this.loading = true;
        const request: PaginationRequest = {
            limit: 100, // Load more learners for the listbox
            offset: 0,
            sort: 'code,asc',
            query: null
        };

        const body = {
            andQ: { courseCode: courseCode }
        };

        this.learnerApiService.getLearnerOptions(request, body).subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.learners = data.items.map((learner: any) => ({
                        ...learner,
                        label: `${learner.code} - ${learner.fullname}${learner.ec ? ` - ${learner.ec}` : ''}`
                    }));
                }
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            }
        });
    }



    onLearnerSelect(event: any) {
        this.selectedLearner = event.value;
        if (this.selectedLearner) {
            this.loadLearnerData(this.selectedLearner.code);
        } else {
            this.learnerData = [];
        }
    }

    loadLearnerData(learnerCode: string) {
        this.learnerApiService.getHistoriesByLearnerCode(learnerCode).subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.learnerData = data || [];
                }
            },
            error: () => {
                this.learnerData = [];
            }
        });
    }
}