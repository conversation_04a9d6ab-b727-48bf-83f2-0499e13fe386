import { LearnerEventType, MAPPING_EVENT_TYPE_GGS, SettingType, TestEnum } from '@app/enums';
import { SyncType } from '@app/enums/sync-type.enum';
import { JwtPayloadDTO } from '@app/models/dto';
import { ResponseSyncDataModel } from '@app/models/response';
import { ITestResultB11Model, ITestResultCe1Model, ITestResultCe2Model, ITestResultIELTS1Model } from '@app/models/test-result.model';
import { ArrayHelper, ExceltHelper, ObjectHelper, StringHelper } from '@app/shared/helpers';
import { TeleBotService } from '@app/shared/services';
import { CourseRepository, SyncErrorDataRepository, LearnerEventRepository, LearnerRepository, SettingRepository } from '@database/mongodb/repositories';
import { SyncErrorDataDocument, LearnerEventModel, SettingDocument } from '@database/mongodb/schemas';
import { Injectable, Logger } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { GoogleSheetsService } from './google-sheet.service';

@Injectable()
export class LearnerEventService {
    private readonly logger = new Logger(LearnerEventService.name);
    private readonly EXCEL_MISSING_EVENT_DATE = 'eventDate không được để trống';
    private readonly EXCEL_INVALID_COURSE_CODE = 'Mã lớp không được để trống hoặc không tồn tại';
    private readonly EXCEL_INVALID_LEARNER_CODE = 'Mã học viên không được để trống hoặc không tồn tại';

    constructor(
        private learnerRepository: LearnerRepository,
        private courseRepository: CourseRepository,
        private learnerEventRepository: LearnerEventRepository,
        private settingRepository: SettingRepository,
        private googleSheetsService: GoogleSheetsService,
        private teleBotService: TeleBotService,
        private syncErrorDataRepository: SyncErrorDataRepository,
    ) {}

    async import(filePath: string, logged: JwtPayloadDTO, isFull?: string): Promise<any[]> {
        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(filePath);
        const worksheet = workbook.worksheets[0];

        const settings = await this.settingRepository.findAll();

        let dataItems: any[] = [];
        let checkLearnerCodes = new Set<string>();
        let checkCourseCodes = new Set<string>();
        let errors: string[];
        let validItems: any[] = [];
        let invalidItems: any[] = [];

        let rowValue: any;
        let learnerCode: string;
        let courseCode: string;
        let item;
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber == 1) {
                return;
            }
            errors = [];
            rowValue = row.values;

            learnerCode = ExceltHelper.parseString(rowValue[1])?.toLowerCase();
            checkLearnerCodes.add(learnerCode);
            courseCode = ExceltHelper.parseString(rowValue[2])?.toLowerCase();
            checkCourseCodes.add(courseCode);

            item = {
                learnerCode: learnerCode,
                courseCode: courseCode,
                eventDate: ExceltHelper.parseDateRaw(rowValue[3], 'eventDate', errors),
                eventType: ExceltHelper.parseEnum(rowValue[4], LearnerEventType),
                reservedFromDate: ExceltHelper.parseDateRaw(rowValue[5], 'reservedFromDate', errors),
                reservedToDate: ExceltHelper.parseDateRaw(rowValue[6], 'reservedToDate', errors),
                test: ExceltHelper.parseEnum(rowValue[7], TestEnum),
                testType: this.parseSettingRaw(rowValue[8], 'testType', errors, settings),
                outcome: this.parseSettingRaw(rowValue[9], 'outcome', errors, settings),
                level: this.parseSettingRaw(rowValue[10], 'level', errors, settings),
                testCode: ExceltHelper.parseString(rowValue[11]),
                rRaw: ExceltHelper.parseString(rowValue[12]),
                rBand: this.parseFloatRaw(rowValue[13], 'rBand', errors),
                lRaw: ExceltHelper.parseString(rowValue[14]),
                lBand: this.parseFloatRaw(rowValue[15], 'lBand', errors),
                wT1: this.parseFloatRaw(rowValue[16], 'wT1', errors),
                wT2: this.parseFloatRaw(rowValue[17], 'wT2', errors),
                wBand: this.parseFloatRaw(rowValue[18], 'wBand', errors),
                sBand: this.parseFloatRaw(rowValue[19], 'sBand', errors),
                ovrRaw: this.parseFloatRaw(rowValue[20], 'ovrRaw', errors),
                ovrRnd: this.parseFloatRaw(rowValue[21], 'ovrRnd', errors),
                note: ExceltHelper.parseString(rowValue[22]),
            };

            // First validate
            if (!item.learnerCode) {
                errors.push('Learner code không được để trống');
            }
            if (!item.eventType) {
                if (!StringHelper.isEmpty(rowValue[4])) {
                    item.eventType = rowValue[4];
                    errors.push('Event type chưa được khởi tạo');
                } else {
                    errors.push('Event type không được để trống');
                }
            }
            if (!item.test && !StringHelper.isEmpty(rowValue[7])) {
                item.test = rowValue[7];
                errors.push('test chưa được khởi tạo');
            }
            if (!item.testType && !StringHelper.isEmpty(rowValue[8])) {
                item.testType = rowValue[8];
                errors.push('testType chưa được khởi tạo');
            }
            if (!item.outcome && !StringHelper.isEmpty(rowValue[9])) {
                item.outcome = rowValue[9];
                errors.push('outcome chưa được khởi tạo');
            }
            if (!item.level && !StringHelper.isEmpty(rowValue[10])) {
                item.level = rowValue[10];
                errors.push('level chưa được khởi tạo');
            }

            if (ArrayHelper.isEmpty(errors)) {
                dataItems.push(item);
            } else {
                invalidItems.push({
                    ...item,
                    errorMessage: errors.join(StringHelper.SPLITTER),
                });
            }
        });
        const [existedLearnerCodes, existedCourseCodes] = await Promise.all([
            this.learnerRepository.findExistingCodes(Array.from(checkLearnerCodes)),
            this.courseRepository.findExistingCodes(Array.from(checkCourseCodes)),
        ]);
        const invalidLearnerCodes = Array.from(checkLearnerCodes).filter(i => !existedLearnerCodes.includes(i));
        const invalidCourseCodes = Array.from(checkCourseCodes).filter(i => !existedCourseCodes.includes(i));

        // Second validate
        dataItems.forEach(i => {
            errors = [];
            // Check learner code is require and exist
            if (!i.learnerCode || invalidLearnerCodes.includes(i.learnerCode)) {
                errors.push(this.EXCEL_INVALID_LEARNER_CODE);
            }
            switch (i.eventType) {
                case LearnerEventType.ENROLL:
                case LearnerEventType.LEFT: {
                    this.checkCourseCode(errors, invalidCourseCodes, i.courseCode);
                    if (!i.eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                    break;
                }
                case LearnerEventType.RESERVED: {
                    this.checkCourseCode(errors, invalidCourseCodes, i.courseCode);
                    if (!i.eventDate && !i.reservedFromDate) {
                        errors.push('Bảo lưu: không được để trống cả 2 trường eventDate và BL_from');
                    }
                    break;
                }
                case LearnerEventType.TEST_MID_2:
                case LearnerEventType.TEST_MID: {
                    this.checkCourseCode(errors, invalidCourseCodes, i.courseCode);
                    if (!i.eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                    if (![TestEnum.MID, TestEnum.MID2].includes(i.test)) {
                        errors.push('Thi giữa kỳ: loại phải là MID/MID2');
                    }
                    if ((!i.rRaw && i.rBand) || (i.rRaw && !i.rBand)) {
                        errors.push('Thi giữa kỳ: có rRaw mà không có rBand hoặc ngược lại');
                    }
                    if ((!i.lRaw && i.lBand) || (i.lRaw && !i.lBand)) {
                        errors.push('Thi giữa kỳ: có lRaw mà không có lBand hoặc ngược lại');
                    }
                    if ((!i.wT1 && !i.wT2 && i.wBand) || (i.wT1 && i.wT2 && !i.wBand)) {
                        errors.push('Thi giữa kỳ: có điểm thành phần (wT1 và wT2) mà không có wBand hoặc ngược lại');
                    }
                    if (i.outcome) {
                        errors.push('Thi giữa kỳ: kết quả phải để trống');
                    }
                    if (!i.testType) {
                        errors.push('Thi giữa kỳ: loại thi không được để trống');
                    }
                    break;
                }
                case LearnerEventType.TEST_FINAL: {
                    if (!i.eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                    if (i.test != TestEnum.FINAL) {
                        errors.push('Thi cuối kỳ: loại phải là FINAL');
                    }
                    if ((!i.rRaw && i.rBand) || (i.rRaw && !i.rBand)) {
                        errors.push('Thi cuối kỳ: có rRaw mà không có rBand hoặc ngược lại');
                    }
                    if ((!i.lRaw && i.lBand) || (i.lRaw && !i.lBand)) {
                        errors.push('Thi cuối kỳ: có lRaw mà không có lBand hoặc ngược lại');
                    }
                    if ((!i.wT1 && !i.wT2 && i.wBand) || (i.wT1 && i.wT2 && !i.wBand)) {
                        errors.push('Thi cuối kỳ: có điểm thành phần (wT1 và wT2) mà không có wBand hoặc ngược lại');
                    }
                    if (!i.outcome) {
                        errors.push('Thi cuối kỳ: outcome không được để trống');
                    } else if (i.outcome == 'ABSENT' && (i.ovrRaw || i.ovrRnd)) {
                        errors.push('Thi cuối kỳ (Bỏ thi): ovrRaw và ovrRnd phải để trống');
                    }
                    if (!i.testType) {
                        errors.push('Thi cuối kỳ: loại thi không được để trống');
                    } else if (i.testType == 'MAIN') {
                        this.checkCourseCode(errors, invalidCourseCodes, i.courseCode);
                        if (i.level) {
                            errors.push('Thi cuối kỳ (Chính thức): level phải để trống');
                        }
                        if (['BARELY', 'FAIL', 'PASS'].includes(i.outcome) && (!i.rBand || !i.lBand || !i.sBand || !i.wBand || !i.ovrRaw || !i.ovrRnd)) {
                            errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                        }
                    } else if (i.testType == 'INDEPENDENT') {
                        if (i.courseCode) {
                            errors.push('Thi cuối kỳ (Độc lập): mã lớp phải để trống');
                        }
                        if (StringHelper.isEmpty(i.level)) {
                            errors.push('Thi cuối kỳ (Độc lập): level không được để trống');
                        }
                        if (['BARELY', 'FAIL', 'PASS'].includes(i.outcome) && (!i.rBand || !i.lBand || !i.sBand || !i.wBand || !i.ovrRaw || !i.ovrRnd)) {
                            errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                        }
                    } else {
                        this.checkCourseCode(errors, invalidCourseCodes, i.courseCode);
                        if (i.level) {
                            errors.push('Thi cuối kỳ (Bù/Lại): level phải để trống');
                        }
                    }
                    break;
                }
                default: {
                    if (!i.eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                }
            }

            if (ArrayHelper.isEmpty(errors)) {
                validItems.push({
                    ...i,
                    createdBy: logged.username,
                    updatedBy: logged.username,
                });
            } else {
                invalidItems.push({
                    ...i,
                    errorMessage: errors.join(StringHelper.SPLITTER),
                });
            }
        });

        if (isFull == '1') {
            return [...validItems, invalidItems];
        }
        if (!ArrayHelper.isEmpty(validItems)) {
            await this.learnerEventRepository.insertMany(validItems);
        }
        return invalidItems;
    }

    async getLeanerEvents(learnerCode: string) {
        return await this.learnerEventRepository.getLeanerEvents(learnerCode);
    }

    private checkCourseCode(errors: any[], invalidCourseCodes: any[], value: string) {
        if (!value || invalidCourseCodes?.includes(value)) {
            errors.push(this.EXCEL_INVALID_COURSE_CODE);
        }
    }

    private parseFloatRaw(rawVal: any, fieldName: string, errors: string[]) {
        if (rawVal && typeof rawVal !== 'number') {
            errors.push(`${fieldName} không đúng định dạng`);
        }
        return rawVal;
    }

    private parseSettingRaw(rawVal: any, fieldName: string, errors: string[], settings: SettingDocument[]) {
        if (rawVal && settings.findIndex(i => i.code == rawVal) == -1) {
            errors.push(`${fieldName} không đúng định dạng`);
        }
        return rawVal;
    }

    async processSyncLearnerEvent() {
        const invalidData: Partial<SyncErrorDataDocument>[] = [];
        const validItems: Partial<LearnerEventModel>[] = [];
        let result = await this.syncLearnerEventFromSheet();
        if (result?.invalidData) {
            invalidData.push(result.invalidData);
        }
        validItems.push(...result.validItems);
        result = await this.syncTestResultInterFromSheet();
        if (result?.invalidData) {
            invalidData.push(result.invalidData);
        }
        validItems.push(...result.validItems);
        result = await this.syncTestPreFromSheet();
        if (result?.invalidData) {
            invalidData.push(result.invalidData);
        }
        validItems.push(...result.validItems);
        result = await this.syncTestCeFromSheet();
        if (result?.invalidData) {
            invalidData.push(result.invalidData);
        }
        validItems.push(...result.validItems);
        await this.learnerEventRepository.deleteMany({});
        await this.learnerEventRepository.insertMany(validItems);
        if (!ArrayHelper.isEmpty(invalidData)) {
            await this.syncErrorDataRepository.create(invalidData);
        }
    }

    async syncLearnerEventFromSheet(): Promise<ResponseSyncDataModel<Partial<LearnerEventModel>>> {
        const processEvents = Object.keys(MAPPING_EVENT_TYPE_GGS);
        processEvents.push('CHUYỂN LỚP');
        const syncType = SyncType.LEARNER_EVENT;
        let validItems: Partial<LearnerEventModel>[] = [];
        let invalidItems: any[] = [];
        const sheetIndexs = {
            eventDate: 0,
            learnerCode: 1,
            eventStr: 6,
            info1: 7,
            info2: 8,
        };
        let errors: string[];
        let matchRegDate: string[];
        let eventStr: string;
        let info1: string;
        let info2: string;
        let eventType: LearnerEventType;
        let eventDate: Date;
        let learnerCode: string;
        let courseCode: string;
        let courseLevel: string;
        let reservedFromDate: Date;
        let reservedToDate: Date;
        try {
            const data = await this.googleSheetsService.readSheet(syncType);
            const [existedLearnerCodes, existedCourseCodes, existedCourseLevels] = await Promise.all([
                this.learnerRepository.findExistingByUniqueField('code'),
                this.courseRepository.findExistingByUniqueField('code'),
                this.settingRepository.findExistingByUniqueField('code', { type: SettingType.COURSE_LEVEL }),
            ]);
            data.forEach(item => {
                courseCode = null;
                courseLevel = null;
                eventStr = item[sheetIndexs.eventStr].toUpperCase();
                // Kiểm tra dòng rỗng
                if (StringHelper.isEmpty(eventStr) || !processEvents.includes(eventStr)) {
                    return;
                }
                errors = [];
                eventDate = ExceltHelper.parseDateRaw(item[sheetIndexs.eventDate], 'eventDate', errors);
                learnerCode = item[sheetIndexs.learnerCode].toUpperCase();
                info1 = item[sheetIndexs.info1].toLowerCase();
                info2 = item[sheetIndexs.info2].toLowerCase();
                eventType = MAPPING_EVENT_TYPE_GGS[eventStr];
                if (!eventType) {
                    if (!eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                    this.checkExistedGsheet(learnerCode, 'learnerCode', existedLearnerCodes, errors);
                    this.checkExistedGsheet(info1, 'CourseFrom', existedCourseCodes, errors);
                    this.checkExistedGsheet(info2, 'CourseTo', existedCourseCodes, errors);

                    if (ArrayHelper.isEmpty(errors)) {
                        validItems.push(
                            ...[
                                { learnerCode, eventDate, eventType: LearnerEventType.LEFT, courseCode: info1 },
                                { learnerCode, eventDate, eventType: LearnerEventType.ENROLL, courseCode: info2 },
                            ],
                        );
                    } else {
                        invalidItems.push({ data: item, error: errors.join(StringHelper.SPLITTER) });
                    }
                    return;
                }

                if ([LearnerEventType.LEFT, LearnerEventType.ENROLL].includes(eventType)) {
                    if (!eventDate) {
                        errors.push(this.EXCEL_MISSING_EVENT_DATE);
                    }
                    this.checkExistedGsheet(learnerCode, 'learnerCode', existedLearnerCodes, errors);
                    this.checkExistedGsheet(info1, 'Course', existedCourseCodes, errors);
                    if (ArrayHelper.isEmpty(errors)) {
                        validItems.push({ learnerCode, eventDate, eventType, courseCode: info1 });
                    } else {
                        invalidItems.push({ data: item, error: errors.join(StringHelper.SPLITTER) });
                    }
                    return;
                }

                if (LearnerEventType.RESERVED == eventType) {
                    if (existedCourseCodes.includes(info1)) {
                        courseCode = info1;
                    } else if (existedCourseLevels.includes(info1.toUpperCase())) {
                        courseLevel = info1.toUpperCase();
                    } else {
                        errors.push('Info1 không phải là course/level');
                    }

                    matchRegDate = info2.match(/\d{1,4}-\d{1,2}-\d{1,4}/g);
                    if (!matchRegDate && !eventDate) {
                        errors.push('Info2 và evenDate cả 2 không được để trống / sai định dạng ngày');
                    }

                    if (matchRegDate && !ArrayHelper.isEmpty(matchRegDate)) {
                        reservedFromDate = ExceltHelper.parseDateRaw(matchRegDate[0], 'reservedFromDate', errors);
                        if (StringHelper.isEmpty(matchRegDate[1])) {
                            reservedToDate = ExceltHelper.parseDateRaw(matchRegDate[1], 'reservedToDate', errors);
                        }
                    }
                    if (ArrayHelper.isEmpty(errors)) {
                        validItems.push({ learnerCode, eventDate, eventType, courseCode, level: courseLevel, reservedFromDate, reservedToDate });
                    } else {
                        invalidItems.push({ data: item, sheetIndexs, error: errors.join(StringHelper.SPLITTER) });
                    }
                    return;
                }
            });
        } catch (error) {
            this.teleBotService.notifyMessage(`Sync ${syncType} has error ${JSON.stringify(error?.message)}`);
            return {
                validItems: [],
                invalidData: {
                    type: syncType,
                    message: error?.detail || error?.message,
                },
            };
        }
        if (ArrayHelper.isEmpty(invalidItems)) {
            return { validItems, invalidData: null };
        }
        return { validItems, invalidData: { type: syncType, data: invalidItems } };
    }

    async syncTestResultInterFromSheet(): Promise<ResponseSyncDataModel<Partial<LearnerEventModel>>> {
        let validItems: Partial<LearnerEventModel>[] = [];
        let invalidItems: any[] = [];
        const syncType = SyncType.TEST_RESULTS_INTER_ABOVE;
        const sheetIndexs = {
            eventDate: 0,
            testType: 1,
            courseCodeLevel: 2, // Mix field
            test: 3,
            testCode: 4,
            learnerCode: 5,
            rRaw: 11,
            rBand: 12,
            lRaw: 13,
            lBand: 14,
            wT1: 15,
            wT2: 16,
            wBand: 17,
            sBand: 18,
            ovrRaw: 19,
            ovrRnd: 20,
            outcome: 21,
            note: 22,
        };
        let itemVal: Partial<LearnerEventModel>;
        let result: ITestResultIELTS1Model;
        let errors: string[];
        try {
            const data = await this.googleSheetsService.readSheet(syncType);
            const [existedLearnerCodes, existedCourseCodes, existedSettings] = await Promise.all([
                this.learnerRepository.findExistingByUniqueField('code'),
                this.courseRepository.findExistingByUniqueField('code'),
                this.settingRepository.findAll(),
            ]);
            data.forEach(item => {
                // Kiểm tra dòng rỗng
                if (StringHelper.isEmpty(item[sheetIndexs.learnerCode])) {
                    return;
                }
                errors = [];
                result = {
                    rRaw: ExceltHelper.parseString(item[sheetIndexs.rRaw]),
                    rBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.rBand], 'rBand', errors),
                    lRaw: ExceltHelper.parseString(item[sheetIndexs.lRaw]),
                    lBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.lBand], 'lBand', errors),
                    wT1: ExceltHelper.parseNumberRaw(item[sheetIndexs.wT1], 'wT1', errors),
                    wT2: ExceltHelper.parseNumberRaw(item[sheetIndexs.wT2], 'wT2', errors),
                    wBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.wBand], 'wBand', errors),
                    sBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.sBand], 'sBand', errors),
                    ovrRaw: ExceltHelper.parseNumberRaw(item[sheetIndexs.ovrRaw], 'ovrRaw', errors),
                    ovrRnd: ExceltHelper.parseNumberRaw(item[sheetIndexs.ovrRnd], 'ovrRnd', errors),
                };
                itemVal = {
                    eventDate: ExceltHelper.parseDateRaw(item[sheetIndexs.eventDate], 'eventDate', errors, true),
                    testType: ExceltHelper.parseSettingRaw(item[sheetIndexs.testType], 'testType', 'name', existedSettings, errors, SettingType.TEST_TYPE, true),
                    test: ExceltHelper.parseEnumRaw(item[sheetIndexs.test], 'test', TestEnum, errors, true),
                    testCode: item[sheetIndexs.testCode],
                    learnerCode: item[sheetIndexs.learnerCode],
                    result,
                    outcome: ExceltHelper.parseSettingRaw(item[sheetIndexs.outcome], 'outcome', 'name', existedSettings, errors, SettingType.TEST_RESULT),
                    note: ExceltHelper.parseString(item[sheetIndexs.note]),
                };

                this.checkExistedGsheet(itemVal.learnerCode, 'learnerCode', existedLearnerCodes, errors);
                if (StringHelper.isEmpty(result.rRaw) !== ObjectHelper.isNullOrUndefined(result.rBand)) {
                    errors.push('Có rRaw mà không có rBand hoặc ngược lại');
                }
                if (StringHelper.isEmpty(result.lRaw) !== ObjectHelper.isNullOrUndefined(result.lBand)) {
                    errors.push('Có lRaw mà không có lBand hoặc ngược lại');
                }
                if (
                    (ObjectHelper.isNullOrUndefined(result.wT1) && ObjectHelper.isNullOrUndefined(result.wT2) && result.wBand) ||
                    (result.wT1 && result.wT2 && ObjectHelper.isNullOrUndefined(result.wBand))
                ) {
                    errors.push('Có điểm thành phần (wT1 và wT2) mà không có wBand hoặc ngược lại');
                }
                switch (itemVal.test) {
                    case TestEnum.MID:
                    case TestEnum.MID2: {
                        itemVal.eventType = itemVal.test == TestEnum.MID ? LearnerEventType.TEST_MID : LearnerEventType.TEST_MID_2;
                        itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                        this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        if (itemVal.outcome) {
                            errors.push('Thi giữa kỳ: kết quả phải để trống');
                        }
                        break;
                    }
                    case TestEnum.FINAL: {
                        if (!itemVal.outcome) {
                            errors.push('Thi cuối kỳ: kết quả không được để trống');
                        }

                        if (itemVal.outcome == 'ABSENT' && (!ObjectHelper.isNullOrUndefined(result.ovrRaw) || !ObjectHelper.isNullOrUndefined(result.ovrRnd))) {
                            errors.push('Thi cuối kỳ (Bỏ thi): ovrRaw và ovrRnd phải để trống');
                        }

                        itemVal.eventType = LearnerEventType.TEST_FINAL;
                        if (itemVal.testType == 'MAIN') {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rBand) ||
                                    ObjectHelper.isNullOrUndefined(result.lBand) ||
                                    ObjectHelper.isNullOrUndefined(result.sBand) ||
                                    ObjectHelper.isNullOrUndefined(result.wBand) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRaw) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRnd))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                            }
                        } else if (itemVal.testType == 'INDEPENDENT') {
                            itemVal.level = ExceltHelper.parseSettingRaw(
                                item[sheetIndexs.courseCodeLevel].toUpperCase(),
                                'courseLevel',
                                'code',
                                existedSettings,
                                errors,
                                SettingType.COURSE_LEVEL,
                                true,
                            );
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rBand) ||
                                    ObjectHelper.isNullOrUndefined(result.lBand) ||
                                    ObjectHelper.isNullOrUndefined(result.sBand) ||
                                    ObjectHelper.isNullOrUndefined(result.wBand) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRaw) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRnd))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                            }
                        } else {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        }
                        break;
                    }
                    default: {
                        errors.push('Loại thi không hợp lệ');
                    }
                }
                if (ArrayHelper.isEmpty(errors)) {
                    validItems.push(itemVal);
                } else {
                    invalidItems.push({ data: item, sheetIndexs, error: errors.join(StringHelper.SPLITTER) });
                }
            });
        } catch (error) {
            this.teleBotService.notifyMessage(`Sync ${syncType} has error ${JSON.stringify(error?.message)}`);
            return {
                validItems: [],
                invalidData: {
                    type: syncType,
                    message: error?.detail || error?.message,
                },
            };
        }
        if (ArrayHelper.isEmpty(invalidItems)) {
            return { validItems, invalidData: null };
        }
        return { validItems, invalidData: { type: syncType, data: invalidItems } };
    }

    async syncTestPreFromSheet(): Promise<ResponseSyncDataModel<Partial<LearnerEventModel>>> {
        let validItems: Partial<LearnerEventModel>[] = [];
        let invalidItems: any[] = [];
        let syncType = SyncType.TEST_RESULTS_PRE;
        const sheetIndexs = {
            eventDate: 0,
            testType: 1,
            courseCodeLevel: 2, // Mix field
            test: 3,
            testCode: 4,
            learnerCode: 5,
            rRaw: 11,
            rBand: 12,
            lRaw: 13,
            lBand: 14,
            wRaw: 15,
            wBand: 16,
            sRaw: 17,
            sBand: 18,
            ovrRaw: 19,
            ovrRnd: 20,
            outcome: 21,
            note: 22,
        };
        let itemVal: Partial<LearnerEventModel>;
        let result: ITestResultB11Model;
        let errors: string[];
        try {
            const data = await this.googleSheetsService.readSheet(syncType);
            const [existedLearnerCodes, existedCourseCodes, existedSettings] = await Promise.all([
                this.learnerRepository.findExistingByUniqueField('code'),
                this.courseRepository.findExistingByUniqueField('code'),
                this.settingRepository.findAll(),
            ]);
            data.forEach(item => {
                // Kiểm tra dòng rỗng
                if (StringHelper.isEmpty(item[sheetIndexs.learnerCode])) {
                    return;
                }
                errors = [];
                result = {
                    rRaw: ExceltHelper.parseString(item[sheetIndexs.rRaw]),
                    rBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.rBand], 'rBand', errors),
                    lRaw: ExceltHelper.parseString(item[sheetIndexs.lRaw]),
                    lBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.lBand], 'lBand', errors),
                    wRaw: ExceltHelper.parseString(item[sheetIndexs.wRaw]),
                    wBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.wBand], 'wBand', errors),
                    sRaw: ExceltHelper.parseString(item[sheetIndexs.sRaw]),
                    sBand: ExceltHelper.parseNumberRaw(item[sheetIndexs.sBand], 'sBand', errors),
                    ovrRaw: ExceltHelper.parseNumberRaw(item[sheetIndexs.ovrRaw], 'ovrRaw', errors),
                    ovrRnd: ExceltHelper.parseNumberRaw(item[sheetIndexs.ovrRnd], 'ovrRnd', errors),
                };
                itemVal = {
                    eventDate: ExceltHelper.parseDateRaw(item[sheetIndexs.eventDate], 'eventDate', errors, true),
                    testType: ExceltHelper.parseSettingRaw(item[sheetIndexs.testType], 'testType', 'name', existedSettings, errors, SettingType.TEST_TYPE, true),
                    test: ExceltHelper.parseEnumRaw(item[sheetIndexs.test], 'test', TestEnum, errors, true),
                    testCode: item[sheetIndexs.testCode],
                    learnerCode: item[sheetIndexs.learnerCode],
                    result,
                    outcome: ExceltHelper.parseSettingRaw(item[sheetIndexs.outcome], 'outcome', 'name', existedSettings, errors, SettingType.TEST_RESULT),
                    note: ExceltHelper.parseString(item[sheetIndexs.note]),
                };

                this.checkExistedGsheet(itemVal.learnerCode, 'learnerCode', existedLearnerCodes, errors);
                if (StringHelper.isEmpty(result.rRaw) !== ObjectHelper.isNullOrUndefined(result.rBand)) {
                    errors.push('Có rRaw mà không có rBand hoặc ngược lại');
                }
                if (StringHelper.isEmpty(result.lRaw) !== ObjectHelper.isNullOrUndefined(result.lBand)) {
                    errors.push('Có lRaw mà không có lBand hoặc ngược lại');
                }
                if (StringHelper.isEmpty(result.wRaw) !== ObjectHelper.isNullOrUndefined(result.wBand)) {
                    errors.push('Có wRaw mà không có wBand hoặc ngược lại');
                }
                if (StringHelper.isEmpty(result.sRaw) !== ObjectHelper.isNullOrUndefined(result.sBand)) {
                    errors.push('Có sRaw mà không có sBand hoặc ngược lại');
                }
                switch (itemVal.test) {
                    case TestEnum.MID:
                    case TestEnum.MID2: {
                        itemVal.eventType = itemVal.test == TestEnum.MID ? LearnerEventType.TEST_MID : LearnerEventType.TEST_MID_2;
                        itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                        this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        if (itemVal.outcome) {
                            errors.push('Thi giữa kỳ: kết quả phải để trống');
                        }
                        break;
                    }
                    case TestEnum.FINAL: {
                        if (!itemVal.outcome) {
                            errors.push('Thi cuối kỳ: kết quả không được để trống');
                        }

                        if (itemVal.outcome == 'ABSENT' && (!ObjectHelper.isNullOrUndefined(result.ovrRaw) || !ObjectHelper.isNullOrUndefined(result.ovrRnd))) {
                            errors.push('Thi cuối kỳ (Bỏ thi): ovrRaw và ovrRnd phải để trống');
                        }

                        itemVal.eventType = LearnerEventType.TEST_FINAL;
                        if (itemVal.testType == 'MAIN') {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rBand) ||
                                    ObjectHelper.isNullOrUndefined(result.lBand) ||
                                    ObjectHelper.isNullOrUndefined(result.sBand) ||
                                    ObjectHelper.isNullOrUndefined(result.wBand) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRaw) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRnd))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                            }
                        } else if (itemVal.testType == 'INDEPENDENT') {
                            itemVal.level = ExceltHelper.parseSettingRaw(
                                item[sheetIndexs.courseCodeLevel].toUpperCase(),
                                'courseLevel',
                                'code',
                                existedSettings,
                                errors,
                                SettingType.COURSE_LEVEL,
                                true,
                            );
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rBand) ||
                                    ObjectHelper.isNullOrUndefined(result.lBand) ||
                                    ObjectHelper.isNullOrUndefined(result.sBand) ||
                                    ObjectHelper.isNullOrUndefined(result.wBand) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRaw) ||
                                    ObjectHelper.isNullOrUndefined(result.ovrRnd))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rBand, lBand, sBand, wBand, ovrRaw, ovrRnd không được để trống');
                            }
                        } else {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        }
                        break;
                    }
                    default: {
                        errors.push('Loại thi không hợp lệ');
                    }
                }
                if (ArrayHelper.isEmpty(errors)) {
                    validItems.push(itemVal);
                } else {
                    invalidItems.push({ data: item, sheetIndexs, error: errors.join(StringHelper.SPLITTER) });
                }
            });
        } catch (error) {
            this.teleBotService.notifyMessage(`Sync ${syncType} has error ${JSON.stringify(error?.message)}`);
            return {
                validItems: [],
                invalidData: {
                    type: syncType,
                    message: error?.detail || error?.message,
                },
            };
        }
        if (ArrayHelper.isEmpty(invalidItems)) {
            return { validItems, invalidData: null };
        }
        return { validItems, invalidData: { type: syncType, data: invalidItems } };
    }

    async syncTestCeFromSheet(): Promise<ResponseSyncDataModel<Partial<LearnerEventModel>>> {
        let validItems: Partial<LearnerEventModel>[] = [];
        let invalidItems: any[] = [];
        const syncType = SyncType.TEST_RESULTS_CE;
        const sheetIndexs = {
            eventDate: 0,
            testType: 1,
            courseCodeLevel: 2, // Mix field
            test: 3,
            testCode: 4,
            learnerCode: 5,
            rScore: 11,
            lScore: 12,
            graWriScore: 13,
            pronSpkScore: 14,
            total: 15,
            midTotal: 16,
            courseTotal: 17,
            outcome: 18,
            note: 19,
        };
        let itemVal: Partial<LearnerEventModel>;
        let result: ITestResultCe1Model | ITestResultCe2Model;
        let errors: string[];
        try {
            const data = await this.googleSheetsService.readSheet(syncType);
            const [existedLearnerCodes, existedCourseCodes, existedSettings] = await Promise.all([
                this.learnerRepository.findExistingByUniqueField('code'),
                this.courseRepository.findExistingByUniqueField('code'),
                this.settingRepository.findAll(),
            ]);
            data.forEach(item => {
                // Kiểm tra dòng rỗng
                if (StringHelper.isEmpty(item[sheetIndexs.learnerCode])) {
                    return;
                }
                errors = [];
                itemVal = {
                    eventDate: ExceltHelper.parseDateRaw(item[sheetIndexs.eventDate], 'eventDate', errors, true),
                    testType: ExceltHelper.parseSettingRaw(item[sheetIndexs.testType], 'testType', 'name', existedSettings, errors, SettingType.TEST_TYPE, true),
                    test: ExceltHelper.parseEnumRaw(item[sheetIndexs.test], 'test', TestEnum, errors, true),
                    testCode: item[sheetIndexs.testCode],
                    learnerCode: item[sheetIndexs.learnerCode],
                    outcome: ExceltHelper.parseSettingRaw(item[sheetIndexs.outcome], 'outcome', 'name', existedSettings, errors, SettingType.TEST_RESULT),
                    note: ExceltHelper.parseString(item[sheetIndexs.note]),
                };
                this.checkExistedGsheet(itemVal.learnerCode, 'learnerCode', existedLearnerCodes, errors);
                switch (itemVal.test) {
                    case TestEnum.MID:
                    case TestEnum.MID2: {
                        result = {
                            rScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.rScore], 'rScore', errors),
                            lScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.lScore], 'lScore', errors),
                            graWriScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.graWriScore], 'graWriScore', errors),
                            pronSpkScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.pronSpkScore], 'pronSpkScore', errors),
                            total: ExceltHelper.parseNumberRaw(item[sheetIndexs.total], 'total', errors),
                        };
                        itemVal.eventType = itemVal.test == TestEnum.MID ? LearnerEventType.TEST_MID : LearnerEventType.TEST_MID_2;
                        itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                        this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        if (itemVal.outcome) {
                            errors.push('Thi giữa kỳ: kết quả phải để trống');
                        }
                        break;
                    }
                    case TestEnum.FINAL: {
                        result = {
                            rScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.rScore], 'rScore', errors),
                            lScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.lScore], 'lScore', errors),
                            graWriScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.graWriScore], 'graWriScore', errors),
                            pronSpkScore: ExceltHelper.parseNumberRaw(item[sheetIndexs.pronSpkScore], 'pronSpkScore', errors),
                            total: ExceltHelper.parseNumberRaw(item[sheetIndexs.total], 'total', errors),
                            midTotal: ExceltHelper.parseNumberRaw(item[sheetIndexs.midTotal], 'midTotal', errors),
                            courseTotal: ExceltHelper.parseNumberRaw(item[sheetIndexs.courseTotal], 'courseTotal', errors),
                        };

                        if (!itemVal.outcome) {
                            errors.push('Thi cuối kỳ: kết quả không được để trống');
                        }

                        if (itemVal.outcome == 'ABSENT' && (!ObjectHelper.isNullOrUndefined(result.total) || !ObjectHelper.isNullOrUndefined(result.courseTotal))) {
                            errors.push('Thi cuối kỳ (Bỏ thi): total và courseTotal phải để trống');
                        }
                        itemVal.eventType = LearnerEventType.TEST_FINAL;
                        if (itemVal.testType == 'MAIN') {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rScore) ||
                                    ObjectHelper.isNullOrUndefined(result.lScore) ||
                                    ObjectHelper.isNullOrUndefined(result.graWriScore) ||
                                    ObjectHelper.isNullOrUndefined(result.pronSpkScore) ||
                                    ObjectHelper.isNullOrUndefined(result.total) ||
                                    ObjectHelper.isNullOrUndefined(result.courseTotal))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rScore, lScore, graWriScore, pronSpkScore, total, courseTotal không được để trống');
                            }
                        } else if (itemVal.testType == 'INDEPENDENT') {
                            itemVal.level = item[sheetIndexs.courseCodeLevel].toUpperCase();
                            itemVal.level = ExceltHelper.parseSettingRaw(
                                item[sheetIndexs.courseCodeLevel],
                                'courseLevel',
                                'code',
                                existedSettings,
                                errors,
                                SettingType.COURSE_LEVEL,
                                true,
                            );
                            if (
                                ['BARELY', 'FAIL', 'PASS'].includes(itemVal.outcome) &&
                                (ObjectHelper.isNullOrUndefined(result.rScore) ||
                                    ObjectHelper.isNullOrUndefined(result.lScore) ||
                                    ObjectHelper.isNullOrUndefined(result.graWriScore) ||
                                    ObjectHelper.isNullOrUndefined(result.pronSpkScore) ||
                                    ObjectHelper.isNullOrUndefined(result.total) ||
                                    ObjectHelper.isNullOrUndefined(result.courseTotal))
                            ) {
                                errors.push('Thi cuối kỳ (Chính thức/Độc lập): rScore, lScore, graWriScore, pronSpkScore, total, courseTotal không được để trống');
                            }
                        } else {
                            itemVal.courseCode = item[sheetIndexs.courseCodeLevel].toLowerCase();
                            this.checkExistedGsheet(itemVal.courseCode, 'courseCode', existedCourseCodes, errors);
                        }
                        break;
                    }
                    default: {
                        errors.push('Loại thi không hợp lệ');
                    }
                }
                itemVal.result = result;
                if (ArrayHelper.isEmpty(errors)) {
                    validItems.push(itemVal);
                } else {
                    invalidItems.push({ data: item, sheetIndexs, error: errors.join(StringHelper.SPLITTER) });
                }
            });
        } catch (error) {
            this.teleBotService.notifyMessage(`Sync ${syncType} has error ${JSON.stringify(error?.message)}`);
            return {
                validItems: [],
                invalidData: {
                    type: syncType,
                    message: error?.detail || error?.message,
                },
            };
        }
        if (ArrayHelper.isEmpty(invalidItems)) {
            return { validItems, invalidData: null };
        }
        return { validItems, invalidData: { type: syncType, data: invalidItems } };
    }

    private checkExistedGsheet(val, fieldName: string, values: string[], errors: string[]): void {
        if (StringHelper.isEmpty(val)) {
            errors.push(`${fieldName} không được để trống`);
        }
        if (!values.includes(val)) {
            errors.push(`${fieldName} không tồn tại`);
        }
    }
}
