import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ViewChildren, QueryList, ContentChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { TextareaModule } from 'primeng/textarea';
import { SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';
import { DatePickerModule } from 'primeng/datepicker';
import { DividerModule } from 'primeng/divider';
import { CheckboxModule } from 'primeng/checkbox';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { GenericSelectComponent } from '../generic-select/generic-select.component';
import { GenericTreeSelectComponent } from '../generic-treeselect/generic-treeselect.component';
import { DatepickerDefaultsDirective } from '@shared/directives/datepicker-defaults.directive';
import { AutoFormatDirective } from '@shared/directives/formats.directive';
import { FormFieldConfig, FieldGroup, FormConfig } from './edit-create-form.interfaces';

@Component({
  selector: 'edit-create-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    InputTextModule,
    TextareaModule,
    SelectModule,
    TooltipModule,
    DatePickerModule,
    DividerModule,
    CheckboxModule,
    ToggleSwitchModule,
    GenericSelectComponent,
    GenericTreeSelectComponent,
    DatepickerDefaultsDirective,
    AutoFormatDirective
  ],
  templateUrl: './edit-create-form.component.html',
  styleUrls: ['./edit-create-form.component.scss']
})
export class EditCreateFormComponent implements OnInit, OnChanges {
  @Input() editMode: boolean = false;
  @Input() initialData: any = null;
  @Input() isDisabled: boolean = false;
  @Input() formConfig: FormConfig = { fields: [] };
  @Output() formChange = new EventEmitter<FormGroup>();

  // Template references for custom field templates
  @ContentChild('customFieldTemplate') customFieldTemplate?: TemplateRef<any>;

  // ViewChildren for accessing field components
  @ViewChildren(GenericSelectComponent) selectComponents!: QueryList<GenericSelectComponent>;
  @ViewChildren(GenericTreeSelectComponent) treeSelectComponents!: QueryList<GenericTreeSelectComponent>;

  form: FormGroup = new FormGroup({});
  fieldGroups: FieldGroup[] = [];

  constructor(private formBuilder: FormBuilder) { }

  ngOnInit() {
    this.createFieldGroups();
    this.buildForm();
    this.populateForm();
    this.emitForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['formConfig'] && !changes['formConfig'].firstChange) {
      this.createFieldGroups();
      this.buildForm();
      this.populateForm();
      this.emitForm();
    }

    if (changes['initialData'] && this.form) {
      this.populateForm();
    }

    if (changes['isDisabled'] && this.form) {
      this.updateFormDisabledState();
    }

  }

  private buildForm() {
    const formControls: { [key: string]: any } = {};

    this.formConfig.fields.forEach((field: FormFieldConfig) => {
      // Skip static and custom fields as they don't need form controls
      if (field.type === 'static' || field.type === 'custom') {
        return;
      }

      const validators = [];
      if (field.required) {
        validators.push(Validators.required);
      }
      if (field.validators) {
        validators.push(...field.validators);
      }

      // Always start with null values like edit-create-dialog
      formControls[field.key] = [null, validators];
    });

    this.form = this.formBuilder.group(formControls);
    console.log(this.form);

    // Subscribe to form changes for dependency tracking
    this.form.valueChanges.subscribe(() => {
      this.updateFieldDisabledStates();
    });
  }

  private populateForm() {
    if (this.form) {
      if (this.initialData) {
        // Edit mode: populate form with initial data using nested property support
        const formData: any = {};
        this.formConfig.fields.forEach(field => {
          // Skip static and custom fields as they don't have form controls
          if (field.type === 'static' || field.type === 'custom') {
            return;
          }

          const value = this.getNestedProperty(this.initialData, field.key);
          if (value !== undefined) {
            formData[field.key] = value;
          }
        });
        this.form.patchValue(formData);
      } else {
        // Create mode: reset form to clear any previous values
        this.form.reset();

        // Set default values for specific field types
        this.formConfig.fields.forEach(field => {
          // Skip static and custom fields as they don't have form controls
          if (field.type === 'static' || field.type === 'custom') {
            return;
          }

          if (field.type === 'checkbox' || field.type === 'toggleSwitch') {
            this.form.get(field.key)?.setValue(false);
          }
        });
        // Use setTimeout to ensure ViewChildren are available
        setTimeout(() => {
          // Reset any generic-select components to clear their options
          if (this.selectComponents) {
            this.selectComponents.forEach(component => {
              component.reset();
            });
          }

          // Reset any generic-treeselect components
          if (this.treeSelectComponents) {
            this.treeSelectComponents.forEach(component => {
              component.reset();
            });
          }
        }, 0);
      }

      // Apply disabled states after population
      this.updateFormDisabledState();
    }
  }

  private emitForm() {
    this.formChange.emit(this.form);
  }

  private createFieldGroups() {
    this.fieldGroups = [];

    for (const field of this.formConfig.fields) {
      // Skip hidden fields
      if (this.isFieldHidden(field)) {
        continue;
      }

      if (field.width === 'full' || field.width === undefined) {
        // Full-width field: create a new group with just this field
        this.fieldGroups.push([field]);
      } else if (field.width === 'half') {
        // Half-width field: check the last group
        const lastGroup = this.fieldGroups[this.fieldGroups.length - 1];

        if (!lastGroup || lastGroup.length === 0) {
          // No previous group or empty group: create new group
          this.fieldGroups.push([field]);
        } else {
          // Check if the last group has a full-width field or already has 2 half-width fields
          const hasFullWidthField = lastGroup.some(f => f.width === 'full' || f.width === undefined);
          const hasTwoHalfWidthFields = lastGroup.length >= 2;

          if (hasFullWidthField || hasTwoHalfWidthFields) {
            // Create new group for this half-width field
            this.fieldGroups.push([field]);
          } else {
            // Add to existing group (should have 1 half-width field)
            lastGroup.push(field);
          }
        }
      }
    }
  }

  // Helper method to get nested property values with support for optional chaining syntax
  private getNestedProperty(obj: any, path: string): any {
    if (!obj || !path) return undefined;

    // Handle optional chaining syntax (e.g., 'profile?.fullname')
    const cleanPath = path.replace(/\?/g, '');

    // Split the path and traverse the object
    return cleanPath.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  getStaticFieldValue(field: FormFieldConfig): string {
    if (this.initialData && field.value) {
      return field.value(this.initialData);
    }
    return '';
  }

  private updateFormDisabledState() {
    if (!this.form) return;

    if (this.isDisabled) {
      this.form.disable({ emitEvent: false }); // Prevent triggering valueChanges
    } else {
      this.form.enable({ emitEvent: false }); // Prevent triggering valueChanges
      // Re-apply individual field disabled states
      this.updateFieldDisabledStates();
    }
  }

  private updateFieldDisabledStates() {
    if (!this.form) return;

    this.formConfig.fields.forEach(field => {
      const control = this.form.get(field.key);
      if (control) {
        if (this.isFieldDisabled(field)) {
          control.disable({ emitEvent: false }); // Prevent triggering valueChanges
        } else if (!this.isDisabled) {
          // Only enable if the whole form is not disabled
          control.enable({ emitEvent: false }); // Prevent triggering valueChanges
        }
      }
    });
  }

  isFieldDisabled(field: FormFieldConfig): boolean {
    if (typeof field.disabled === 'boolean') {
      return field.disabled;
    }
    if (typeof field.disabled === 'function') {
      // Use getRawValue() to include disabled controls in the value
      const rawValue = this.form?.getRawValue() || {};
      return field.disabled(this.editMode, rawValue);
    }
    return false;
  }

  isFieldHidden(field: FormFieldConfig): boolean {
    if (typeof field.hidden === 'boolean') {
      return field.hidden;
    }
    if (typeof field.hidden === 'function') {
      // Use getRawValue() to include disabled controls in the value
      // Check if form exists to avoid errors with static/custom fields
      const formValue = this.form ? this.form.getRawValue() : {};
      return field.hidden(this.editMode, formValue);
    }
    return false;
  }

  getFieldTooltip(field: FormFieldConfig): string {
    if (typeof field.tooltip === 'string') {
      return field.tooltip;
    }
    if (typeof field.tooltip === 'function') {
      // Use getRawValue() to include disabled controls in the value
      // Check if form exists to avoid errors with static/custom fields
      const formValue = this.form ? this.form.getRawValue() : {};
      return field.tooltip(this.editMode, formValue);
    }
    return '';
  }

  getFieldError(fieldKey: string): string | null {
    const control = this.form.get(fieldKey);
    if (control?.invalid && control?.touched) {
      const field = this.formConfig.fields.find(f => f.key === fieldKey);

      if (field?.customErrorMessage) {
        return field.customErrorMessage;
      }

      if (control.errors?.['required']) {
        return `${field?.label || 'Trường này'} là bắt buộc`;
      }

      // Add more specific error handling as needed
      return `${field?.label || 'Trường này'} không hợp lệ`;
    }
    return null;
  }



  getDependencyValue(field: FormFieldConfig): any {
    if (field.dependsOn && this.form) {
      return this.form.get(field.dependsOn)?.value;
    }
    return null;
  }

  // Not used right now - Method to get field component reference by templateRef name
  // getFieldComponent(templateRef: string): any {
  //   // This allows parent components to access specific field components
  //   // when they need to call methods like clearSelection(), etc.
  //   const selectComponent = this.selectComponents.find((_, index) => {
  //     const field = this.formConfig.fields.filter((f: FormFieldConfig) => f.type === 'select')[index];
  //     return field?.templateRef === templateRef;
  //   });

  //   if (selectComponent) return selectComponent;

  //   const treeSelectComponent = this.treeSelectComponents.find((_, index) => {
  //     const field = this.formConfig.fields.filter((f: FormFieldConfig) => f.type === 'treeselect')[index];
  //     return field?.templateRef === templateRef;
  //   });

  //   return treeSelectComponent;
  // }


}
