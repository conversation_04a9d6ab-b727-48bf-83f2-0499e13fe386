stages:
  - deploy

deploy_dev:
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "dev"
  script:
    - echo "Running DEV deployment via Ansible"
    - ansible-playbook -i .ansible/inventory .ansible/deploy.yml --extra-vars "@.ansible/group_vars/dev.yml"
  environment:
    name: development

deploy_prod:
  stage: deploy
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  script:
    - echo "Running PROD deployment via Ansible"
    - ansible-playbook -i .ansible/inventory .ansible/deploy.yml --extra-vars "@.ansible/group_vars/prod.yml"
  environment:
    name: production