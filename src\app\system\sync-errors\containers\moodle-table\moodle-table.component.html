<p-table [value]="data" [scrollable]="true" scrollHeight="400px" responsiveLayout="scroll">
    <ng-template #header>
        <tr>
            <th pSortableColumn="code">
                <div class="flex items-center gap-1">
                    code
                    <p-sortIcon field="code"/>
                </div>
            </th>
            <th pSortableColumn="fullname">
                <div class="flex items-center gap-1">
                    fullname
                    <p-sortIcon field="fullname"/>
                </div>
            </th>
            <th pSortableColumn="email">
                <div class="flex items-center gap-1">
                    email
                    <p-sortIcon field="email"/>
                </div>
            </th>
            <th style="min-width: 15rem" alignFrozen="right" pSortableColumn="error" pFrozenColumn [frozen]="true">
                error
                <p-sortIcon field="error"/>
            </th>
        </tr>
    </ng-template>
    <ng-template #body let-childItem>
        <tr>
            <td>
                {{ childItem.code }}
            </td>
            <td>
                {{ childItem.fullname }}
            </td>
            <td>
                {{ childItem.email }}
            </td>
            <td style="min-width: 20rem" alignFrozen="right" pFrozenColumn [frozen]="true">
                {{ childItem.error }}
            </td>
        </tr>
    </ng-template>
    <ng-template #emptymessage>
        <tr>
            <td colspan="numberOfCols">Không có dữ liệu</td>
        </tr>
    </ng-template>
</p-table>
