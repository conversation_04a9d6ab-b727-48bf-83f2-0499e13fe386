import { inject } from '@angular/core';
import { EmployeePositionApiService } from '@shared/services';
import { TableConfig, PLACEHOLDER_FUNCTIONS } from '../generic-p-table.interfaces';

export const employeePositionTableConfig = (): TableConfig => {
  const employeePositionApiService = inject(EmployeePositionApiService);

  return {
    title: 'Lịch sử công tác',
    entityName: 'lịch sử công tác',
    service: employeePositionApiService,
    method: 'getEmployeePositions',
    columns: [
      { field: 'employee.code', header: 'Mã nhân viên', width: 'min-w-[100px]', sortable: true, type: 'text' },
      { field: 'employee.name', header: 'Tên nhân viên', width: 'min-w-[200px]', sortable: true, type: 'text' },
      { field: 'position.name', header: 'Vị trí', width: 'min-w-[150px]', sortable: true, type: 'text' },
      { field: 'department.name', header: 'Phòng ban', width: 'min-w-[150px]', sortable: true, type: 'text' },
      { field: 'status', header: 'Trạng thái', width: 'min-w-[200px]', sortable: true, type: 'tag', tagType: 'employeePositionStatus' },
      { field: 'fromDate', header: 'Ngày nhận việc', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'toDate', header: 'Ngày kết thúc', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' }
    ],
    features: {
      virtualScroll: true,
      toolbar: true
    },
    actions: [
      {
        location: 'toolbar',
        useDefaultStyle: 'add',
        onClick: PLACEHOLDER_FUNCTIONS.OPEN_NEW
      },
      {
        location: 'row',
        icon: 'pi pi-user-minus',
        tooltip: 'Nghỉ việc',
        severity: 'warn',
        styleClass: 'p-button-warning',
        onClick: PLACEHOLDER_FUNCTIONS.RETIRE
      },
      {
        location: 'row',
        useDefaultStyle: 'edit',
        onClick: PLACEHOLDER_FUNCTIONS.EDIT
      },
      {
        location: 'row',
        useDefaultStyle: 'delete',
        onClick: PLACEHOLDER_FUNCTIONS.DELETE,
        confirmMessage: 'Bạn có chắc chắn muốn xóa lịch sử công tác này?'
      }
    ]
  };
};
