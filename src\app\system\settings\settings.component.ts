import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { PanelMenuModule } from 'primeng/panelmenu';
import { MenuItem } from 'primeng/api';
import { RippleModule } from 'primeng/ripple';

@Component({
    selector: 'app-settings',
    standalone: true,
    imports: [CommonModule, RouterModule, PanelMenuModule, RippleModule],
    templateUrl: './settings.component.html',
    styleUrl: './settings.component.scss'
})
export class SettingsComponent {
    settingsMenu: MenuItem[] = [
        {
            label: 'Bảo mật',
            // icon: 'pi pi-shield',
            expanded: true,
            items: [
                {
                    key: 'roles',
                    label: 'Phân quyền',
                    icon: 'pi pi-users',
                    routerLink: ['/system/settings/roles']
                },
                {
                    key: 'users',
                    label: 'Người dùng',
                    icon: 'pi pi-user',
                    routerLink: ['/system/settings/users']
                }
            ]
        },
        {
            label: 'Nhân sự',
            // icon: 'pi pi-briefcase',
            expanded: true,
            items: [
                {
                    key: 'departments',
                    label: 'Phòng ban',
                    icon: 'pi pi-building',
                    routerLink: ['/system/settings/departments']
                },
                {
                    key: 'positions',
                    label: 'Vị trí',
                    icon: 'pi pi-id-card',
                    routerLink: ['/system/settings/positions']
                }
            ]
        }
    ];

    constructor(private router: Router) {}
}
