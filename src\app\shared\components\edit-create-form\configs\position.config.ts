import { inject } from '@angular/core';
import { Validators } from '@angular/forms';
import { FormConfig } from '../edit-create-form.interfaces';
import { DepartmentApiService, RoleApiService } from '@shared/services';
import { ROLE_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';
import { DEPARTMENT_TREE_SELECT_CONFIG } from '@shared/components/generic-treeselect/generic-treeselect.configs';

export const positionFormConfig = (): FormConfig => {
  const departmentApiService = inject(DepartmentApiService);
  const roleApiService = inject(RoleApiService);

  return {
    fields: [
      {
        key: 'id', label: 'ID', type: 'text', width: 'full', hidden: true
      },
      {
        key: 'code', label: 'Mã vị trí', required: true, width: 'half',
        type: 'text', placeholder: 'Nhập mã vị trí (chữ thường, số, dấu gạch ngang)',
        validators: [Validators.pattern(/^[a-z0-9-]+$/)],
        customErrorMessage: 'Mã vị trí là bắt buộc và chỉ chứa chữ thường, số, dấu gạch ngang',
        disabled: (editMode: boolean) => editMode,
        tooltip: (editMode: boolean) => editMode ? 'Không thể sửa mã vị trí đã có' : ''
      },
      {
        key: 'name', label: 'Tên vị trí', required: true, width: 'half',
        type: 'text', placeholder: 'Nhập tên vị trí',
        customErrorMessage: 'Tên vị trí là bắt buộc'
      },
      {
        key: 'departmentId', label: 'Phòng ban', required: true, width: 'half',
        type: 'treeselect', config: { ...DEPARTMENT_TREE_SELECT_CONFIG, service: departmentApiService },
        customErrorMessage: 'Phòng ban là bắt buộc'
      },
      {
        key: 'roleId', label: 'Vai trò', required: true, width: 'half',
        type: 'select', config: { ...ROLE_SELECT_CONFIG, service: roleApiService },
        customErrorMessage: 'Vai trò là bắt buộc'
      }
    ]
  };
};
