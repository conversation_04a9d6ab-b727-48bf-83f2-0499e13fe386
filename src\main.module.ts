import { PERMISSIONS } from '@app/constants';
import { APP_CONTROLLERS } from '@app/controllers';
import { ResponseInterceptor } from '@app/interceptors/response.interceptor';
import { EmployeeListener, PositionListener } from '@app/listeners';
import {
    AuthService,
    CenterLocationService,
    CourseService,
    EmployeePositionService,
    EmployeeService,
    GoogleSheetsService,
    JobsService,
    LearnerEventService,
    LearnerService,
    MailerService,
    MoodleService,
    PartnerService,
    PermissionService,
    PositionService,
    ProbationService,
    ProfileService,
    RoleService,
    SettingService,
    SyncErrorsService,
    UserService,
} from '@app/services';
import { DepartmentService } from '@app/services/department.service';
import { TeleBotService } from '@app/shared/services';
import { MONGO_COLLECTION_PROVIDERS, MONGO_CONECTION_PROVIDERS } from '@database/mongodb';
import {
    CenterLocationRepository,
    CourseRepository,
    DepartmentRepository,
    EmployeePositionHistoryRepository,
    EmployeePositionRepository,
    LearnerEventRepository,
    LearnerRepository,
    PartnerRepository,
    PermissionRepository,
    PositionRepository,
    ProbationRepository,
    RoleRepository,
    SettingRepository,
    SyncErrorDataRepository,
    UserRepository,
} from '@database/mongodb/repositories';
import { EmployeeRepository } from '@database/mongodb/repositories/employee.repo';
import { ProfileRepository } from '@database/mongodb/repositories/profile.repo';
import { sqlEntities } from '@database/orm';
import { MoodleGroupRepository } from '@database/orm/repositories/moodle-group.repo';
import { MoodleUserRepository } from '@database/orm/repositories/moodle-user.repo';
import { HttpModule } from '@nestjs/axios';
import { Global, Module, OnModuleInit } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { JwtModule } from '@nestjs/jwt';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { configEnv } from '@setup/config-env';
import { sqlConnectOptions } from '@setup/sql-connect';
import * as _ from 'lodash';

@Global()
@Module({
    controllers: [...APP_CONTROLLERS],
    providers: [
        // Service
        MailerService,

        AuthService,
        UserService,

        CourseService,
        LearnerService,
        LearnerEventService,
        SettingService,
        GoogleSheetsService,
        JobsService,
        CenterLocationService,
        SyncErrorsService,
        PartnerService,
        MoodleService,
        RoleService,
        PermissionService,
        EmployeeService,
        ProfileService,
        DepartmentService,
        PositionService,
        EmployeePositionService,
        ProbationService,

        TeleBotService,

        // Listeners
        EmployeeListener,
        PositionListener,

        // databases
        ...MONGO_CONECTION_PROVIDERS,
        ...MONGO_COLLECTION_PROVIDERS,

        // Repositories
        CourseRepository,
        LearnerRepository,
        LearnerEventRepository,
        UserRepository,
        SettingRepository,
        SyncErrorDataRepository,
        CenterLocationRepository,
        PartnerRepository,

        MoodleUserRepository,
        MoodleGroupRepository,
        RoleRepository,
        PermissionRepository,
        ProfileRepository,
        EmployeeRepository,
        DepartmentRepository,
        PositionRepository,
        EmployeePositionRepository,
        EmployeePositionHistoryRepository,
        ProbationRepository,
        // Interceptor
        {
            provide: APP_INTERCEPTOR,
            useClass: ResponseInterceptor,
        },
    ],
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
            envFilePath: configEnv(),
        }),
        JwtModule.registerAsync({
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                global: true,
                secret: configService.get('APP_TOKEN_PRIVATE_KEY'),
                signOptions: { expiresIn: '1d' },
            }),
        }),
        HttpModule.register({
            timeout: 30000,
            maxRedirects: 5,
        }),
        MongooseModule.forRootAsync({
            inject: [ConfigService],
            useFactory: async (configService: ConfigService) => ({
                uri: configService.get('MONGODB_CONNECTION_STRING'),
            }),
        }),
        TypeOrmModule.forRootAsync({
            inject: [ConfigService],
            useFactory: (configService: ConfigService) => sqlConnectOptions(configService),
        }),
        TypeOrmModule.forFeature(sqlEntities()),
        ScheduleModule.forRoot(),
        EventEmitterModule.forRoot(),
    ],
})
export class MainModule implements OnModuleInit {
    constructor(
        private permissionRepository: PermissionRepository,
        private roleRepository: RoleRepository,
        private userRepository: UserRepository,
    ) {}

    async onModuleInit() {
        const permissions = [];
        const permissionCodes = [];
        Object.values(PERMISSIONS).forEach(v => {
            permissions.push({
                code: v.code,
                scope: v.scope,
                description: v.description,
            });
            permissionCodes.push(v.code);
        });
        await this.permissionRepository.deleteMany({});
        await this.permissionRepository.create(permissions);

        const permisisonSorted = _.sortBy(_.uniq(permissionCodes));
        await this.userRepository.updateOne({ isAdmin: true }, { permissions: permisisonSorted, permissionsKey: permisisonSorted.toString() }, true);
    }
}
