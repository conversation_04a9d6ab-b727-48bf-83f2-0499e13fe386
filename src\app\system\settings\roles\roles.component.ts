import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { PageConstant } from '@shared/constants';
import { DefaultCancelButtonDirective, DefaultConfirmButtonDirective } from '@shared/directives';
import { ErrorHandlerService } from '@shared/services';
import { PaginationRequest } from '@shared/services';
import { ISort, SORT_VAL } from '@shared/services';
import { RoleApiService } from '@shared/services';
import { ToastService } from '@shared/services';
import { AccordionModule } from 'primeng/accordion';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { ToolbarModule } from 'primeng/toolbar';
import { TooltipModule } from 'primeng/tooltip';
import { Tree, TreeModule } from 'primeng/tree';

@Component({
    selector: 'app-roles',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TableModule,
        ButtonModule,
        DialogModule,
        InputTextModule,
        CheckboxModule,
        ConfirmDialogModule,
        AccordionModule,
        TreeModule,
        TooltipModule,
        TagModule,
        ToolbarModule,
        DividerModule,
        DefaultCancelButtonDirective,
        DefaultConfirmButtonDirective
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './roles.component.html',
    styleUrl: './roles.component.scss'
})
export class RolesComponent implements OnInit {
    @ViewChild('permTree', { static: false }) tree!: Tree;

    roles: any[] = [];
    permissionGroups: any[] = [];
    loading: boolean = false;
    permissionsLoading: boolean = false;

    roleDialog: boolean = false;
    editMode: boolean = false;
    cloneMode: boolean = false;
    isDefaultRole: boolean = false;

    roleForm: FormGroup;

    // Tree structure for permissions
    permissionsTree: any[] = [];
    selectedPermissionNodes: any[] = [];

    // Pagination properties
    totalRecords: number = 0;
    itemPerPage: number = PageConstant.ITEM_PER_PAGE;
    itemPerPageOptions: number[] = PageConstant.ITEM_PER_PAGE_OPTIONS;
    offset: number = 0;
    sort: ISort = { field: null, order: 1 };

    constructor(
        private roleApiService: RoleApiService,
        private errorHandlerService: ErrorHandlerService,
        private toastService: ToastService,
        private formBuilder: FormBuilder,
        private confirmationService: ConfirmationService
    ) {
        this.roleForm = this.formBuilder.group({
            id: [''],
            code: ['', [Validators.required, Validators.pattern(/^[a-z0-9-]+$/)]],
            name: ['', Validators.required],
            permissions: [[]]
        });
    }

    ngOnInit() {
        this.loadRoles();
        this.loadPermissions();
    }

    loadRoles() {
        this.loading = true;

        const sortStr = this.sort.field ? `${this.sort.field},${SORT_VAL[`${this.sort.order}`]}` : '';
        const params: PaginationRequest = {
            limit: this.itemPerPage,
            offset: this.offset,
            sort: sortStr,
            query: null
        };

        this.roleApiService.getRoles(params).subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.roles = data.items || [];
                    this.totalRecords = data.total || 0;
                }
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            }
        });
    }

    loadPermissions() {
        this.permissionsLoading = true;
        this.roleApiService.getPermissions().subscribe({
            next: (response: any) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.permissionGroups = data || [];
                    // Transform permission groups into tree structure
                    this.permissionsTree = this.permissionGroups.map(group => {
                        return {
                            key: group.scope,
                            label: group.scope.charAt(0).toUpperCase() + group.scope.slice(1),
                            data: null,
                            expanded: true,
                            children: group.permissions.map((permission: any) => {
                                return {
                                    key: permission.code,
                                    label: permission.code.match(':(.+)$')[1],
                                    data: permission.description,
                                    selectable: true
                                };
                            })
                        };
                    });
                }
                this.permissionsLoading = false;
            },
            error: () => {
                this.permissionsLoading = false;
            }
        });
    }

    onLazyLoad(event: TableLazyLoadEvent) {
        this.offset = event.first || 0;
        this.itemPerPage = event.rows || PageConstant.ITEM_PER_PAGE;
        this.sort = {
            field: event.sortField,
            order: event.sortOrder || 1
        };
        this.loadRoles();
    }

    openNew() {
        this.editMode = false;
        this.cloneMode = false;
        this.isDefaultRole = false;
        this.roleForm.reset();
        this.roleForm.get('code')?.enable();
        this.selectedPermissionNodes = [];
        this.setTreeSelectable(this.permissionsTree, true);
        this.roleDialog = true;
    }

    editRole(role: any) {
        this.editMode = true;
        this.cloneMode = false;
        this.isDefaultRole = !!role.default;
        this.roleForm.patchValue({
            id: role.id,
            code: role.code,
            name: role.name
        });
        this.roleForm.get('code')?.disable();

        // Update tree selection based on role permissions
        this.updateTreeSelection(role.permissions || []);
        this.setTreeSelectable(this.permissionsTree, !role.default);

        this.roleDialog = true;
    }

    cloneRole(role: any) {
        this.editMode = false;
        this.cloneMode = true;
        this.isDefaultRole = false;
        this.roleForm.reset();
        this.roleForm.get('code')?.enable();

        // Suggest a new code with "-copy" suffix
        const suggestedCode = `${role.code}-copy`;

        // Pre-fill the form with the role's data but with a suggested new code
        this.roleForm.patchValue({
            code: suggestedCode,
            name: `${role.name} (Copy)`
        });

        // Copy the permissions from the original role
        this.updateTreeSelection(role.permissions || []);
        this.setTreeSelectable(this.permissionsTree, true);

        this.roleDialog = true;
    }

    private setTreeSelectable(nodes: any[], selectable: boolean) {
        nodes.forEach(node => {
            node.selectable = selectable;
            if (node.children && node.children.length) {
                this.setTreeSelectable(node.children, selectable);
            }
        });
    }

    updateTreeSelection(permissionCodes: string[]) {
        this.selectedPermissionNodes = [];

        if (!this.permissionsTree.length || !permissionCodes.length) {
            return;
        }

        // Find and add nodes that match the permission codes
        this.permissionsTree.forEach(group => {
            if (group.children && Array.isArray(group.children)) {
                group.children.forEach((node: any) => {
                    if (permissionCodes.includes(node.key)) {
                        this.selectedPermissionNodes.push(node);
                    }
                });
            }
        });
    }

    deleteRole(role: any) {
        this.confirmationService.confirm({
            message: 'Are you sure you want to delete this role?',
            header: 'Confirm',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.roleApiService.deleteRole(role.code).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Role deleted successfully');
                            this.loadRoles();
                        }
                    }
                });
            }
        });
    }

    updateRole() {
        if (this.roleForm.invalid) {
            return;
        }

        // Extract permission codes
        const permissionCodes = this.extractPermissionCodes();

        const roleData = {
            ...this.roleForm.getRawValue(),
            permissions: permissionCodes
        };

        // Send update / create to backend
        if (this.editMode) {
            this.roleApiService.updateRole(roleData).subscribe({
                next: (response) => {
                    const { hasError } = this.errorHandlerService.handleInternal(response);
                    if (!hasError) {
                        this.toastService.showSuccess('Role updated successfully');
                        this.roleDialog = false;
                        this.loadRoles();
                    }
                }
            });
        } else {
            this.roleApiService.createRole(roleData).subscribe({
                next: (response) => {
                    const { hasError } = this.errorHandlerService.handleInternal(response);
                    if (!hasError) {
                        this.toastService.showSuccess('Role created successfully');
                        this.roleDialog = false;
                        this.loadRoles();
                    }
                }
            });
        }
    }
    extractPermissionCodes(): string[] {
        if (!this.selectedPermissionNodes || !this.selectedPermissionNodes.length) {
            return [];
        }

        const permissionCodes: string[] = [];

        // Extract permission codes from selected nodes
        this.selectedPermissionNodes.forEach(node => {
            // If it's a leaf node (direct permission)
            if (node && node.key && !node.children) {
                permissionCodes.push(node.key);
            }
            // If it's a parent node (scope node)
            else if (node && node.children && Array.isArray(node.children)) {
                // Find all child nodes in the original tree structure, add all child permissions
                const parentNode = this.permissionsTree.find(group => group.key === node.key);
                if (parentNode && parentNode.children) {
                    parentNode.children.forEach((child: any) => {
                        if (child && child.key) {
                            permissionCodes.push(child.key);
                        }
                    });
                }
            }
        });

        // Remove duplicates
        return [...new Set(permissionCodes)];
    }

    onDialogShow() {
        this.propagateTreeSelection();
    }

    // Propagate selection up the tree after the tree is rendered
    propagateTreeSelection() {
        if (this.tree && this.selectedPermissionNodes && this.selectedPermissionNodes.length > 0) {
            this.selectedPermissionNodes.forEach(node => {
                if (this.tree) {
                    this.tree.propagateUp(node, true);
                }
            });
        }
    }
}