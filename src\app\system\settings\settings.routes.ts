import { Routes } from '@angular/router';
import { PERMISSIONS } from '@shared/constants';
import { PermissionGuard } from '../../core/guards/permission.guard';
import { SettingsComponent } from './settings.component';
import { RolesComponent } from './roles/roles.component';
import { UsersComponent } from './users/users.component';
import { DepartmentsComponent } from './departments/departments.component';
import { PositionsComponent } from './positions/positions.component';

export default [
    {
        path: '',
        component: SettingsComponent,
        children: [
            { path: '', redirectTo: 'roles', pathMatch: 'full' },
            {
                path: 'roles',
                component: RolesComponent,
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.ROLE_VIEW.code] }
            },
            {
                path: 'users',
                component: UsersComponent,
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.ROLE_VIEW.code] }
            },
            {
                path: 'departments',
                component: DepartmentsComponent,
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.DEPARTMENT_VIEW.code] }
            },
            {
                path: 'positions',
                component: PositionsComponent,
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.POSITION_VIEW.code] }
            }
        ]
    }
] as Routes;
