import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { EditCreateFormComponent, FormConfig, employeeInfoFormConfig } from '@shared/components/edit-create-form';
import { EditCreateFormConfigService } from '@shared/services';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';

@Component({
    selector: 'app-playground',
    standalone: true,
    imports: [
        CommonModule,
        EditCreateFormComponent,
        ButtonModule,
        DialogModule
    ],
    templateUrl: './playground.component.html',
    styleUrl: './playground.component.scss'
})
export class PlaygroundComponent implements OnInit {
    // Test form properties
    testFormDialog: boolean = false;
    testFormConfig!: FormConfig;
    testFormData: any = null;

    constructor(
    ) {
        // Initialize with empty config, will load async
        this.testFormConfig = employeeInfoFormConfig();
    }

    ngOnInit() {
    }

    // Test form methods
    openTestForm() {
        // this.testFormData = {
        //     toDate: new Date(),
        //     deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        //     status: 'IN_PROGRESS',
        //     isManual: false
        // };
        this.testFormDialog = true;
    }

    onTestFormChange(form: any) {
        console.log('Test form changed:', form.value);
    }

    closeTestForm() {
        this.testFormDialog = false;
        this.testFormData = null;
    }
}
