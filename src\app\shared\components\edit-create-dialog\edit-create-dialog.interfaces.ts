import { FormConfig } from '../edit-create-form';

export interface DialogHandlerConfig {
  service: any; // The service that handles CRUD operations
  createMethod: string; // Method name for creating (e.g., 'assignPositionToEmployee')
  updateMethod: string; // Method name for updating (e.g., 'updateEmployeePosition')
  entityLabel?: string; // Entity label for generating default messages (e.g., 'nhân viên', 'lịch sử công tác')
  createSuccessMessage?: string; // Success message for create (optional if entityLabel provided)
  updateSuccessMessage?: string; // Success message for update (optional if entityLabel provided)
  errorMessage?: string; // Error message (optional if entityLabel provided)
  // Data transformation functions
  commonDataTransform?: (formValue: any) => any; // Common data transformation function
  createDataTransform?: (formValue: any) => any;
  updateDataTransform?: (formValue: any) => any;
  initialDataTransform?: (rawData: any) => any; // Transform raw data into form-ready format
  // Callback functions
  onSuccess?: () => void; // Called after successful save
  onCancel?: () => void; // Called when dialog is cancelled
}

export interface DialogHandlerResult {
  success: boolean;
  error?: any;
}

export interface EditCreateDialogConfig {
  width?: string;
  createHeader?: string;
  editHeader?: string;
  modal?: boolean;
  dismissableMask?: boolean;
  styleClass?: string;
  formConfig: FormConfig; // Form configuration object
  actions: DialogAction[]; // Required - no more optional
  handlerConfig?: DialogHandlerConfig; // Optional - for automatic data transformation
}

export interface DialogAction {
  label?: string | undefined;
  icon?: string | undefined;
  severity?: "success" | "info" | "warn" | "danger" | "help" | "primary" | "secondary" | "contrast" | null | undefined;
  disabled?: (formValue: any, formValid: boolean, editMode: boolean) => boolean;
  onClick: string;
  type?: 'button' | 'submit';
  useDefaultStyle?: 'cancel' | 'confirm';
}

export type DialogEntityType =
  // HR entities
  | 'employee' | 'position' | 'probation'
  // Organizational entities
  | 'employeePosition';

// Factory function type for dialog configs
export type DialogConfigFactory = (onSuccess: () => void, onCancel: () => void) => EditCreateDialogConfig;

// Registry type for dynamic imports
export type DialogConfigRegistry = Record<DialogEntityType, () => Promise<DialogConfigFactory>>;
