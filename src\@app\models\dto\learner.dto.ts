import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateLearnerDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toUpperCase())
    readonly code: string;

    @ApiProperty({ description: 'fullname' })
    @IsString()
    @IsNotEmpty()
    readonly fullname: string;

    @ApiProperty({ description: 'email' })
    @IsOptional()
    @IsEmail()
    @Transform(({ value }) => value?.toLowerCase())
    readonly email: string;
}

export class UpdateLearnerDTO {
    @ApiProperty({ description: 'id' })
    @IsString()
    @IsNotEmpty()
    readonly id: string;

    @ApiProperty({ description: 'fullname' })
    @IsString()
    @IsNotEmpty()
    readonly fullname: string;

    @ApiProperty({ description: 'email' })
    @IsOptional()
    @IsEmail()
    @Transform(({ value }) => value?.toLowerCase())
    readonly email: string;
}

export class LearnerFromGcpDTO {
    id?: string;
    code: string;
    fullname: string;
    ec?: string;
    email?: string;
}
