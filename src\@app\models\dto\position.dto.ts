import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

export class PositionDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toLowerCase())
    readonly code: string;

    @ApiProperty({ description: 'name' })
    @IsString()
    @IsNotEmpty()
    readonly name: string;

    @ApiProperty({ description: 'roleId' })
    @IsString()
    @IsNotEmpty()
    readonly roleId: string;

    @ApiProperty({ description: 'departmentId' })
    @IsString()
    @IsNotEmpty()
    readonly departmentId: string;
}
