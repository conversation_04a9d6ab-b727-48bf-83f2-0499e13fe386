# Portal Frontend Documentation

Welcome to the comprehensive documentation for the Portal Frontend application. This documentation is organized into logical sections to help you understand, implement, and maintain the various components and systems.

## 📚 Table of Contents

### 🏗️ Architecture & Setup
- [Project Overview](./architecture/project-overview.md)
- [Getting Started](./architecture/getting-started.md)
- [Project Structure](./architecture/project-structure.md)
- [Development Guidelines](./architecture/development-guidelines.md)

### 🧩 Components Documentation
- [Component Overview](./components/README.md)
- [Edit-Create Dialog System](./components/edit-create-dialog.md)
- [Generic Stepper Component](./components/generic-stepper.md)
- [Generic P-Table Component](./components/generic-p-table.md)
- [Generic Popover Component](./components/generic-popover.md)
- [Edit-Create Form Component](./components/edit-create-form.md)

### 🔧 Services Documentation
- [Services Overview](./services/README.md)
- [Form Handler Service](./services/form-handler.md)
- [Edit-Create Form Config Service](./services/edit-create-form-config.md)
- [Popover Config Service](./services/popover-config.md)

### 📋 Usage Examples & Patterns
- [Common Patterns](./examples/common-patterns.md)
- [Component Integration](./examples/component-integration.md)
- [Configuration Examples](./examples/configuration-examples.md)
- [Best Practices](./examples/best-practices.md)

### 🧪 Testing
- [Testing Guidelines](./testing/testing-guidelines.md)
- [Unit Testing Examples](./testing/unit-testing.md)
- [Integration Testing](./testing/integration-testing.md)

### 🚀 Deployment & Maintenance
- [Build & Deployment](./deployment/build-deployment.md)
- [Troubleshooting](./deployment/troubleshooting.md)
- [Performance Optimization](./deployment/performance.md)

## 🔍 Quick Navigation

### For New Developers
1. Start with [Project Overview](./architecture/project-overview.md)
2. Follow [Getting Started](./architecture/getting-started.md)
3. Review [Development Guidelines](./architecture/development-guidelines.md)
4. Explore [Component Overview](./components/README.md)

### For Component Development
1. Review [Component Overview](./components/README.md)
2. Check [Common Patterns](./examples/common-patterns.md)
3. Follow [Best Practices](./examples/best-practices.md)
4. Use [Configuration Examples](./examples/configuration-examples.md)

### For Troubleshooting
1. Check [Troubleshooting Guide](./deployment/troubleshooting.md)
2. Review [Testing Guidelines](./testing/testing-guidelines.md)
3. Consult component-specific documentation

## 📖 How to Use This Documentation

### Navigation
- Each section has its own README with detailed navigation
- Use the table of contents above for quick access
- Cross-references are provided throughout the documentation

### Code Examples
- All code examples are tested and up-to-date
- Examples include both TypeScript and HTML templates
- Configuration examples are provided for each component

### Conventions
- 📁 Folder/Section indicators
- 🔧 Technical implementation details
- 💡 Tips and best practices
- ⚠️ Important warnings or considerations
- 🚀 Performance-related information

## 🤝 Contributing to Documentation

### Adding New Documentation
1. Follow the existing structure and naming conventions
2. Update relevant README files with new links
3. Include practical examples and code snippets
4. Cross-reference related documentation

### Updating Existing Documentation
1. Keep examples current with the codebase
2. Update cross-references when moving content
3. Maintain consistent formatting and style
4. Test all code examples before committing

## 📝 Documentation Standards

### File Naming
- Use kebab-case for file names
- Include descriptive names that match content
- Use `.md` extension for all markdown files

### Content Structure
- Start with a clear overview
- Include table of contents for longer documents
- Provide practical examples
- End with troubleshooting or common issues

### Code Examples
- Include complete, working examples
- Provide context for code snippets
- Use consistent indentation and formatting
- Include both success and error scenarios

## 🛠️ Documentation Tools

### Available Commands
```bash
# Generate/update documentation index
npm run docs:index

# Serve documentation as website
npm run docs:serve

# Open main documentation
npm run docs:open
```

### Interactive Viewer
Open `viewer.html` in your browser for an interactive documentation experience with:
- Visual navigation cards
- Quick search functionality
- Responsive design for mobile/desktop
- Direct links to all documentation sections

### Navigation Aids
- **[Navigation Guide](./navigation.md)** - Task-based documentation discovery
- **[Complete Index](./index.md)** - Auto-generated file index
- **[Interactive Viewer](./viewer.html)** - Web-based documentation browser

## 🔧 Recently Added Services

### Form Handler Service
The **[Form Handler Service](./services/form-handler.md)** provides unified form submission handling for both dialog and popover components with:
- Unified interface for CRUD and single-action operations
- Automatic data transformation and error handling
- Type-safe configuration with TypeScript
- Promise-based API with comprehensive testing

## 🚀 Quick Start Links

### Most Used Documentation
- **[Edit-Create Dialog System](./components/edit-create-dialog.md)** - Complete CRUD dialog solution
- **[Generic Stepper Component](./components/generic-stepper.md)** - Multi-step workflows
- **[Form Handler Service](./services/form-handler.md)** - Unified form submission handling
- **[Component Integration](./examples/component-integration.md)** - How components work together

### Configuration References
- **[Edit-Create Form Config](./services/edit-create-form-config.md)** - Form configuration patterns
- **[Popover Config Service](./services/popover-config.md)** - Popover configuration examples
- **[Configuration Examples](./examples/configuration-examples.md)** - Real-world config examples

---

**Last Updated:** 2025-07-17  
**Version:** 1.0.0  
**Maintained by:** Development Team

**💡 Pro Tip:** Use `Ctrl+F` to search within any documentation page, or use the [Interactive Viewer](./viewer.html) for visual navigation!
