import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { DepartmentDocument } from '../schemas';
import { SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { StringHelper, ArrayHelper } from '@app/shared/helpers';

@Injectable()
export class DepartmentRepository extends GenericRepository<DepartmentDocument> {
    private readonly logger = new Logger(DepartmentRepository.name);

    constructor(
        @Inject(MONGO_CONST.DEPARTMENT_COLLECTION)
        private readonly departmentModel: Model<DepartmentDocument>,
    ) {
        super(departmentModel);
    }

    async getChildren(departmentId: string): Promise<any[]> {
        return await this.departmentModel.aggregate([
            {
                $match: { id: departmentId },
            },
            {
                $graphLookup: {
                    from: MONGO_CONST.DEPARTMENT_COLLECTION,
                    startWith: departmentId,
                    connectFromField: 'id',
                    connectToField: 'parentId',
                    as: 'childs',
                },
            },
        ]);
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const parentId = andQ?.parentId || null;
        const name = orQ?.name || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        } else {
            matchQ['parentId'] = null;
        }

        if (!StringHelper.isEmpty(name)) {
            matchQ['$or'] = [{ name: { $regex: name, $options: 'i' } }];
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.departmentModel.aggregate([
            {
                $match: matchQ,
            },
            {
                $graphLookup: {
                    from: MONGO_CONST.DEPARTMENT_COLLECTION,
                    startWith: '$id',
                    connectFromField: 'id',
                    connectToField: 'parentId',
                    as: 'childs',
                },
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    name: 1,
                    parentId: 1,
                    childs: 1,
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }
}
