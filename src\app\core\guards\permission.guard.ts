import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, Router, CanLoad, Route, UrlTree } from '@angular/router';
import { AuthService } from '../../shared/services/auth.service';
import { Observable } from 'rxjs';
import { FULL_ROUTES } from '@shared/constants';

@Injectable({ providedIn: 'root' })
export class PermissionGuard implements CanActivate, CanLoad {

    constructor(private authService: AuthService, private router: Router) { }

    private hasAccess(requiredPermissions: string[]): boolean {
        return this.authService.hasAnyPermission(requiredPermissions);
    }

    private checkAccess(requiredPermissions: string[]): boolean | UrlTree {
        try {
            if (this.authService.isTokenExpired()) {
                return this.router.createUrlTree([FULL_ROUTES.LOGIN]);
            }

            if (!requiredPermissions || !this.hasAccess(requiredPermissions)) {
                console.log('No permission access');
                return this.router.createUrlTree(['/auth/access']);
            }

            return true;
        } catch (error) {
            return this.router.createUrlTree([FULL_ROUTES.LOGIN]);
        }
    }

    canActivate(route: ActivatedRouteSnapshot): boolean | UrlTree | Observable<boolean | UrlTree> {
        const requiredPermissions = route.data['permissions'] as string[];
        return this.checkAccess(requiredPermissions);
    }

    canLoad(route: Route): boolean | UrlTree | Observable<boolean | UrlTree> {
        const requiredPermissions = route.data?.['permissions'] as string[];
        return this.checkAccess(requiredPermissions);
    }
}
