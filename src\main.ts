import './ts-paths-fix-apply';
import { NestFactory } from '@nestjs/core';
import * as bodyParser from 'body-parser';
import { MainModule } from './main.module';
import { initHelmet, swagger, urlBlackList } from '@setup';
import { ErrorFilter } from '@errors/filters';

const bootstrap = async () => {
    const app = await NestFactory.create(MainModule);

    // ============== use config ===========
    app.use(bodyParser.json());
    app.use(bodyParser.urlencoded({ extended: false }));
    app.useGlobalFilters(new ErrorFilter());
    app.enableCors({
        origin: ['http://localhost:4200',
            'http://localhost:3000',
            'https://www.portal.weset.edu.vn',
            'https://portal.weset.edu.vn',
            'https://dev.portal.weset.edu.vn',
            'https://www.dev.portal.weset.edu.vn',
            'https://h5.zdn.vn'],
        methods: 'GET,POST,PUT,DELETE',
    });
    // app.enableCors();

    swagger(app);
    urlBlackList(app);
    initHelmet(app);

    const port = 8800;
    await app.listen(port, async () => {
        console.info(`Application is listening on port ${port} and ${await app.getUrl()} `);
    });
};
bootstrap();
