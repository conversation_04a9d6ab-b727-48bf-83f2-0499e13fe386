import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app.component';
import { enableProdMode } from '@angular/core';
import { environment } from './environments/environment.prod';

if (environment.PRODUCT_MODE) {
    enableProdMode();
}

bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err));
