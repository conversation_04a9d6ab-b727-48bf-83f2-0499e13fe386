# Edit-Create-Dialog System Documentation

## Overview

The Edit-Create-Dialog system is a comprehensive, generic dialog solution for creating and editing entities in Angular applications. It consists of three main components that work together to provide a flexible, reusable dialog system with automatic form generation, validation, and data handling.

### Core Components

1. **EditCreateDialogComponent** - The main dialog component with dynamic form generation
2. **EditCreateDialogHandlerService** - Service for handling CRUD operations and data transformations
3. **EditCreateDialogConfigService** - Service for managing entity-specific dialog configurations

## Architecture Overview

```mermaid
graph TB
    A[Parent Component] --> B[EditCreateDialogComponent]
    B --> C[EditCreateDialogHandlerService]
    B --> D[EditCreateDialogConfigService]
    D --> E[Entity Configs]
    E --> F[Employee Config]
    E --> G[Position Config]
    E --> H[Probation Config]
    C --> I[API Services]
    C --> J[Toast Service]
    C --> K[<PERSON><PERSON><PERSON>ler <PERSON>]
```

## EditCreateDialogComponent

### Purpose
A generic, reusable dialog component that dynamically generates forms based on configuration objects. It supports both create and edit modes with automatic form validation, field dependencies, and custom actions.

### Key Features
- **Dynamic Form Generation**: Creates forms based on field configurations
- **Multiple Field Types**: Supports text, textarea, select, treeselect, datepicker, and checkbox fields
- **Flexible Layout**: Supports full-width and half-width field layouts with automatic grouping
- **Field Dependencies**: Fields can depend on other fields for dynamic behavior
- **Custom Validation**: Supports custom validators and error messages
- **Action System**: Configurable actions with custom logic and styling
- **Data Transformation**: Automatic data transformation for create/edit operations

### Interfaces

#### FieldConfig
```typescript
export interface FieldConfig {
  key: string;                    // Form control name
  label: string;                  // Display label
  type: 'select' | 'treeselect' | 'datepicker' | 'text' | 'textarea' | 'checkbox' | 'toggleSwitch';
  required?: boolean;             // Whether field is required
  validators?: ValidatorFn[];     // Custom validators
  width?: 'full' | 'half';       // Field width (default: full)
  disabled?: boolean | ((editMode: boolean, formValue: any) => boolean);
  hidden?: boolean | ((editMode: boolean, formValue: any) => boolean);
  config?: any;                   // Configuration for generic components
  templateRef?: string;           // For @ViewChild references
  placeholder?: string;           // Field placeholder text
  rows?: number;                  // For textarea fields
  dependsOn?: string;            // Field dependency for dynamic behavior
  customErrorMessage?: string;    // Custom validation error message
}
```

#### EditCreateDialogConfig
```typescript
export interface EditCreateDialogConfig {
  width?: string;                 // Dialog width (default: auto)
  createHeader?: string;          // Header for create mode
  editHeader?: string;           // Header for edit mode
  modal?: boolean;               // Whether dialog is modal (default: true)
  dismissableMask?: boolean;     // Whether clicking mask closes dialog (default: true)
  styleClass?: string;           // CSS classes for dialog (default: 'p-fluid')
  fields: FieldConfig[];         // Field configurations (required)
  actions: DialogAction[];       // Action configurations (required)
  handlerConfig?: DialogHandlerConfig; // Optional automatic data handling
}
```

#### DialogAction
```typescript
export interface DialogAction {
  label?: string;                // Button label
  icon?: string;                 // Button icon
  severity?: "success" | "info" | "warn" | "danger" | "help" | "primary" | "secondary" | "contrast" | null;
  disabled?: (formValue: any, formValid: boolean, editMode: boolean) => boolean;
  onClick: (formValue: any, editMode: boolean) => void | Promise<void>;
  type?: 'button' | 'submit';    // Button type (default: 'button')
  useDefaultStyle?: 'cancel' | 'confirm'; // Use predefined styling
}
```

### Component Inputs/Outputs

#### Inputs
- `visible: boolean` - Controls dialog visibility
- `editMode: boolean` - Determines if dialog is in edit mode
- `dialogConfig: EditCreateDialogConfig` - Dialog configuration
- `initialData: any` - Initial data for edit mode
- `showDefaultActions: boolean` - Whether to show default cancel/save actions

#### Outputs
- `visibleChange: EventEmitter<boolean>` - Emits when visibility changes
- `onSave: EventEmitter<{formValue: any, editMode: boolean}>` - Emits on save action
- `onCancel: EventEmitter<void>` - Emits on cancel action
- `onFieldChange: EventEmitter<{fieldKey: string, value: any, formValue: any}>` - Emits on field changes

### Key Methods

#### buildForm()
Creates reactive form based on field configurations with validators and change subscriptions.

#### populateForm()
Populates form with initial data in edit mode or resets form in create mode. Handles component reset for select/treeselect components.

#### isFieldDisabled(field: FieldConfig): boolean
Determines if a field should be disabled based on configuration and current state.

#### getDependencyValue(field: FieldConfig): any
Gets the value of a field's dependency for dynamic behavior.

#### executeAction(action: DialogAction)
Executes a dialog action with error handling for async operations.

### Field Layout System

The component supports intelligent field layout with automatic grouping:

- **Full Width Fields**: Take up the entire dialog width
- **Half Width Fields**: Automatically grouped in pairs using CSS Grid
- **Mixed Layouts**: Full and half-width fields can be mixed seamlessly

### Field Types

#### Text Input
```typescript
{
  key: 'fullname',
  label: 'Họ và tên',
  type: 'text',
  required: true,
  placeholder: 'Nhập họ và tên'
}
```

#### Select (Generic)
```typescript
{
  key: 'employeeId',
  label: 'Nhân viên',
  type: 'select',
  required: true,
  config: { ...EMPLOYEE_SELECT_CONFIG, service: employeeApiService }
}
```

#### TreeSelect (Generic)
```typescript
{
  key: 'departmentId',
  label: 'Phòng ban',
  type: 'treeselect',
  required: true,
  config: { ...DEPARTMENT_TREE_SELECT_CONFIG, service: departmentApiService }
}
```

#### Date Picker
```typescript
{
  key: 'birthday',
  label: 'Ngày sinh',
  type: 'datepicker',
  placeholder: 'Chọn ngày sinh'
}
```

#### Textarea
```typescript
{
  key: 'note',
  label: 'Ghi chú',
  type: 'textarea',
  rows: 3,
  placeholder: 'Nhập ghi chú'
}
```

#### Checkbox
```typescript
{
  key: 'isActive',
  label: 'Kích hoạt',
  type: 'checkbox',
  placeholder: 'Tài khoản được kích hoạt'
}
```

## EditCreateDialogHandlerService

### Purpose
Provides generic CRUD operation handling with automatic data transformation, error handling, and success notifications. Eliminates the need for repetitive save/cancel logic in components.

### Key Features
- **Generic Save Operations**: Handles both create and update operations
- **Data Transformation**: Automatic data transformation before API calls
- **Error Handling**: Integrated error handling with toast notifications
- **Success Callbacks**: Configurable success and cancel callbacks
- **Promise-based**: Returns promises for async operation handling

### Interfaces

#### DialogHandlerConfig
```typescript
export interface DialogHandlerConfig {
  service: any;                    // The service that handles CRUD operations
  createMethod: string;            // Method name for creating
  updateMethod: string;            // Method name for updating
  entityLabel?: string;            // Entity label for generating default messages
  createSuccessMessage?: string;   // Custom success message for create
  updateSuccessMessage?: string;   // Custom success message for update
  errorMessage?: string;           // Custom error message
  // Data transformation functions
  createDataTransform?: (formValue: any) => any;
  updateDataTransform?: (formValue: any) => any;
  initialDataTransform?: (rawData: any) => any;
  // Callback functions
  onSuccess?: () => void;          // Called after successful save
  onCancel?: () => void;           // Called when dialog is cancelled
}
```

#### DialogHandlerResult
```typescript
export interface DialogHandlerResult {
  success: boolean;
  error?: any;
}
```

### Key Methods

#### handleSave(formValue: any, editMode: boolean, config: DialogHandlerConfig): Promise<DialogHandlerResult>
Generic save handler that:
1. Determines operation type (create/update) based on editMode
2. Applies appropriate data transformation
3. Calls the configured service method
4. Handles success/error responses
5. Shows appropriate toast notifications
6. Executes success callbacks

#### handleCancel(config: DialogHandlerConfig): void
Generic cancel handler that executes the configured cancel callback.

### Usage Example
```typescript
const handlerConfig: DialogHandlerConfig = {
  service: this.employeeApiService,
  createMethod: 'createEmployee',
  updateMethod: 'updateEmployee',
  entityLabel: 'nhân viên',
  createDataTransform: (formValue: any) => ({
    code: formValue.code?.toUpperCase(),
    fullname: formValue.fullname,
    // ... other transformations
  }),
  onSuccess: () => {
    this.loadEmployees();
    this.hideDialog();
  }
};

// In dialog action
onClick: async (formValue: any, editMode: boolean) => {
  await this.editCreateDialogHandlerService.handleSave(formValue, editMode, handlerConfig);
}
```

## EditCreateDialogConfigService

### Purpose
Centralized service for managing entity-specific dialog configurations. Provides a clean, maintainable way to define and retrieve dialog configurations for different entity types.

### Key Features
- **Entity-based Configuration**: Configurations organized by entity type
- **Function Map Pattern**: Uses direct function mapping for scalability
- **Callback Integration**: Supports success and cancel callbacks
- **Reusable Configurations**: Promotes configuration reuse across components
- **Type Safety**: Strongly typed entity configurations

### Supported Entity Types
```typescript
export type DialogEntityType = 'employeePosition' | 'probation' | 'employee';
```

### Configuration Map
The service uses a direct function map pattern for efficient configuration retrieval:

```typescript
private readonly configMap: Record<string, DialogConfigFactory> = {
  employeePosition: (onSuccess, onCancel) => this.getEmployeePositionDialogConfig(onSuccess, onCancel),
  probation: (onSuccess, onCancel) => this.getProbationDialogConfig(onSuccess, onCancel),
  employee: (onSuccess, onCancel) => this.getEmployeeDialogConfig(onSuccess, onCancel)
};
```

### Key Methods

#### getDialogConfig(entityType: DialogEntityType, onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig
Main method for retrieving dialog configurations. Takes entity type and callbacks, returns complete dialog configuration.

#### Entity-Specific Configuration Methods

##### getEmployeePositionDialogConfig()
Configuration for employee position assignment dialogs:
- Employee selection (disabled in edit mode)
- Position selection (disabled in edit mode)
- Department tree selection
- Date range selection (from/to dates)
- Automatic data transformation for API calls

##### getProbationDialogConfig()
Configuration for employee probation dialogs:
- Employee selection
- Employee position selection (depends on employee)
- Probation end date
- Deadline date
- Note field
- Data transformation excluding unnecessary fields

##### getEmployeeDialogConfig()
Configuration for employee management dialogs:
- Employee code (auto-uppercase, disabled in edit mode)
- Personal information (fullname, nickname, birthday)
- Contact information (phone, email with validation)
- Identification (CMND/CCCD with validation)
- Notes
- Comprehensive validation with custom error messages

### Configuration Structure Example

Each entity configuration includes:

1. **Dialog Properties**: Width, headers, modal settings
2. **Handler Configuration**: Service methods, data transformations, callbacks
3. **Field Definitions**: Complete field configurations with validation
4. **Action Definitions**: Cancel and save actions with proper handlers

```typescript
private getEmployeeDialogConfig(onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig {
  const handlerConfig: DialogHandlerConfig = {
    service: this.employeeApiService,
    createMethod: 'createEmployee',
    updateMethod: 'updateEmployee',
    entityLabel: 'nhân viên',
    createDataTransform: (formValue: any) => ({
      code: formValue.code?.toUpperCase(),
      fullname: formValue.fullname,
      // ... other transformations
    }),
    onSuccess,
    onCancel
  };

  return {
    width: '650px',
    createHeader: 'Thêm nhân viên',
    editHeader: 'Sửa nhân viên',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    handlerConfig,
    fields: [
      // ... field configurations
    ],
    actions: [
      // ... action configurations
    ]
  };
}
```

## Usage Patterns

### Basic Usage in Components

```typescript
export class EmployeeListComponent {
  dialogVisible = false;
  editMode = false;
  selectedEmployee: any = null;
  dialogConfig: EditCreateDialogConfig;

  constructor(
    private editCreateDialogConfigService: EditCreateDialogConfigService
  ) {}

  showCreateDialog() {
    this.editMode = false;
    this.selectedEmployee = null;
    this.dialogConfig = this.editCreateDialogConfigService.getDialogConfig(
      'employee',
      () => this.onDialogSuccess(),
      () => this.onDialogCancel()
    );
    this.dialogVisible = true;
  }

  showEditDialog(employee: any) {
    this.editMode = true;
    this.selectedEmployee = employee;
    this.dialogConfig = this.editCreateDialogConfigService.getDialogConfig(
      'employee',
      () => this.onDialogSuccess(),
      () => this.onDialogCancel()
    );
    this.dialogVisible = true;
  }

  onDialogSuccess() {
    this.dialogVisible = false;
    this.loadEmployees(); // Refresh data
  }

  onDialogCancel() {
    this.dialogVisible = false;
  }
}
```

### Template Usage

```html
<edit-create-dialog
  [(visible)]="dialogVisible"
  [editMode]="editMode"
  [dialogConfig]="dialogConfig"
  [initialData]="selectedEmployee">
</edit-create-dialog>
```

### Custom Actions

```typescript
// Custom action with validation
{
  label: 'Lưu và tiếp tục',
  icon: 'pi pi-save',
  severity: 'success',
  type: 'submit',
  disabled: (formValue: any, formValid: boolean) => !formValid,
  onClick: async (formValue: any, editMode: boolean) => {
    const result = await this.editCreateDialogHandlerService.handleSave(
      formValue,
      editMode,
      handlerConfig
    );
    if (result.success) {
      // Continue with next action
      this.showNextDialog();
    }
  }
}
```

### Field Dependencies

```typescript
// Position field depends on employee selection
{
  key: 'employeePositionId',
  label: 'Vị trí',
  type: 'select',
  required: true,
  config: { ...EMPLOYEE_POSITION_BY_EMPLOYEE_SELECT_CONFIG, service: this.employeePositionApiService },
  dependsOn: 'employeeId' // This field will update when employeeId changes
}
```

### Conditional Field Disabling

```typescript
{
  key: 'employeeId',
  label: 'Nhân viên',
  type: 'select',
  required: true,
  disabled: (editMode) => editMode, // Disabled only in edit mode
  config: { ...EMPLOYEE_SELECT_CONFIG, service: this.employeeApiService }
}
```

### Hidden Fields

Fields can be hidden from the UI while still being part of the form. This is useful for ID fields needed for updates but not visible to users.

```typescript
// Always hidden field (e.g., for ID fields)
{
  key: 'id',
  label: 'ID',
  type: 'text',
  hidden: true
}

// Conditionally hidden field
{
  key: 'internalNotes',
  label: 'Internal Notes',
  type: 'textarea',
  hidden: (editMode, formValue) => !formValue.isAdmin // Hidden for non-admin users
}
```

**Note:** Hidden fields are still included in form validation and data submission. They're only hidden from the UI.

## Best Practices

### 1. Configuration Organization
- Keep entity configurations in the config service
- Use meaningful entity type names
- Group related fields logically
- Maintain consistent field ordering

### 2. Data Transformation
- Use transformation functions for data cleanup
- Handle date conversions appropriately
- Exclude unnecessary fields in transformations
- Maintain data integrity between create/update operations

### 3. Validation Strategy
- Use built-in validators when possible
- Implement custom validators for business rules
- Provide clear, user-friendly error messages
- Validate at both field and form levels

### 4. Action Design
- Keep action logic concise and focused
- Use async/await for API operations
- Handle errors gracefully
- Provide user feedback for all operations

### 5. Field Dependencies
- Use `dependsOn` for dynamic field behavior
- Reset dependent fields when parent changes
- Handle loading states for dependent data
- Validate dependency chains

### 6. Performance Considerations
- Use OnPush change detection when possible
- Implement proper component cleanup
- Avoid unnecessary form rebuilds
- Cache configuration objects when appropriate

## Common Patterns

### 1. Master-Detail Dialogs
```typescript
// Show employee dialog, then position dialog
showEmployeePositionFlow(employee: any) {
  this.showEmployeeDialog(employee, () => {
    this.showPositionDialog(employee.id);
  });
}
```

### 2. Multi-Step Creation
```typescript
// Create employee, then assign position
createEmployeeWithPosition() {
  const employeeConfig = this.configService.getDialogConfig('employee',
    () => this.showPositionDialog(),
    () => this.cancelFlow()
  );
  this.showDialog(employeeConfig);
}
```

### 3. Conditional Field Display
```typescript
// Show different fields based on user role
getFieldsForRole(role: string): FieldConfig[] {
  const baseFields = this.getBaseFields();
  if (role === 'admin') {
    return [...baseFields, ...this.getAdminFields()];
  }
  return baseFields;
}
```

## Error Handling

### Form Validation Errors
- Field-level validation with custom messages
- Form-level validation for complex rules
- Real-time validation feedback
- Accessibility-compliant error display

### API Operation Errors
- Automatic error handling through handler service
- Toast notifications for user feedback
- Error logging for debugging
- Graceful degradation for network issues

### Data Transformation Errors
- Validation before transformation
- Fallback values for missing data
- Type checking for critical fields
- Error boundaries for component protection

## Testing Strategies

### Unit Testing
- Test configuration generation
- Validate form building logic
- Test data transformation functions
- Mock service dependencies

### Integration Testing
- Test dialog opening/closing
- Validate form submission flows
- Test field dependency behavior
- Verify error handling paths

### E2E Testing
- Test complete user workflows
- Validate accessibility compliance
- Test responsive behavior
- Verify data persistence

## Migration Guide

### From Custom Dialogs
1. Extract field configurations from existing forms
2. Create entity configuration in config service
3. Replace custom dialog with edit-create-dialog
4. Update component logic to use new patterns
5. Test all functionality thoroughly

### Adding New Entity Types
1. Define entity type in DialogEntityType
2. Add configuration method to config service
3. Update config map with new entity
4. Create field configurations
5. Define actions and handlers
6. Test integration with existing components

## Troubleshooting

### Common Issues

#### Dialog Not Showing
- Check `visible` property binding
- Verify dialog configuration is set
- Ensure component is properly imported

#### Form Not Populating
- Verify `initialData` is provided in edit mode
- Check field key matching with data properties
- Ensure data transformation is correct

#### Validation Not Working
- Check validator configuration
- Verify required field settings
- Test custom validation functions

#### Actions Not Executing
- Check action configuration syntax
- Verify service method names
- Test error handling in action functions

#### Field Dependencies Not Working
- Verify `dependsOn` field key exists
- Check dependency value retrieval
- Test dependent field reset logic

### Debug Tips
- Use browser dev tools to inspect form values
- Check console for error messages
- Verify network requests for API calls
- Test with different data scenarios
- Use Angular DevTools for component inspection

## Future Enhancements

### Planned Features
- File upload field support
- Rich text editor integration
- Multi-select field types
- Conditional field visibility
- Form section grouping
- Advanced validation rules
- Internationalization support
- Theme customization options

### Performance Improvements
- Virtual scrolling for large forms
- Lazy loading for field components
- Optimized change detection
- Memory leak prevention
- Bundle size optimization

## Technical Implementation Details

### Form Building Process
1. **Configuration Parsing**: Dialog config is parsed to extract field definitions
2. **Form Control Creation**: Reactive form controls are created with validators
3. **Layout Generation**: Fields are arranged based on width specifications
4. **Component Binding**: Generic components are bound to form controls
5. **Validation Setup**: Real-time validation is configured
6. **Change Detection**: Form changes trigger dependency updates

### Data Flow
1. **Input Processing**: Initial data is transformed for form consumption
2. **User Interaction**: Form changes trigger validation and dependencies
3. **Submission**: Form data is validated and transformed for API calls
4. **Response Handling**: API responses trigger success/error handling
5. **State Updates**: Component state is updated based on operation results

### Component Lifecycle
1. **Initialization**: Form is built based on configuration
2. **Data Population**: Initial data populates form in edit mode
3. **User Interaction**: Real-time validation and dependency handling
4. **Submission**: Data transformation and API calls
5. **Cleanup**: Form reset and component cleanup on dialog close

This documentation provides a comprehensive guide to understanding, implementing, and maintaining the Edit-Create-Dialog system. The system's flexibility and reusability make it an essential component for any Angular application requiring dynamic form dialogs.
