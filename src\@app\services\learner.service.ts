import { LearnerEventType } from '@app/enums';
import { SyncType } from '@app/enums/sync-type.enum';
import { LearnerFromGcpDTO, SearchOptionsDTO } from '@app/models/dto';
import { CreateUserForLearnerDTO } from '@app/models/dto/user.dto';
import { PaginationRequest } from '@app/models/pagination';
import { <PERSON><PERSON><PERSON><PERSON>elper, StringHelper } from '@app/shared/helpers';
import { TeleBotService } from '@app/shared/services/tele-bot.service';
import { LearnerEventRepository, LearnerRepository, ProfileRepository, SyncErrorDataRepository } from '@database/mongodb/repositories';
import { ILearnerDocument, IProfileDocument, LearnerDocument } from '@database/mongodb/schemas';
import { MoodleUserEntity } from '@database/orm/entities/moodle-user.entity';
import { MoodleGroupRepository } from '@database/orm/repositories/moodle-group.repo';
import { MoodleUserRepository } from '@database/orm/repositories/moodle-user.repo';
import { Injectable, Logger } from '@nestjs/common';
import { v4 } from 'uuid';
import { GoogleSheetsService } from './google-sheet.service';
import { MoodleService } from './moodle.service';
import { ProfileService } from './profile.service';
import { UserService } from './user.service';

@Injectable()
export class LearnerService {
    private readonly logger = new Logger(LearnerService.name);

    constructor(
        private learnerRepository: LearnerRepository,
        private userService: UserService,
        private googleSheetsService: GoogleSheetsService,
        private teleBotService: TeleBotService,
        private syncErrorDataRepository: SyncErrorDataRepository,
        private moodleUserRepository: MoodleUserRepository,
        private moodleGroupRepository: MoodleGroupRepository,
        private learnerEventRepository: LearnerEventRepository,
        private moodleService: MoodleService,
        private profileService: ProfileService,
        private profileRepository: ProfileRepository,
    ) {}

    async syncLearnerFromSheetToMoodle() {
        const dataImported: LearnerFromGcpDTO[] = await this.getLearnerFromSheet();
        const moodleUsers: MoodleUserEntity[] = await this.moodleUserRepository.findBy({ deleted: 0 });
        const usernamesMoodle = new Set(moodleUsers.map(obj => obj.username));
        const newUsers = [];
        const invalidUsers = [];

        dataImported.forEach(obj => {
            if (usernamesMoodle.has(obj.code) || StringHelper.isEmpty(obj.email)) return;
            if (StringHelper.isEmpty(obj.fullname) || !StringHelper.isValidEmail(obj.email)) {
                invalidUsers.push({
                    data: [obj.code, obj.fullname, obj.email],
                    sheetIndexs: { code: 0, fullname: 1, email: 2 },
                    error: 'Fullname is blank or Email is invalid',
                });
            } else {
                const { firstName, lastName } = StringHelper.splitFullName(obj.fullname);
                newUsers.push({
                    ...obj,
                    firstName: StringHelper.isEmpty(firstName) ? StringHelper.HYPHEN : firstName,
                    lastName: StringHelper.isEmpty(lastName) ? StringHelper.HYPHEN : lastName,
                });
            }
        });

        if (!ArrayHelper.isEmpty(invalidUsers)) {
            this.syncErrorDataRepository.create({
                data: invalidUsers,
                type: SyncType.MOODLE_USER,
            });
        }

        const parts: any[] = ArrayHelper.partitionArray(newUsers, 30);
        for (let i = 0; i < parts.length; i++) {
            this.logger.debug(`Process part ${i}`);
            await this.moodleService.createMoodleUsers(parts[i]);
            await this.moodleService.addMoodleUsersToCohort(parts[i]);
        }
    }

    async syncLearnerFromSheetToPortal() {
        const dataImported: LearnerFromGcpDTO[] = await this.getLearnerFromSheet();
        const existeds = await this.learnerRepository.findAll({ code: { $in: dataImported.map(i => i.code) } });
        const existedCodes = existeds.map(i => i.code);

        const dataNeedUpdate: LearnerFromGcpDTO[] = [];
        let index: number;
        existeds.forEach(i => {
            index = dataImported.findIndex(d => d.code == i.code);
            if (index == -1) return;
            dataNeedUpdate.push({
                id: i.id,
                code: i.code,
                fullname: dataImported[index].fullname,
                email: dataImported[index].email,
                ec: dataImported[index].ec,
            });
        });

        await this.syncCreateLearner(dataImported.filter(i => !existedCodes.includes(i.code)));
        await this.syncUpdateLearner(dataNeedUpdate);
    }

    private async syncUpdateLearner(data: LearnerFromGcpDTO[]) {
        if (ArrayHelper.isEmpty(data)) return;
        const bulkProfileOps = [];
        const bulkLearnerOps = [];

        try {
            let dataItem: LearnerFromGcpDTO;
            for (let i = 0; i < data.length; i++) {
                dataItem = data[i];
                bulkProfileOps.push({
                    updateMany: {
                        filter: { id: dataItem.id },
                        update: {
                            fullname: dataItem.fullname,
                            email: dataItem.email,
                            textSearch: this.buildTextSearch(dataItem),
                        },
                    },
                });

                bulkLearnerOps.push({
                    updateMany: {
                        filter: { id: dataItem.id },
                        update: {
                            ec: dataItem.ec,
                        },
                    },
                });
            }

            await Promise.all([this.profileRepository.bulkWrite(bulkProfileOps), this.learnerRepository.bulkWrite(bulkLearnerOps)]);
        } catch (error) {
            this.teleBotService.notifyMessage(`syncUpdateLearner has error ${JSON.stringify(error?.message)}`);
            this.syncErrorDataRepository.create({
                type: SyncType.LEARNER,
                message: error?.detail || error?.message,
            });
        }
    }

    private async syncCreateLearner(data: LearnerFromGcpDTO[]) {
        if (ArrayHelper.isEmpty(data)) return;
        try {
            const createUserData: CreateUserForLearnerDTO[] = [];
            const createProfileData: IProfileDocument[] = [];
            const createLearnerData: ILearnerDocument[] = [];

            let uuid;
            data.forEach((i: LearnerFromGcpDTO) => {
                uuid = v4();
                createUserData.push({ id: uuid, code: i.code });
                createProfileData.push({
                    id: uuid,
                    fullname: i.fullname,
                    email: i.email,
                    textSearch: this.buildTextSearch(i),
                });
                createLearnerData.push({
                    id: uuid,
                    code: i.code,
                    ec: i.ec,
                });
            });

            await Promise.all([
                this.learnerRepository.insertMany(createLearnerData),
                this.userService.createManyUserForLearner(createUserData, null),
                this.profileService.createProfiles(createProfileData, null),
            ]);
        } catch (error) {
            this.teleBotService.notifyMessage(`syncCreateLearner has error ${JSON.stringify(error?.message)}`);
            this.syncErrorDataRepository.create({
                type: SyncType.LEARNER,
                message: error?.detail || error?.message,
            });
        }
    }

    private buildTextSearch(data: LearnerFromGcpDTO) {
        return StringHelper.removeVietnameseChars(`${data.code} - ${data.fullname} - ${data.email} - ${data.ec}`);
    }

    private async getLearnerFromSheet(): Promise<LearnerFromGcpDTO[]> {
        const data: any[] = await this.googleSheetsService.readSheet(SyncType.LEARNER);
        return data
            .filter(obj => obj && !StringHelper.isEmpty(obj[1]))
            .map(item => {
                return {
                    code: item[0],
                    fullname: item[1],
                    ec: item[4],
                    email: item[8],
                };
            });
    }

    async searchText(text: string) {
        return await this.profileRepository.textSearchForLearner(StringHelper.removeVietnameseChars(text));
    }

    async syncGroupMembersToMoodle() {
        const groupMemberMoodleData = await this.moodleGroupRepository.getMemberInGroup();
        const userMoodleData = await this.moodleUserRepository.findBy({ deleted: 0 });

        const userMoodleMap = new Map();
        const groupMoodleMap = new Map();
        const memberMoodleMap = new Map();
        let groupNamekey;
        let members = [];
        groupMemberMoodleData.forEach(d => {
            if (!StringHelper.isEmpty(d.groupName)) {
                groupNamekey = d.groupName.toLowerCase();
                groupMoodleMap.set(groupNamekey, d.groupId);

                if (!StringHelper.isEmpty(d.userId)) {
                    if (memberMoodleMap.has(groupNamekey)) {
                        members = memberMoodleMap.get(groupNamekey);
                        members.push(d.username);
                    } else {
                        members = [d.username];
                    }
                    memberMoodleMap.set(groupNamekey, members);
                } else {
                    memberMoodleMap.set(groupNamekey, []);
                }
            }
        });
        userMoodleData.forEach(u => {
            userMoodleMap.set(u.username, u.id);
        });
        const mongoData = await this.learnerEventRepository.findAllEventsLatestByEventTypes([LearnerEventType.ENROLL, LearnerEventType.RESERVED, LearnerEventType.LEFT]);
        const addedMembers = [];
        const removeMembers = [];

        let moodleMembers: string[];
        let moodleGroupId: string;
        let moodleUserId: string;
        mongoData.forEach(e => {
            moodleMembers = memberMoodleMap.get(e.courseCode);
            moodleGroupId = groupMoodleMap.get(e.courseCode);
            moodleUserId = userMoodleMap.get(e.learnerCode);
            if (!moodleUserId) {
                return;
            }
            if (e.eventType == LearnerEventType.ENROLL.toString()) {
                if (moodleMembers && !moodleMembers.includes(e.learnerCode)) {
                    addedMembers.push({ groupId: moodleGroupId, userId: moodleUserId });
                }
            } else {
                if (moodleMembers && moodleMembers.includes(e.learnerCode)) {
                    removeMembers.push({ groupId: moodleGroupId, userId: moodleUserId });
                }
            }
        });
        await this.moodleService.addMoodleGroupUsers(addedMembers);
        await this.moodleService.deleteMoodleGroupUsers(removeMembers);
    }

    async getLearnerOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.learnerRepository.getOptions(request, body);
    }
}
