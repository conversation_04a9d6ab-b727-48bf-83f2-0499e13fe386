import { inject } from '@angular/core';
import { ProbationApiService } from '@shared/services';
import { TableConfig, PLACEHOLDER_FUNCTIONS } from '../generic-p-table.interfaces';

export const probationTableConfig = (): TableConfig => {
  const probationApiService = inject(ProbationApiService);

  return {
    title: 'Quản lý thử việc',
    entityName: 'thử việc',
    service: probationApiService,
    method: 'getProbations',
    columns: [
      { field: 'employee.code', header: 'Mã nhân viên', width: 'min-w-[100px]', sortable: true, type: 'text' },
      { field: 'employee.name', header: 'Tên nhân viên', width: 'min-w-[200px]', sortable: true, type: 'text' },
      { field: 'position.name', header: 'Vị trí', width: 'min-w-[150px]', sortable: true, type: 'text' },
      { field: 'department.name', header: 'Phòng ban', width: 'min-w-[150px]', sortable: true, type: 'text' },
      { field: 'fromDate', header: '<PERSON><PERSON><PERSON> bắt đầu', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'toDate', header: 'Ngày kết thúc', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'deadline', header: 'Hạn chót', width: 'min-w-[150px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'status', header: 'Trạng thái', width: 'min-w-[120px]', sortable: true, type: 'tag', tagType: 'probationStatus' }
    ],
    features: {
      virtualScroll: true,
      toolbar: true
    },
    actions: [
      {
        location: 'toolbar',
        useDefaultStyle: 'add',
        onClick: PLACEHOLDER_FUNCTIONS.OPEN_NEW
      },
      {
        location: 'row',
        useDefaultStyle: 'edit',
        onClick: PLACEHOLDER_FUNCTIONS.EDIT
      }
    ]
  };
};
