<div class="score-table ml-2 my-4">
    <!-- Iterate through all pairs -->
    <ng-container *ngFor="let pair of pairs">
        <div class="pair rounded-md shadow">
            <div class="header">{{ pair.header }}</div>
            <div class="value">{{ pair.value }}</div>
        </div>
    </ng-container>
    <!-- Comment element -->
    <div class="pair rounded-md shadow" *ngIf="hasComment">
        <div class="header">Nhận xét</div>
        <div class="value">
                <button pButton 
                        icon="pi pi-align-center" severity="secondary" 
                        class="p-button-text rounded-[8px] px-[6px] py-[3px]"
                        (click)="openPopupComment()"></button>
        </div>
    </div>
</div>
<p-dialog header="Đánh giá của giáo viên" [modal]="true" [(visible)]="visible" dismissableMask="true" [style]="{ width: '50%', height: 'auto' }" styleClass="whitespace-pre-wrap">{{ note }}</p-dialog>
