import { EmployeeStatusEnum, ProbationStatusEnum } from '@app/enums';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsDateString, IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ProfileDTO } from './profile.dto';

export class CreateEmployeeDTO extends ProfileDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toUpperCase())
    readonly code: string;

    @ApiProperty({ description: 'note' })
    @IsOptional()
    @IsString()
    readonly note: string;
}

export class UpdateEmployeeDTO extends ProfileDTO {
    @ApiProperty({ description: 'code' })
    @IsString()
    @IsNotEmpty()
    @Transform(({ value }) => value?.toUpperCase())
    readonly code: string;

    @ApiProperty({ description: 'note' })
    @IsOptional()
    @IsString()
    readonly note: string;
}

export class AssignEmployeePositionDTO {
    @ApiProperty({ description: 'employeeId' })
    @IsString()
    @IsNotEmpty()
    readonly employeeId: string;

    @ApiProperty({ description: 'positionId' })
    @IsString()
    @IsNotEmpty()
    readonly positionId: string;

    @ApiProperty({ description: 'departmentId' })
    @IsString()
    @IsNotEmpty()
    readonly departmentId: string;

    @ApiProperty({ description: 'fromDate' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    readonly fromDate: Date;
}

export class UpdateEmployeePositionDTO {
    @ApiProperty({ description: 'id' })
    @IsString()
    @IsNotEmpty()
    readonly id: string;

    @ApiProperty({ description: 'positionId' })
    @IsString()
    @IsNotEmpty()
    readonly positionId: string;

    @ApiProperty({ description: 'departmentId' })
    @IsString()
    @IsNotEmpty()
    readonly departmentId: string;

    @ApiProperty({ description: 'fromDate' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    readonly fromDate: Date;
}

export class ProbationDTO {
    @ApiProperty({ description: 'employeePositionId' })
    @IsString()
    @IsNotEmpty()
    readonly employeePositionId: string;

    @ApiProperty({ description: 'toDate' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    readonly toDate: Date;

    @ApiProperty({ description: 'deadline' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    readonly deadline: Date;

    @ApiProperty({ description: 'isManual' })
    @IsBoolean()
    readonly isManual: boolean;

    @ApiProperty({ description: 'status' })
    @IsEnum(ProbationStatusEnum)
    readonly status: ProbationStatusEnum;
}

export class SearchEmployeeDTO {
    @IsString()
    @IsOptional()
    code: string;
    @ApiProperty({ description: 'fromDateF' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    fromDateF: Date;
    @ApiProperty({ description: 'fromDateT' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    fromDateT: Date;
    @ApiProperty({ description: 'toDateF' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    toDateF: Date;
    @ApiProperty({ description: 'toDateT' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    toDateT: Date;
    @IsArray()
    @IsEnum(EmployeeStatusEnum, { each: true })
    @IsOptional()
    statuses: EmployeeStatusEnum[];
    @IsString()
    @IsOptional()
    fullname: string;
    @IsString()
    @IsOptional()
    nickname: string;
    @IsString()
    @IsOptional()
    phone: string;
    @IsString()
    @IsOptional()
    email: string;
    @IsArray()
    @IsOptional()
    departmentIds: string[];
    @IsArray()
    @IsOptional()
    positionIds: string[];
}

export class RetireDTO {
    @ApiProperty({ description: 'id' })
    @IsString()
    @IsNotEmpty()
    readonly id: string;

    @ApiProperty({ description: 'toDate' })
    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsOptional()
    readonly toDate: Date;
}
