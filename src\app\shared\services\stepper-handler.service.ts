import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';
import {
  StepperConfig,
  StepHandlerConfig,
  StepperState,
  StepperResult
} from '../components/generic-stepper/stepper.interfaces';

@Injectable({
  providedIn: 'root'
})
export class StepperHandlerService {

  constructor(
    private toastService: ToastService,
    private errorHandlerService: ErrorHandlerService
  ) { }

  // PUBLIC ACTION METHODS - for use in config onClick handlers

  goBack(stepperState: StepperState, activateCallback?: (step: number) => void): void {
    if (stepperState.currentStep > 1) {
      stepperState.currentStep--;

      // When going back to a step that has data, set it to 'view' mode
      if (stepperState.stepData[stepperState.currentStep]) {
        stepperState.stepModes[stepperState.currentStep] = 'view';
      }

      if (activateCallback) {
        activateCallback(stepperState.currentStep);
      }
    }
  }

  enableEdit(stepperState: StepperState): void {
    // Set current step to 'edit' mode
    stepperState.stepModes[stepperState.currentStep] = 'edit';
  }

  private navigateToNextStep(
    stepperState: StepperState,
    stepperConfig: StepperConfig,
    activateCallback?: (step: number) => void
  ): void {
    // Check if this is the last step & complete the stepper if onComplete is provided
    if (stepperState.currentStep === 3) {
      if (stepperConfig.onComplete) stepperConfig.onComplete();
    } else {
      // Advance to next step
      console.log(`Navigating to next step from ${stepperState.currentStep}, mode ${stepperState.stepModes[stepperState.currentStep]}`);
      stepperState.currentStep++;
      console.log(`Navigated to step ${stepperState.currentStep}, mode ${stepperState.stepModes[stepperState.currentStep]}`);

      if (activateCallback) {
        activateCallback(stepperState.currentStep);
      }
    }
  }

  skipStep(_stepperState: StepperState, stepperConfig: StepperConfig, hideDialogCallback?: () => void): void {
    if (hideDialogCallback) {
      hideDialogCallback();
    } else if (stepperConfig.onComplete) {
      stepperConfig.onComplete();
    }
  }

  cancel(stepperConfig: StepperConfig): void {
    if (stepperConfig.onCancel) stepperConfig.onCancel();
  }

  async goNext(
    stepperState: StepperState,
    stepperConfig: StepperConfig,
    activateCallback?: (step: number) => void,
    currentStepConfig?: StepHandlerConfig
  ): Promise<void> {
    const currentStepMode = stepperState.stepModes[stepperState.currentStep];

    // If in 'view' mode, just navigate without API call
    if (currentStepMode === 'view') {
      this.navigateToNextStep(stepperState, stepperConfig, activateCallback);
      return Promise.resolve();
    }

    // For 'create' and 'edit' modes, make API call
    if (!currentStepConfig) return Promise.resolve();

    stepperState.isLoading = true;

    // Determine if this is an update or create operation
    const isUpdate = currentStepMode === 'edit';

    // Get current step's entityLabel
    const currentStep = stepperConfig.steps.find(s => s.value === stepperState.currentStep);
    const entityLabel = currentStep?.entityLabel;

    // Execute the step action and return a Promise
    try {
      const result = await this.executeStepAction(currentStepConfig, stepperState, isUpdate, entityLabel);
      if (result.success) {
        // Transform and store the result data
        const form = stepperState.forms[stepperState.currentStep];
        if (form) {
          const formValue = form.getRawValue();

          // Apply commonDataTransform first if it exists
          const commonTransformed = currentStepConfig?.commonDataTransform
            ? currentStepConfig.commonDataTransform(formValue, stepperState)
            : formValue;

          // Then apply stepDataTransform on top of common transform
          if (currentStepConfig?.stepDataTransform) {
            const stepTransformed = currentStepConfig.stepDataTransform(result.data, formValue, stepperState);
            stepperState.stepData[stepperState.currentStep] = { ...commonTransformed, ...stepTransformed };
          } else {
            stepperState.stepData[stepperState.currentStep] = { ...commonTransformed, ...result.data };
          }
        } else {
          stepperState.stepData[stepperState.currentStep] = result.data;
        }

        // After successful API call, set current step to 'view' mode
        stepperState.stepModes[stepperState.currentStep] = 'view';
        console.log(`Set step ${stepperState.currentStep} to view mode`);

        // Navigate to next step
        this.navigateToNextStep(stepperState, stepperConfig, activateCallback);
      } else {
        console.error('Step execution failed:', result.error);
      }
    } catch (error) {
      console.error('Step execution failed:', error);
    } finally {
      stepperState.isLoading = false;
    }
  };


  /**
   * Execute a step action (create or update)
   */
  private async executeStepAction(
    stepHandlerConfig: StepHandlerConfig,
    stepperState: StepperState,
    isUpdate: boolean = false,
    entityLabel?: string
  ): Promise<StepperResult> {
    try {
      let serviceCall: Observable<any>;
      let successMessage: string;
      let transformedData: any;

      // Get raw form values
      const form = stepperState.forms[stepperState.currentStep];
      if (!form || form.invalid) {
        return { success: false, error: 'Form is invalid' };
      }
      const formValue = form.getRawValue();

      // Apply commonDataTransform first if it exists
      const commonTransformed = stepHandlerConfig.commonDataTransform
        ? stepHandlerConfig.commonDataTransform(formValue, stepperState)
        : formValue;

      // Then apply specific transform (create or update) on top of common transform
      const specificTransformFunction = isUpdate ? stepHandlerConfig.updateDataTransform : stepHandlerConfig.createDataTransform;
      transformedData = specificTransformFunction
        ? { ...commonTransformed, ...specificTransformFunction(formValue, stepperState) }
        : commonTransformed;

      // Call the service method
      const methodName = isUpdate ? stepHandlerConfig.updateMethod : stepHandlerConfig.createMethod;
      if (!methodName) {
        return { success: false, error: `Method not found in config` };
      }
      const serviceMethod = stepHandlerConfig.service[methodName];
      if (!serviceMethod) {
        return { success: false, error: `Method ${methodName} not found in service` };
      }

      serviceCall = serviceMethod.call(stepHandlerConfig.service, transformedData);
      successMessage = this.getDefaultSuccessMessage(entityLabel, isUpdate);

      return this.executeServiceCall(serviceCall, successMessage, stepHandlerConfig);
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
     * Execute the service call with unified error handling and success logic
     */
  private async executeServiceCall(
    serviceCall: Observable<any>,
    successMessage: string,
    handlerConfig: StepHandlerConfig
  ): Promise<StepperResult> {
    return new Promise((resolve) => {
      serviceCall.subscribe({
        next: (response) => {
          const { hasError } = this.errorHandlerService.handleInternal(response);
          if (!hasError) {
            this.toastService.showSuccess(successMessage);
            resolve({ success: true });
          } else {
            resolve({ success: false, error: response });
          }
        },
        error: (error) => {
          resolve({ success: false, error });
        }
      });
    });
  }




  /**
   * Generate default success/error message based on entity label (for popover operations)
   */
  private getDefaultSuccessMessage(entityLabel?: string, isUpdate: boolean = false): string {
    const action = isUpdate ? 'Cập nhật' : 'Tạo';
    return entityLabel ? `${action} ${entityLabel} thành công` : `${action} thành công`
  }

  private getDefaultErrorMessage(entityLabel?: string, isUpdate: boolean = false): string {
    const action = isUpdate ? 'cập nhật' : 'tạo';
    return entityLabel ? `Có lỗi xảy ra khi ${action} ${entityLabel}` : `Có lỗi xảy ra khi ${action}`;
  }


  private showSuccessMessage(entityLabel?: string, isUpdate: boolean = false): void {
    const message = isUpdate
      ? this.getDefaultUpdateMessage(entityLabel)
      : this.getDefaultCreateMessage(entityLabel);

    this.toastService.showSuccess(message);
  }

  private showErrorMessage(entityLabel?: string, isUpdate: boolean = false): void {
    const message = this.getDefaultErrorMessage(entityLabel, isUpdate);
    this.toastService.showError(message);
  }

  private getDefaultCreateMessage(entityLabel?: string): string {
    return entityLabel ? `Tạo ${entityLabel} thành công` : 'Tạo thành công';
  }

  private getDefaultUpdateMessage(entityLabel?: string): string {
    return entityLabel ? `Cập nhật ${entityLabel} thành công` : 'Cập nhật thành công';
  }


}
