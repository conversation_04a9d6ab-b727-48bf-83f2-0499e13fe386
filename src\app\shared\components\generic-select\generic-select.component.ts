import { CommonModule } from '@angular/common';
import { Component, ContentChild, ElementRef, forwardRef, Input, OnChanges, OnDestroy, OnInit, SimpleChanges, TemplateRef, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ErrorHandlerService } from '@shared/services';
import { PaginationRequest } from '@shared/services';
import { SelectLazyLoadEvent, SelectModule } from 'primeng/select';
import { TagModule } from 'primeng/tag';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';

/**
 * Generic Select Component - Modular Version with Dependency Tracking
 *
 * This component provides automatic dependency tracking and reloading capabilities.
 *
 * Key features:
 * - Automatic dependency tracking with [dependencyValue] input
 * - Config-based dependency management with onDependencyChange callback
 * - Automatic data reloading when dependencies change
 * - Automatic selection clearing when dependencies change
 * - Public methods for manual control (reloadData, clearData)
 *
 * Basic usage:
 * ```typescript
 * const config: SelectConfig = {
 *   service: employeePositionApiService,
 *   method: 'getEmployeePositionOptions',
 *   idField: 'id',
 *   labelFormatter: (item) => `${item.employee.code} - ${item.position.name}`,
 *   valueField: 'id',
 *   templateName: 'employeePosition'
 * };
 * ```
 *
 * Dependency tracking usage:
 * ```html
 * <generic-p-select
 *   [config]="employeePositionConfig"
 *   [dependencyValue]="form.get('employeeId')?.value"
 *   formControlName="employeePositionId" />
 * ```
 *
 * Dependency config with callback:
 * ```typescript
 * const config: SelectConfig = {
 *   service: employeePositionApiService,
 *   method: 'getEmployeePositionOptions',
 *   // ... other config
 *   onDependencyChange: (employeeId) => {
 *     return employeeId ? { andQ: { employeeId } } : {};
 *   }
 * };
 * ```
 *

 *     if (employeeId) query.employeeId = employeeId;
 *     if (statusId) query.statusId = statusId;
 *     
 *     return Object.keys(query).length > 0 ? { andQ: query } : {};
 *   }
 * };
 * ```
 */

export interface SelectConfig<T = any> {
    // API-based configuration (optional when using static options)
    service?: any; // The service to call
    method?: string; // Method name to call on the service
    labelFormatter?: (item: T) => string; // How to format the label
    valueField?: string; // Which field to use as value
    searchFields?: string[]; // Fields to search in
    sortField?: string; // Default sort field
    placeholder?: string;

    // Static options configuration (alternative to API-based)
    staticOptions?: any[]; // Array of static options
    optionLabel?: string; // Property name for label (e.g., 'label', 'name')
    optionValue?: string; // Property name for value (e.g., 'value', 'id')

    // New customization options
    baseQuery?: any; // Static query that's always applied (e.g., departmentId filter)
    queryBuilder?: (searchTerm: string, baseQuery?: any) => any; // Custom query builder
    additionalParams?: any; // Additional parameters to pass to the service
    transformResponse?: (response: any) => any; // Transform the response before processing
    // Template name for built-in templates
    templateName?: string; // Name of the built-in template to use ('employee', 'position', 'department', 'role')
    // Simple ID field name for handling pre-selected values (defaults to 'id')
    idField?: string; // Field name to use when querying by ID (e.g., 'id', '_id', 'code')
    // Support for POST requests with body data
    usePostMethod?: boolean; // Whether to use POST method with body data instead of GET with query params
    bodyBuilder?: (searchTerm: string, baseQuery?: any, idQuery?: any) => any; // Custom body builder for POST requests
    // Dependency tracking for automatic reloading (use [dependencyValue] input)
    // Callback to update baseQuery when dependency changes
    onDependencyChange?: (dependencyValue: any) => any;
}

@Component({
    selector: 'generic-p-select',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        SelectModule,
        TagModule,
        GenericTagComponent
    ],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => GenericSelectComponent),
            multi: true
        }
    ],
    templateUrl: './generic-select.component.html'
})
export class GenericSelectComponent<T = any> implements OnInit, OnDestroy, OnChanges, ControlValueAccessor {
    @Input() id: string = '';
    @Input() placeholder: string = 'Chọn...';
    @Input() disabled: boolean = false;
    @Input() filter: boolean = true;
    @Input() virtualScroll: boolean = true;
    @Input() lazy: boolean = true;
    @Input() virtualScrollItemSize: number = 38;
    @Input() appendTo: string = 'body';
    @Input() styleClass: string = 'w-full';
    @Input() showClear: boolean = false;
    @Input() selectItemPerPage: number = 10;
    @Input() config!: SelectConfig<T>;
    @Input() additionalFilters: any = {}; // Dynamic filters that can change
    @Input() dependencyValue: any = null; // Value that this select depends on

    @ContentChild('selectedItem') selectedItemTemplate?: TemplateRef<any>;
    @ContentChild('item') itemTemplate?: TemplateRef<any>;

    @ViewChild('genericSelect', { read: ElementRef }) genericSelect!: any;

    // Data for dropdown
    items: T[] = [];
    options: any[] = [];

    // Loading state
    loading: boolean = false;

    // Pagination properties
    offset: number = 0;

    // Filter property
    filterValue: string = '';

    // Static mode flag - calculated once and updated on config changes
    isStaticMode: boolean = false;

    // Debouncing for filter
    private filterSubject = new Subject<string>();

    // ControlValueAccessor properties
    value: any = null;
    onChange = (value: any) => { };
    onTouched = () => { };

    virtualScrollOptions = {
        showLoader: false,
        lazy: true,
        onLazyLoad: this.onLazyScrollLoad.bind(this)
    };

    constructor(
        private errorHandlerService: ErrorHandlerService,
        private cd: ChangeDetectorRef
    ) { }

    private updateStaticMode() {
        this.isStaticMode = !!(this.config?.staticOptions && this.config.staticOptions.length > 0);
    }

    ngOnInit() {
        if (!this.config) {
            throw new Error('GenericSelectComponent requires a config input');
        }

        // Calculate static mode
        this.updateStaticMode();
    }

    ngAfterViewInit() {
        // Validate configuration
        if (!this.isStaticMode && (!this.config.service || !this.config.method)) {
            throw new Error('GenericSelectComponent requires either staticOptions or both service and method in config');
        }

        if (this.isStaticMode) {
            // Load static options immediately
            this.loadStaticOptions();
        } else {
            // Set up debounced filter for API mode
            this.filterSubject.pipe(
                debounceTime(200),
                distinctUntilChanged()
            ).subscribe(filterValue => {
                this.filterValue = filterValue;
                this.loadData(true);
            });
        }
    }

    /**
     * Load static options and convert them to the expected format
     */
    private loadStaticOptions() {
        if (!this.config.staticOptions) return;
        this.loading = true;

        const optionLabel = this.config.optionLabel || 'label';
        const optionValue = this.config.optionValue || 'value';

        // Use setTimeout to ensure PrimeNG is fully initialized
        setTimeout(() => {
            if (!this.config.staticOptions) return;

            this.items = [...this.config.staticOptions];
            this.options = this.config.staticOptions.map(item => ({
                ...item,
                label: item[optionLabel],
                value: item[optionValue]
            }));
            this.loading = false;
        }, 0);
    }

    ngOnChanges(changes: SimpleChanges) {
        // Handle config changes - recalculate static mode
        if (changes['config'] && !changes['config'].firstChange) {
            this.updateStaticMode();
            if (this.isStaticMode) {
                this.loadStaticOptions();
                return;
            }
        }

        // For API mode only - reload data when additionalFilters change
        if (changes['additionalFilters'] && !changes['additionalFilters'].firstChange && !this.isStaticMode) {
            this.loadData(true);
        }

        // For API mode only - reload data when dependencyValue changes
        if (changes['dependencyValue'] && !changes['dependencyValue'].firstChange && !this.isStaticMode) {
            const currentValue = changes['dependencyValue'].currentValue;
            const previousValue = changes['dependencyValue'].previousValue;

            // Only reload if the value actually changed
            if (currentValue !== previousValue) {
                // Clear current selection when dependency changes
                this.value = null;
                this.onChange(null);

                // Update baseQuery using onDependencyChange callback if available
                if (this.config.onDependencyChange) {
                    this.config.baseQuery = this.config.onDependencyChange(currentValue);
                }

                // Reload data if dependency has a value, otherwise clear data
                if (currentValue !== null && currentValue !== undefined) {
                    this.loadData(true);
                } else {
                    this.items = [];
                    this.options = [];
                }
            }
        }

        // For API mode only - reload data when config changes (for cases where config object is reassigned)
        if (changes['config'] && !changes['config'].firstChange && !this.isStaticMode) {
            this.loadData(true);
        }
    }

    ngOnDestroy() {
        this.filterSubject.complete();
    }

    onLazyScrollLoad(event: SelectLazyLoadEvent) {
        // Skip lazy loading for static mode
        if (this.isStaticMode) {
            return;
        }

        const first = event.first ?? 0;
        if (first === this.offset && this.options.length > 0) {
            return;
        }

        this.offset = event.first || 0;
        this.loadData();
    }

    loadData(resetData: boolean = false) {
        // If in static mode, don't load from API
        if (this.isStaticMode) {
            this.loadStaticOptions();
            return;
        }

        this.loading = true;

        if (resetData) {
            this.items = [];
            // this.options = [];
            this.offset = 0;
        }

        // Build query using custom query builder or default logic
        let query = {};
        let body = null;

        if (this.config.usePostMethod && this.config.bodyBuilder) {
            // Use custom body builder for POST requests (no ID query for regular data loading)
            body = this.config.bodyBuilder(this.filterValue, {
                ...this.config.baseQuery,
                ...this.additionalFilters
            }, null);
        } else if (this.config.queryBuilder) {
            // Use custom query builder
            query = this.config.queryBuilder(this.filterValue, {
                ...this.config.baseQuery,
                ...this.additionalFilters
            });
        } else {
            // Default query building logic
            const baseQuery = { ...this.config.baseQuery, ...this.additionalFilters };

            if (this.filterValue && this.filterValue.trim()) {
                query = {
                    ...baseQuery,
                    search: this.filterValue.trim()
                };
            } else if (Object.keys(baseQuery).length > 0) {
                query = baseQuery;
            }
        }

        const params: PaginationRequest = {
            limit: this.selectItemPerPage,
            offset: this.offset,
            sort: this.config.sortField || 'code,1',
            query: this.config.usePostMethod ? null : query,
            ...this.config.additionalParams
        };

        // Call the service method dynamically
        if (!this.config.service || !this.config.method) {
            console.error('Service and method are required for API mode');
            this.loading = false;
            return;
        }

        const serviceMethod = this.config.service[this.config.method];
        if (!serviceMethod) {
            console.error(`Method ${this.config.method} not found on service`);
            this.loading = false;
            return;
        }

        // Call service method with or without body based on configuration
        const serviceCall = this.config.usePostMethod
            ? serviceMethod.call(this.config.service, params, body)
            : serviceMethod.call(this.config.service, params);

        serviceCall.subscribe({
            next: (response: any) => {
                // Apply response transformation if provided
                const transformedResponse = this.config.transformResponse
                    ? this.config.transformResponse(response)
                    : response;

                const { hasError, data } = this.errorHandlerService.handleInternal(transformedResponse);
                if (!hasError) {
                    if (resetData) {
                        this.items = data.items || [];
                    } else {
                        this.items = [...this.items, ...(data.items || [])]
                            .filter((item: any, index, self) => index === self.findIndex((i: any) => i.id === item.id));
                    }

                    this.options = this.items.map(item => ({
                        ...item,
                        label: this.config.labelFormatter ? this.config.labelFormatter(item) : (item as any).label || (item as any).name,
                        value: this.config.valueField ? (item as any)[this.config.valueField] : (item as any).value || (item as any).id
                    }));

                    this.loading = false;
                }
            },
            error: () => {
                this.loading = false;
            }
        });
        console.log(this.options);
    }

    onFilter(event: any) {
        const filterValue = event.filter || '';

        if (this.isStaticMode) {
            // Client-side filtering for static options
            this.filterStaticOptions(filterValue);
        } else {
            // Server-side filtering for API mode
            this.filterSubject.next(filterValue);
        }
    }

    /**
     * Filter static options on the client side
     */
    private filterStaticOptions(filterValue: string) {
        if (!this.config.staticOptions) return;

        const optionLabel = this.config.optionLabel || 'label';

        if (!filterValue.trim()) {
            // Show all options when no filter
            this.loadStaticOptions();
        } else {
            // Filter options based on label
            const filteredOptions = this.config.staticOptions.filter(item =>
                item[optionLabel]?.toLowerCase().includes(filterValue.toLowerCase())
            );

            const optionValue = this.config.optionValue || 'value';
            this.items = [...filteredOptions];
            this.options = filteredOptions.map(item => ({
                ...item,
                label: item[optionLabel],
                value: item[optionValue]
            }));
        }
    }

    loadSpecificItem(itemId: any): Promise<void> {
        return new Promise<void>((resolve) => {
            // If in static mode, find the item in static options
            if (this.isStaticMode) {
                const optionValue = this.config.optionValue || 'value';
                const existingOption = this.options.find(option => option.value === itemId);
                if (!existingOption) {
                    // Try to find in static options
                    const staticItem = this.config.staticOptions?.find(item => item[optionValue] === itemId);
                    if (staticItem) {
                        const optionLabel = this.config.optionLabel || 'label';
                        const newOption = {
                            ...staticItem,
                            label: staticItem[optionLabel],
                            value: staticItem[optionValue]
                        };
                        this.items.unshift(staticItem);
                        this.options.unshift(newOption);
                    }
                }
                resolve();
                return;
            }

            // Use the idField (defaults to 'id') to build a simple query
            const idFieldName = this.config.idField || 'id';
            const idQuery = { [idFieldName]: itemId };
            let body = null;
            let query = idQuery;

            if (this.config.usePostMethod && this.config.bodyBuilder) {
                // Use custom body builder for POST requests with ID query
                body = this.config.bodyBuilder('', {}, idQuery);
                query = {};
            }

            const params: PaginationRequest = {
                limit: 1, // We only need one item
                offset: 0,
                sort: this.config.sortField || 'code,1',
                ...this.config.additionalParams
            };

            if (query) {
                params.query = query;
            }

            // Use the same service method as regular data loading
            if (!this.config.service || !this.config.method) {
                console.error('Service and method are required for API mode');
                this.loadData(true);
                resolve();
                return;
            }

            const serviceMethod = this.config.service[this.config.method];
            if (!serviceMethod) {
                console.error(`Method ${this.config.method} not found on service`);
                this.loadData(true);
                resolve();
                return;
            }

            // Call service method with or without body based on configuration
            const serviceCall = this.config.usePostMethod
                ? serviceMethod.call(this.config.service, params, body)
                : serviceMethod.call(this.config.service, params);

            serviceCall.subscribe({
                next: (response: any) => {
                    // Apply response transformation if provided
                    const transformedResponse = this.config.transformResponse
                        ? this.config.transformResponse(response)
                        : response;

                    const { hasError, data } = this.errorHandlerService.handleInternal(transformedResponse);
                    if (!hasError && data && data.items && data.items.length > 0) {
                        const item = data.items[0];
                        // Add the specific item to options if it's not already there
                        const existingOption = this.options.find(option => option.value === itemId);
                        if (!existingOption) {
                            const newOption = {
                                ...item,
                                label: this.config.labelFormatter ? this.config.labelFormatter(item) : (item as any).label || (item as any).name,
                                value: this.config.valueField ? (item as any)[this.config.valueField] : (item as any).value || (item as any).id
                            };

                            // Add to items and options
                            this.items.unshift(item);
                            this.options.unshift(newOption);
                        }
                    }
                    resolve();
                },
                error: (error: any) => {
                    console.error('Error loading specific item:', error);
                    // Fallback to loading first page
                    this.loadData(true);
                    resolve();
                }
            });
        });
    }

    onValueChange(value: any) {
        this.value = value;
        this.onChange(value);
        this.onTouched();
    }

    /**
     * Public method to reload data programmatically
     * Useful for manual refresh or when dependencies change outside of Angular change detection
     */
    reloadData() {
        this.loadData(true);
    }

    /**
     * Public method to clear data and selection
     * Useful when dependencies become invalid
     */
    clearData() {
        this.items = [];
        this.options = [];
        this.value = null;
        this.onChange(null);
    }

    // ControlValueAccessor implementation
    writeValue(value: any): void {
        // If a value is set and we don't have options loaded yet, or the value is not in current options
        if (value && (!this.options.length || !this.options.find(option => option.value === value))) {
            this.loadSpecificItem(value).then(() => {
                this.value = value;
                this.cd.markForCheck();
                const labelEl = this.genericSelect.nativeElement
                    .querySelector('.p-placeholder');
                if (labelEl) {
                    labelEl.classList.remove('p-placeholder');
                }
            });
        } else {
            this.value = value;
        }
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
    }

    // Public methods
    reset() {
        this.items = [];
        this.options = [];
        this.offset = 0;
        this.filterValue = '';
        this.value = null;
    }

    reload() {
        if (this.isStaticMode) {
            this.loadStaticOptions();
        } else {
            this.loadData(true);
        }
    }
}
