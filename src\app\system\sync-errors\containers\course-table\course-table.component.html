<p-table [value]="data" [scrollable]="true" scrollHeight="400px" responsiveLayout="scroll">
    <ng-template pTemplate="header">
        <tr>
            <th class="w-1/12" pSortableColumn="courseType">
                <div class="flex items-center gap-1">
                    courseType
                    <p-sortIcon field="courseType"/>
                </div>
            </th>
            <th class="w-2/12" pSortableColumn="courseName">
                <div class="flex items-center gap-1">
                    courseName
                    <p-sortIcon field="courseName"/>
                </div>
            </th>
            <th class="w-1/12" pSortableColumn="courseLevel">
                <div class="flex items-center gap-1">
                    courseLevel
                    <p-sortIcon field="courseLevel"/>
                </div>
            </th>
            <th style="min-width: 15rem" alignFrozen="right" pSortableColumn="error" pFrozenColumn [frozen]="true">
                error
                <p-sortIcon field="error"/>
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-childItem>
        <tr>
            <td>
                {{ childItem.courseType }}
            </td>
            <td>
                {{ childItem.courseName }}
            </td>
            <td>
                {{ childItem.courseLevel }}
            </td>
            <td style="min-width: 20rem" alignFrozen="right" pFrozenColumn [frozen]="true">
                {{ childItem.error }}
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="numberOfCols">Không có dữ liệu</td>
        </tr>
    </ng-template>
</p-table>