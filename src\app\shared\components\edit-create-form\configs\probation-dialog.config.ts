import { inject } from '@angular/core';
import { FormConfig } from '../edit-create-form.interfaces';
import { EmployeeApiService, EmployeePositionApiService } from '@shared/services';
import { EMPLOYEE_SELECT_CONFIG, EMPLOYEE_POSITION_BY_EMPLOYEE_SELECT_CONFIG } from '@shared/components/generic-select/generic-select.configs';

export const probationDialogFormConfig = (): FormConfig => {
  const employeeApiService = inject(EmployeeApiService);
  const employeePositionApiService = inject(EmployeePositionApiService);

  return {
    fields: [
      {
        key: 'employeeId', label: 'Nhân viên', required: true, width: 'full',
        type: 'select', config: { ...EMPLOYEE_SELECT_CONFIG, service: employeeApiService }
      },
      {
        key: 'employeePositionId', label: 'Vị trí', required: true, width: 'full',
        type: 'select', config: { ...EMPLOYEE_POSITION_BY_EMPLOYEE_SELECT_CONFIG, service: employeePositionApiService },
        dependsOn: 'employeeId'
      },
      {
        key: 'toDate', label: 'Ngày kết thúc thử việc', required: true, width: 'half',
        type: 'datepicker', placeholder: 'Chọn ngày kết thúc'
      },
      {
        key: 'deadline', label: 'Hạn đánh giá', required: true, width: 'half',
        type: 'datepicker', placeholder: 'Chọn hạn đánh giá'
      }
    ]
  };
};
