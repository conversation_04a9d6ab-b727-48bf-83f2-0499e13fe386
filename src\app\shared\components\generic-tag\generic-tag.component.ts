import { CommonModule } from '@angular/common';
import { Component, Input, ViewChild, ElementRef, AfterViewInit, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { TagModule, Tag } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';
import { EMPLOYEE_STATUS_CONFIG } from '@shared/enums/employee-status.enum';
import { EMPLOYEE_POSITION_STATUS_CONFIG } from '@shared/enums/employee-position-status.enum';
import { PROBATION_STATUS_CONFIG } from '@shared/enums/probation-status.enum';

export type TagType = 'employeeStatus' | 'employeePositionStatus' | 'probationStatus';

interface TagConfig {
    label: string;
    cssClass: string;
}

@Component({
    selector: 'generic-p-tag',
    standalone: true,
    imports: [
        CommonModule,
        TagModule,
        TooltipModule
    ],
    template: `
        <p-tag #tagElement
               [value]="tagLabel"
               [styleClass]="convertedStyleClass"
               [pTooltip]="shouldShowTooltip ? tagLabel : ''"
               tooltipPosition="top">
        </p-tag>
    `
})
export class GenericTagComponent implements OnChanges, AfterViewInit {
    @Input() value!: string;
    @Input() type!: TagType;
    @Input() styleClass?: string; // Additional CSS classes

    @ViewChild('tagElement', { static: false }) tagElement!: Tag;

    shouldShowTooltip = false;
    tagLabel: string = '';
    convertedStyleClass: string = '';

    private readonly statusConfigs = {
        employeeStatus: EMPLOYEE_STATUS_CONFIG,
        employeePositionStatus: EMPLOYEE_POSITION_STATUS_CONFIG,
        probationStatus: PROBATION_STATUS_CONFIG
    };

    ngOnChanges(changes: SimpleChanges): void {
        // Update tag display when inputs change
        if (changes['value'] || changes['type'] || changes['styleClass']) {
            this.updateTag();
        }
    }

    ngAfterViewInit(): void {
        this.updateTag();
    }

    private updateTag(): void {
        // Update display properties
        const config = this.getConfig();
        const label = config?.label || this.value;
        const baseCssClass = config?.cssClass || 'text-sm !bg-gray-100 !text-gray-700';
        const cssClass = this.styleClass ? `${baseCssClass} ${this.styleClass}` : baseCssClass;

        this.tagLabel = label;
        this.convertedStyleClass = cssClass;

        // Check overflow after DOM updates, but only if view is initialized
        if (this.tagElement) {
            setTimeout(() => {
                this.checkOverflow();
            });
        }
    }

    private checkOverflow(): void {
        if (this.tagElement?.el?.nativeElement) {
            const element = this.tagElement.el.nativeElement;
            const labelElement = element.querySelector('.p-tag-label') as HTMLElement;
            const style = getComputedStyle(element);

            if (labelElement) {
                // Store original styles
                const originalOverflow = labelElement.style.overflow;
                const originalTextOverflow = labelElement.style.textOverflow;
                const originalWhiteSpace = labelElement.style.whiteSpace;

                // Temporarily remove overflow restrictions to get true content width
                labelElement.style.overflow = 'visible';
                labelElement.style.textOverflow = 'unset';
                labelElement.style.whiteSpace = 'nowrap';

                // Get the actual content width and available width
                const contentWidth = labelElement.scrollWidth;
                const paddingLeft = parseFloat(style.paddingLeft);
                const paddingRight = parseFloat(style.paddingRight);
                const availableWidth = element.clientWidth - paddingLeft - paddingRight;

                // Restore original styles
                labelElement.style.overflow = originalOverflow;
                labelElement.style.textOverflow = originalTextOverflow;
                labelElement.style.whiteSpace = originalWhiteSpace;

                // Check if content overflows
                this.shouldShowTooltip = contentWidth > availableWidth;
            }
        } else {
            console.log('Tag element not found');
        }
    }

    private getConfig(): TagConfig | undefined {
        const statusConfig = this.statusConfigs[this.type];
        if (!statusConfig) {
            return undefined;
        }
        return (statusConfig as any)[this.value];
    }
}
