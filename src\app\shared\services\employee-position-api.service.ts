import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { RequestHelper } from '@shared/helpers/request.helper';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class EmployeePositionApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/employee-positions`;
    }

    getEmployeePositions(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(this.url, { params: options });
    }

    getEmployeePositionOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }

    getEmployeePositionById(id: string): Observable<any> {
        return this.httpClient.get<any>(`${this.url}/${id}`);
    }

    assignPositionToEmployee(employeePosition: any): Observable<any> {
        return this.httpClient.post<any>(this.url, employeePosition);
    }

    updateEmployeePosition(updateData: any): Observable<any> {
        return this.httpClient.put<any>(this.url, updateData);
    }

    deleteEmployeePosition(id: string): Observable<any> {
        return this.httpClient.delete<any>(`${this.url}/${id}`);
    }

    retireEmployeePosition(retireData: { id: string; toDate: Date }): Observable<any> {
        return this.httpClient.put<any>(`${this.url}/retire`, retireData);
    }
}
