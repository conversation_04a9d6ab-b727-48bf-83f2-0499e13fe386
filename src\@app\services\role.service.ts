import { EVENTS } from '@app/constants';
import { JwtPayloadDTO, RoleDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { ArrayHelper } from '@app/shared/helpers';
import { TeleBotService } from '@app/shared/services';
import { PermissionRepository, RoleRepository, UserRepository } from '@database/mongodb/repositories';
import { EntityExistedError, RequestInvalidError } from '@errors/index';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as _ from 'lodash';

@Injectable()
export class RoleService {
    private readonly logger = new Logger(RoleService.name);
    constructor(
        private roleRepository: RoleRepository,
        private permissionRepository: PermissionRepository,
        private teleBotService: TeleBotService,
        private eventEmitter: EventEmitter2,
    ) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.roleRepository.findWithPagination(request);
    }

    async createRole(dto: RoleDTO, logged: JwtPayloadDTO) {
        this.logger.debug('createRole', dto);
        const existRole = await this.roleRepository.findOne({ code: dto.code });
        if (existRole) {
            throw new EntityExistedError('Role code aldready exists');
        }
        const valid = await this.permissionRepository.checkExistPermissions(dto.permissions);
        if (!valid) {
            throw new RequestInvalidError('Certain permissions do not exist');
        }
        await this.roleRepository.create({
            code: dto.code,
            name: dto.name,
            permissions: _.uniq(dto.permissions),
            createdBy: logged.username,
            updatedBy: logged.username,
        });
    }

    async editRole(dto: RoleDTO, logged: JwtPayloadDTO) {
        this.logger.debug('editRole', dto);
        const existRole = await this.roleRepository.findOne({ code: dto.code });
        if (!existRole) {
            throw new RequestInvalidError('Certain role do not exist');
        }
        await this.roleRepository.updateOne(
            { code: dto.code },
            {
                name: dto.name,
                permissions: _.uniq(dto.permissions),
                updatedBy: logged.username,
            },
        );
        await this.eventEmitter.emitAsync(EVENTS.ROLE_PERMISSION_CHANGED, { roleId: existRole.id });
    }

    async deleteRole(code: string, logged: JwtPayloadDTO) {
        // handle after
    }
}
