import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class CenterLocationApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/center-locations`;
    }

    getAll(): Observable<any> {
        return this.httpClient.get<any>(`${this.url}`);
    }
}
