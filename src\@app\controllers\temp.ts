employee: {
    query: {
        id: $eq
    }
    textSearch: {
        code: $regex
        fullname: $regex
    }
}

id = id & (code = regex || fullname=regex)

_________________________
Position:
rootDepartment = departmentId && (code = regex || name = regex)
id = id && (code = regex || name = regex)

position: {
    andQ: {
        rootDepartmentId: $eq
        id: $eq
    }
    orQ: {
        code: $regex
        name: $regex
    }
}



empPos: {
    andQ: {
        employee.id: $eq
        id: $eq
    }
    orQ: { 
            name: $regex
        }

}

position: {
    andQ: {
        orQ: {
            id: $eq
            name: $regex
        }
    }
    rootDepartment.id: $eq
}