<!-- Toolbar -->
<p-toolbar *ngIf="config.features?.toolbar" styleClass="mb-4 !border-none">
    <ng-template #start>
        <h3 class="text-xl font-semibold">{{ config.title }}</h3>
    </ng-template>
    <ng-template #end>
        <!-- Custom toolbar actions -->
        <ng-container *ngIf="toolbarActionsTemplate">
            <ng-container *ngTemplateOutlet="toolbarActionsTemplate"></ng-container>
        </ng-container>

        <!-- Toolbar actions from config -->
        <ng-container *ngFor="let action of getToolbarActions()">
            <p-button pRipple
                      styleClass="h-10"
                      [icon]="action.icon || (action.useDefaultStyle === 'add' ? 'pi pi-plus' : '')"
                      [label]="action.label || (action.useDefaultStyle === 'add' ? 'Thêm mới' : '')"
                      [severity]="action.severity"
                      [class]="action.styleClass"
                      [disabled]="action.disabled ? action.disabled(undefined) : false"
                      [pTooltip]="action.tooltip"
                      (onClick)="executeAction(action, undefined, $event)" />
        </ng-container>


    </ng-template>
</p-toolbar>

<!-- Table Container -->
<div class="flex w-full" style="max-height: calc(100vh - 20rem)">
    <div class="flex flex-col w-full">
        <!-- Virtual Scroll Table -->
        <p-table *ngIf="config.features?.virtualScroll; else paginationTable"
                 #dataTable
                 defaultLazyScrollPTable
                 [value]="data"
                 [totalRecords]="totalRecords"
                 [rows]="itemPerPage"
                 [loading]="loading"
                 [virtualScrollItemSize]="virtualScrollItemSize"
                 [virtualScrollOptions]="virtualScrollOptions"
                 [selectionMode]="selectionMode"
                 [(selection)]="selection"
                 [metaKeySelection]="metaKeySelection"
                 [dataKey]="dataKey"
                 (onRowSelect)="onTableRowSelect($event)"
                 (onRowUnselect)="onTableRowUnselect($event)"
                 (onLazyLoad)="onTableLazyLoad($event)">
            
            <!-- Header Template -->
            <ng-template pTemplate="header">
                <tr>
                    <th *ngFor="let col of config.columns"
                        [class]="col.width"
                        [pSortableColumn]="col.sortable ? col.field : undefined">
                        {{ col.header }}
                        <p-sortIcon *ngIf="col.sortable" [field]="col.field" />
                    </th>
                    <th *ngIf="hasActions" class="min-w-[200px]">Hành động</th>
                </tr>
            </ng-template>
            
            <!-- Body Template -->
            <ng-template pTemplate="body" let-item>
                <tr style="height:46px" [pSelectableRow]="hasSelection ? item : null">
                    <td *ngFor="let col of config.columns" [class]="col.width">
                        <!-- Text type -->
                        <span *ngIf="col.type === 'text' || !col.type">
                            {{ getFieldValue(item, col.field) }}
                        </span>
                        
                        <!-- Date type -->
                        <span *ngIf="col.type === 'date'">
                            {{ getFieldValue(item, col.field) | date:(col.dateFormat || 'dd/MM/yyyy') }}
                        </span>
                        
                        <!-- Tag type -->
                        <generic-p-tag *ngIf="col.type === 'tag' && col.tagType"
                                       [value]="getFieldValue(item, col.field)"
                                       [type]="col.tagType">
                        </generic-p-tag>

                        <!-- Fallback for tag type without tagType specified -->
                        <span *ngIf="col.type === 'tag' && !col.tagType">
                            {{ getFieldValue(item, col.field) }}
                        </span>
                        
                        <!-- Custom type - template reference -->
                        <ng-container *ngIf="col.type === 'custom'">
                            <ng-container *ngIf="customColumnTemplate">
                                <ng-container *ngTemplateOutlet="customColumnTemplate; context: { $implicit: item, field: col.field, column: col }">
                                </ng-container>
                            </ng-container>
                            <!-- Fallback for custom types without template -->
                            <span *ngIf="!customColumnTemplate">
                                {{ getFieldValue(item, col.field) }}
                            </span>
                        </ng-container>
                    </td>
                    
                    <!-- Actions Column -->
                    <td *ngIf="hasActions" class="min-w-[200px]">
                        <div class="flex gap-2">
                            <!-- Custom Actions via Content Projection -->
                            <ng-container *ngIf="customActionsTemplate">
                                <ng-container *ngTemplateOutlet="customActionsTemplate; context: { $implicit: item }">
                                </ng-container>
                            </ng-container>

                            <!-- Row Actions from Config -->
                            <button *ngFor="let action of getRowActions()"
                                    pButton
                                    [icon]="action.icon || (action.useDefaultStyle === 'edit' ? 'pi pi-pencil' : action.useDefaultStyle === 'delete' ? 'pi pi-trash' : '')"
                                    [class]="'p-button-text p-button-sm ' + (action.styleClass || (action.useDefaultStyle === 'delete' ? 'p-button-danger ' : '')) + (action.styleClass || '')"
                                    [severity]="action.severity"
                                    [pTooltip]="action.tooltip"
                                    [disabled]="action.disabled ? action.disabled(item) : false"
                                    tooltipPosition="top"
                                    (click)="executeAction(action, item, $event)">
                            </button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            
            <!-- Empty Message -->
            <ng-template pTemplate="emptymessage">
                <tr style="height:46px">
                    <td [attr.colspan]="config.columns.length + (hasActions ? 1 : 0)"
                        class="text-center p-4">
                        Chưa có {{ config.entityName }} nào cả.
                    </td>
                </tr>
            </ng-template>
        </p-table>

        <!-- Pagination Table -->
        <ng-template #paginationTable>
            <p-table #dataTable
                     [value]="data"
                     [lazy]="true"
                     [paginator]="true"
                     [rows]="itemPerPage"
                     [totalRecords]="totalRecords"
                     [rowsPerPageOptions]="itemPerPageOptions"
                     [showCurrentPageReport]="true"
                     [loading]="loading"
                     [currentPageReportTemplate]="'{first} - {last} of {totalRecords} ' + config.entityName"
                     [selectionMode]="selectionMode"
                     [(selection)]="selection"
                     [metaKeySelection]="metaKeySelection"
                     [dataKey]="dataKey"
                     (onRowSelect)="onTableRowSelect($event)"
                     (onRowUnselect)="onTableRowUnselect($event)"
                     (onLazyLoad)="onTableLazyLoad($event)">
                
                <!-- Header Template -->
                <ng-template pTemplate="header">
                    <tr>
                        <th *ngFor="let col of config.columns"
                            [class]="col.width"
                            [pSortableColumn]="col.sortable ? col.field : undefined">
                            {{ col.header }}
                            <p-sortIcon *ngIf="col.sortable" [field]="col.field" />
                        </th>
                        <th *ngIf="hasActions">Hành động</th>
                    </tr>
                </ng-template>
                
                <!-- Body Template -->
                <ng-template pTemplate="body" let-item>
                    <tr [pSelectableRow]="hasSelection ? item : null">
                        <td *ngFor="let col of config.columns" [class]="col.width">
                            <!-- Text type -->
                            <span *ngIf="col.type === 'text' || !col.type">
                                {{ getFieldValue(item, col.field) }}
                            </span>
                            
                            <!-- Date type -->
                            <span *ngIf="col.type === 'date'">
                                {{ getFieldValue(item, col.field) | date:(col.dateFormat || 'dd/MM/yyyy') }}
                            </span>
                            
                            <!-- Tag type -->
                            <generic-p-tag *ngIf="col.type === 'tag' && col.tagType"
                                           [value]="getFieldValue(item, col.field)"
                                           [type]="col.tagType">
                            </generic-p-tag>

                            <!-- Fallback for tag type without tagType specified -->
                            <span *ngIf="col.type === 'tag' && !col.tagType">
                                {{ getFieldValue(item, col.field) }}
                            </span>
                            
                            <!-- Custom type - template reference -->
                            <ng-container *ngIf="col.type === 'custom'">
                                <ng-container *ngIf="customColumnTemplate">
                                    <ng-container *ngTemplateOutlet="customColumnTemplate; context: { $implicit: item, field: col.field, column: col }">
                                    </ng-container>
                                </ng-container>
                                <!-- Fallback for custom types without template -->
                                <span *ngIf="!customColumnTemplate">
                                    {{ getFieldValue(item, col.field) }}
                                </span>
                            </ng-container>
                        </td>
                        
                        <!-- Actions Column -->
                        <td *ngIf="hasActions">
                            <div class="flex gap-2">
                                <!-- Custom Actions via Content Projection -->
                                <ng-container *ngIf="customActionsTemplate">
                                    <ng-container *ngTemplateOutlet="customActionsTemplate; context: { $implicit: item }">
                                    </ng-container>
                                </ng-container>

                                <!-- Row Actions from Config -->
                                <button *ngFor="let action of getRowActions()"
                                        pButton
                                        [icon]="action.icon || (action.useDefaultStyle === 'edit' ? 'pi pi-pencil' : action.useDefaultStyle === 'delete' ? 'pi pi-trash' : '')"
                                        [class]="'p-button-text p-button-sm ' + (action.styleClass || (action.useDefaultStyle === 'delete' ? 'p-button-danger ' : '')) + (action.styleClass || '')"
                                        [severity]="action.severity"
                                        [pTooltip]="action.tooltip"
                                        [disabled]="action.disabled ? action.disabled(item) : false"
                                        tooltipPosition="top"
                                        (click)="executeAction(action, item, $event)">
                                </button>
                            </div>
                        </td>
                    </tr>
                </ng-template>
                
                <!-- Empty Message -->
                <ng-template pTemplate="emptymessage">
                    <tr>
                        <td [attr.colspan]="config.columns.length + (hasActions ? 1 : 0)"
                            class="text-center p-4">
                            Chưa có {{ config.entityName }} nào cả.
                        </td>
                    </tr>
                </ng-template>
            </p-table>
        </ng-template>
    </div>
</div>

<!-- Confirmation Dialog -->
<p-confirmDialog [style]="{width: '450px'}" dismissableMask="true" />

<!-- Content Projection for Dialogs and Additional Components -->
<ng-content></ng-content>
