<p-popover [style]="{width: popoverWidth}"
           [dismissable]="popoverConfig.dismissable !== false"
           [styleClass]="popoverConfig.styleClass"
           (onHide)="onPopoverHide()"
           #popover>
    <ng-template pTemplate="content">
        <div class="p-4">
            <!-- Title -->
            <h4 class="mb-3 text-lg font-semibold" *ngIf="popoverTitle">{{ popoverTitle }}</h4>

            <!-- Edit Create Form -->
            <edit-create-form
              [editMode]="false"
              [initialData]="transformedInitialData"
              [formConfig]="formConfig"
              (formChange)="onFormChange($event)">

              <!-- Custom Field Template Slot -->
              <ng-container *ngIf="customFieldTemplate && form">
                <ng-container *ngTemplateOutlet="customFieldTemplate; context: { $implicit: form }">
                </ng-container>
              </ng-container>
            </edit-create-form>

            <!-- Actions -->
            <div class="flex justify-end gap-2" *ngIf="popoverConfig.actions.length > 0">
                <ng-container *ngFor="let action of popoverConfig.actions">
                    <p-button *ngIf="action.useDefaultStyle === 'cancel'"
                              defaultCancelButton
                              (onClick)="executeAction(action)"
                              [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false) : false" />
                    <p-button *ngIf="action.useDefaultStyle === 'confirm'"
                              defaultConfirmButton
                              [label]="action.label || 'Xác nhận'"
                              [severity]="action.severity || 'primary'"
                              (onClick)="executeAction(action)"
                              [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false) : false" />
                    <p-button *ngIf="!action.useDefaultStyle"
                              [label]="action.label"
                              [icon]="action.icon"
                              [severity]="action.severity"
                              [type]="action.type || 'button'"
                              (onClick)="executeAction(action)"
                              [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false) : false" />
                </ng-container>
            </div>
        </div>
    </ng-template>
</p-popover>
