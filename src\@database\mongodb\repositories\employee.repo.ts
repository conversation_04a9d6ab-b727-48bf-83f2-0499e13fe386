import { EMPLOYEE_POSITION_STATUS_PRIORITY, EmployeePositionStatusEnum, EmployeeStatusEnum } from '@app/enums';
import { SearchEmployeeDTO, SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DateH<PERSON>per, StringHelper } from '@app/shared/helpers';
import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { EmployeeDocument } from '../schemas';

@Injectable()
export class EmployeeRepository extends GenericRepository<EmployeeDocument> {
    private readonly logger = new Logger(EmployeeRepository.name);

    constructor(
        @Inject(MONGO_CONST.EMPLOYEE_COLLECTION)
        private readonly employeeModel: Model<EmployeeDocument>,
    ) {
        super(employeeModel);
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const code = orQ?.code || null;
        const fullname = orQ?.fullname || null;
        const nickname = orQ?.nickname || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        }
        if (!StringHelper.isEmpty(code)) {
            matchQ['$or'] = [{ code: { $regex: code, $options: 'i' } }];
        }
        if (!StringHelper.isEmpty(fullname)) {
            if (ArrayHelper.isEmpty(matchQ['$or'])) {
                matchQ['$or'] = [{ 'profile.fullname': { $regex: fullname, $options: 'i' } }];
            } else {
                matchQ['$or'].push({ 'profile.fullname': { $regex: fullname, $options: 'i' } });
            }
        }
        if (!StringHelper.isEmpty(nickname)) {
            if (ArrayHelper.isEmpty(matchQ['$or'])) {
                matchQ['$or'] = [{ 'profile.nickname': { $regex: nickname, $options: 'i' } }];
            } else {
                matchQ['$or'].push({ 'profile.nickname': { $regex: nickname, $options: 'i' } });
            }
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.employeeModel.aggregate([
            { $lookup: { from: MONGO_CONST.PROFILE_COLLECTION, localField: 'id', foreignField: 'id', as: 'profile' } },
            { $unwind: '$profile' },
            {
                $match: matchQ,
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    fullname: '$profile.fullname',
                    nickname: '$profile.nickname',
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async findWithPaginationWithProfile(request: PaginationRequest): Promise<PaginationResponse<any>> {
        const { query, offset, limit, sort } = request;
        const total = await this.employeeModel.countDocuments(query).exec();
        let aggregateQuery = this.employeeModel.aggregate([
            {
                $match: query,
            },
            {
                $lookup: {
                    from: MONGO_CONST.PROFILE_COLLECTION,
                    localField: 'id',
                    foreignField: 'id',
                    as: 'profile',
                },
            },
            {
                $unwind: '$profile',
            },
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
                    let: {
                        letEmployeeId: '$id',
                        letReferenceDate: {
                            $cond: {
                                if: { $eq: ['$toDate', null] },
                                then: DateHelper.getStartOfDate(),
                                else: '$toDate',
                            },
                        },
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [
                                        { $eq: ['$employeeId', '$$letEmployeeId'] },
                                        { $eq: ['$deletedAt', null] },
                                        { $lte: ['$fromDate', '$$letReferenceDate'] },
                                        {
                                            $or: [{ $eq: ['$toDate', null] }, { $gte: ['$toDate', '$$letReferenceDate'] }],
                                        },
                                        // { $nin: ['$status', [EmployeePositionStatusEnum.INACTIVE]] },
                                    ],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                id: 1,
                                positionId: 1,
                                departmentId: 1,
                                status: 1,
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.POSITION_COLLECTION,
                                let: { letPositionId: '$positionId' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: ['$id', '$$letPositionId'],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            _id: 0,
                                            code: 1,
                                            name: 1,
                                        },
                                    },
                                ],
                                as: 'position',
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.DEPARTMENT_COLLECTION,
                                let: { letDepartmentId: '$departmentId' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: ['$id', '$$letDepartmentId'],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            _id: 0,
                                            code: 1,
                                            name: 1,
                                        },
                                    },
                                ],
                                as: 'department',
                            },
                        },
                    ],
                    as: 'employeePositions',
                },
            },
        ]);

        if (sort && Object.keys(sort).length > 0) {
            aggregateQuery = aggregateQuery.sort(sort);
        }
        const items = await aggregateQuery.skip(offset).limit(limit).exec();
        return new PaginationResponse({ total, items, offset, limit, sort });
    }

    async searchEmployees(request: PaginationRequest, body: SearchEmployeeDTO) {
        const { offset, limit, sort } = request;
        const pipelines = [];
        let employeeQuery = {
            deletedAt: null,
        };

        if (!StringHelper.isEmpty(body.code)) {
            employeeQuery['code'] = body.code;
        }
        if (body.fromDateF && body.fromDateT) {
            employeeQuery['fromDate'] = {
                $gte: body.fromDateF,
                $lte: body.fromDateT,
            };
        } else if (body.fromDateF) {
            employeeQuery['fromDate'] = {
                $gte: body.fromDateF,
            };
        } else if (body.fromDateT) {
            employeeQuery['fromDate'] = {
                $lte: body.fromDateT,
            };
        }
        if (body.toDateF && body.toDateT) {
            employeeQuery['toDate'] = {
                $gte: body.toDateF,
                $lte: body.toDateT,
            };
        } else if (body.toDateF) {
            employeeQuery['toDate'] = {
                $gte: body.toDateF,
            };
        } else if (body.toDateT) {
            employeeQuery['toDate'] = {
                $lte: body.toDateT,
            };
        }
        if (!ArrayHelper.isEmpty(body.statuses)) {
            employeeQuery['status'] = { $in: body.statuses };
        }
        if (Object.keys(employeeQuery).length > 0) {
            pipelines.push({ $match: employeeQuery });
        }

        // profile
        let profileQuery = {};
        if (!StringHelper.isEmpty(body.fullname)) {
            profileQuery['profile.fullname'] = {
                $regex: body.fullname,
                $options: 'i',
            };
        }
        if (!StringHelper.isEmpty(body.nickname)) {
            profileQuery['profile.nickname'] = {
                $regex: body.nickname,
                $options: 'i',
            };
        }
        if (!StringHelper.isEmpty(body.email)) {
            profileQuery['profile.email'] = body.email;
        }
        if (!StringHelper.isEmpty(body.phone)) {
            profileQuery['profile.phone'] = body.phone;
        }
        if (Object.keys(profileQuery).length > 0) {
            pipelines.push({ $match: profileQuery });
        }
        pipelines.push(
            ...[
                {
                    $lookup: {
                        from: MONGO_CONST.PROFILE_COLLECTION,
                        let: { refId: '$id' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [{ $eq: ['$id', '$$refId'] }],
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    fullname: 1,
                                    nickname: 1,
                                    birthday: 1,
                                    phone: 1,
                                    email: 1,
                                },
                            },
                        ],
                        as: 'profile',
                    },
                },
                {
                    $unwind: '$profile',
                },
            ],
        );

        // EmpPos
        let employeePositionQuery: any[] = [{ $eq: ['$deletedAt', null] }];
        let strictPosition = false;
        if (!ArrayHelper.isEmpty(body.positionIds)) {
            employeePositionQuery.push({
                $in: ['$positionId', body.positionIds],
            });
            strictPosition = true;
        }
        if (!ArrayHelper.isEmpty(body.departmentIds)) {
            employeePositionQuery.push({
                $in: ['$departmentId', body.departmentIds],
            });
            strictPosition = true;
        }
        pipelines.push({
            $lookup: {
                from: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
                let: { letEmployeeId: '$id' },
                pipeline: [
                    {
                        $match: {
                            $expr: {
                                $and: [{ $eq: ['$employeeId', '$$letEmployeeId'] }, ...employeePositionQuery],
                            },
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            positionId: 1,
                            departmentId: 1,
                            status: 1,
                        },
                    },
                    {
                        $lookup: {
                            from: MONGO_CONST.POSITION_COLLECTION,
                            let: { letPositionId: '$positionId' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $eq: ['$id', '$$letPositionId'],
                                        },
                                    },
                                },
                                {
                                    $project: {
                                        _id: 0,
                                        name: 1,
                                    },
                                },
                            ],
                            as: 'position',
                        },
                    },
                    {
                        $unwind: {
                            path: '$position',
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $lookup: {
                            from: MONGO_CONST.DEPARTMENT_COLLECTION,
                            let: { letDepartmentId: '$departmentId' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: {
                                            $eq: ['$id', '$$letDepartmentId'],
                                        },
                                    },
                                },
                                {
                                    $project: {
                                        _id: 0,
                                        name: 1,
                                    },
                                },
                            ],
                            as: 'department',
                        },
                    },
                    {
                        $unwind: {
                            path: '$department',
                            preserveNullAndEmptyArrays: false,
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            positionName: '$position.name',
                            departmentName: '$department.name',
                            status: 1,
                        },
                    },
                ],
                as: 'employeePositions',
            },
        });

        if (strictPosition) {
            pipelines.push({
                $match: {
                    employeePositions: { $ne: [] },
                },
            });
        }

        let result = await this.employeeModel.aggregate([
            ...pipelines,
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async onChangeStatus(id: string) {
        const employeeWithPositions = await this.employeeModel.aggregate([
            {
                $match: { id, deletedAt: null },
            },
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
                    localField: 'id',
                    foreignField: 'employeeId',
                    as: 'employeePositions',
                },
            },
        ]);

        if (ArrayHelper.isEmpty(employeeWithPositions)) {
            return;
        }
        const employeePositions = employeeWithPositions[0].employeePositions || [];
        let fromDates = [];
        let toDates = [];
        employeePositions.forEach(e => {
            fromDates.push(e.fromDate);
            toDates.push(e.toDate);
        });
        let minDate = null;
        if (!ArrayHelper.isEmpty(fromDates)) {
            minDate = fromDates.reduce((min, curr) => {
                if (min == null || curr < min) return curr;
                return min;
            });
        }
        let maxDate = null;
        if (!ArrayHelper.isEmpty(toDates) && !toDates.includes(null)) {
            maxDate = toDates.reduce((max, curr) => {
                if (max == null || curr > max) return curr;
                return max;
            });
        }
        await this.employeeModel.updateOne({ id }, { status: this.mappingStatus(employeePositions), fromDate: minDate, toDate: maxDate }).exec();
    }

    async findOneWithDetails(id: string): Promise<any> {
        const matchCondition = {
            id,
            deletedAt: null,
        };

        const result = await this.employeeModel.aggregate([
            {
                $match: matchCondition,
            },
            {
                $lookup: {
                    from: MONGO_CONST.PROFILE_COLLECTION,
                    localField: 'id',
                    foreignField: 'id',
                    as: 'profile',
                },
            },
            {
                $unwind: {
                    path: '$profile',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
                    let: { letEmployeeId: '$id' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $and: [{ $eq: ['$employeeId', '$$letEmployeeId'] }, { $eq: ['$deletedAt', null] }],
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.POSITION_COLLECTION,
                                let: { letPositionId: '$positionId' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: ['$id', '$$letPositionId'],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            _id: 0,
                                            id: 1,
                                            code: 1,
                                            name: 1,
                                            departmentId: 1,
                                            roleId: 1,
                                        },
                                    },
                                ],
                                as: 'position',
                            },
                        },
                        {
                            $unwind: {
                                path: '$position',
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.DEPARTMENT_COLLECTION,
                                let: { letDepartmentId: '$departmentId' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $eq: ['$id', '$$letDepartmentId'],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            _id: 0,
                                            id: 1,
                                            code: 1,
                                            name: 1,
                                        },
                                    },
                                ],
                                as: 'department',
                            },
                        },
                        {
                            $unwind: {
                                path: '$department',
                                preserveNullAndEmptyArrays: true,
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.PROBATION_COLLECTION,
                                let: { letEmployeePositionId: '$id' },
                                pipeline: [
                                    {
                                        $match: {
                                            $expr: {
                                                $and: [{ $eq: ['$employeePositionId', '$$letEmployeePositionId'] }, { $eq: ['$deletedAt', null] }],
                                            },
                                        },
                                    },
                                    {
                                        $project: {
                                            _id: 0,
                                            id: 1,
                                            status: 1,
                                            fromDate: 1,
                                            toDate: 1,
                                            deadline: 1,
                                            isManual: 1,
                                            createdAt: 1,
                                            updatedAt: 1,
                                        },
                                    },
                                ],
                                as: 'probations',
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                id: 1,
                                employeeId: 1,
                                positionId: 1,
                                departmentId: 1,
                                fromDate: 1,
                                toDate: 1,
                                status: 1,
                                position: 1,
                                department: 1,
                                probations: 1,
                                createdAt: 1,
                                updatedAt: 1,
                            },
                        },
                    ],
                    as: 'employeePositions',
                },
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    status: 1,
                    note: 1,
                    fromDate: 1,
                    toDate: 1,
                    profile: 1,
                    employeePositions: 1,
                    createdAt: 1,
                    updatedAt: 1,
                },
            },
        ]);

        return ArrayHelper.isEmpty(result) ? null : result[0];
    }

    mappingStatus(employeePositions: any): EmployeeStatusEnum {
        if (ArrayHelper.isEmpty(employeePositions)) {
            return EmployeeStatusEnum.NO_POSITION;
        }
        if (employeePositions.length == 1) {
            return EmployeeStatusEnum[employeePositions[0].status];
        }
        const mapStatus = new Map<number, Set<EmployeePositionStatusEnum>>();

        let priority;
        let setStatus;
        employeePositions.forEach(e => {
            priority = EMPLOYEE_POSITION_STATUS_PRIORITY[e.status];
            if (mapStatus.has(priority)) {
                setStatus = mapStatus.get(priority);
            } else {
                setStatus = new Set<EmployeePositionStatusEnum>();
            }
            setStatus.add(e.status);
            mapStatus.set(priority, setStatus);
        });
        const minIndex = Math.min(...mapStatus.keys());
        setStatus = mapStatus.get(minIndex);
        if (![2, 3].includes(minIndex)) {
            return setStatus.values().next().value;
        }
        if (minIndex == 2) {
            if (setStatus.size == 1) {
                return setStatus.values().next().value;
            }
            return EmployeeStatusEnum.PROBATION;
        }
        if (minIndex == 3) {
            if (setStatus.size == 1) {
                return setStatus.values().next().value;
            }
            if (setStatus.size == 2 && setStatus.has(EmployeePositionStatusEnum.TO_ONBOARD) && setStatus.has(EmployeePositionStatusEnum.TO_INACTIVE)) {
                return EmployeeStatusEnum.TO_TRANSFER;
            }
        }
        return EmployeeStatusEnum.MIXED;
    }

    async findIdsByFullname(fullname: string) {
        const result = await this.employeeModel.aggregate([
            { $lookup: { from: MONGO_CONST.PROFILE_COLLECTION, localField: 'id', foreignField: 'id', as: 'profile' } },
            { $unwind: '$profile' },
            { $match: { 'profile.fullname': { $regex: fullname, $options: 'i' } } },
            { $group: { _id: 1, ids: { $push: '$id' } } },
        ]);
        return result.length > 0 ? result[0].ids : [];
    }
}
