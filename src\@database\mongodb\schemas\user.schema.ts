import { UserType } from '@app/enums';
import { SchemaConfig } from '@database/mongodb';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

export interface IUserDocument {
    id: string;
    username: string;
    permissions?: string[];
    permissionsKey?: string;
    password?: string;
    salt?: string;
    default?: boolean;
    resetCode?: string;
    isAdmin?: boolean;
    type: UserType;
}

@Schema({ versionKey: false, timestamps: true, toJSON: SchemaConfig.ToJSON, toObject: SchemaConfig.ToObject })
export class UserDocument extends Audit implements IUserDocument {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    username: string;
    @Prop({ index: true, default: [] })
    permissions: string[];
    @Prop({ index: true, default: null })
    permissionsKey: string;
    @Prop({ required: true })
    password: string;
    @Prop({ index: true, required: true })
    salt: string;
    @Prop({ index: true, default: false })
    default: boolean;
    @Prop({ index: true, default: null })
    resetCode: string;
    @Prop({ index: true, default: false })
    isAdmin: boolean;
    @Prop({ index: true, required: true })
    type: UserType;
}

export const UserSchema = SchemaFactory.createForClass(UserDocument);
