import { AbstractControl, ValidationErrors } from '@angular/forms';
import { StringHelper } from '@shared/helpers';

export class AppValidators {
  static vnPhone(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    let value = control.value.replace(/\s|\.|-/g, ''); // remove spaces, dots, dashes

    if (value.startsWith('+84')) {
      value = '0' + value.substring(3);
    }

    const mobileRegex = /^0(3[2-9]|5[6|8|9]|7[06-9]|8[1-5]|9[0-9])[0-9]{7}$/;
    const fixedRegex = /^0(2[0-9]{1,2})[0-9]{7}$/;

    if (mobileRegex.test(value) || fixedRegex.test(value)) {
      return null;
    }

    return { invalidPhone: true };
  }

  static name(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    let value = control.value;
    if (/^ | $|  /.test(value)) {
      return { invalidName: true };
    } else if (/[^\p{L}\- ]/ug.test(value)) {
      return { invalidName: true };
    } else if (value != StringHelper.capitalizeLetter(value)) {
      return { invalidName: true };
    }
    return null;
  }

  static ssn(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;
    let value = control.value;
    if (!/^\d{9}$/.test(value) && !/^\d{12}$/.test(value)) {
      return { invalidSSN: true };
    }
    return null;
  }
}

// Keep the original function exports for backward compatibility
export function vnPhoneValidator(control: AbstractControl): ValidationErrors | null {
  return AppValidators.vnPhone(control);
}

export function NameValidator(control: AbstractControl): ValidationErrors | null {
  return AppValidators.name(control);
}

export function SSNValidator(control: AbstractControl): ValidationErrors | null {
  return AppValidators.ssn(control);
}