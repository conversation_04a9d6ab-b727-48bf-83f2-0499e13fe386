import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';
import { PERMISSIONS } from '@shared/constants';
import { EVENT_TYPE_MAP, LearnerEventType, SettingType } from '@shared/enums';
import { DateHelper, StringHelper } from '@shared/helpers';
import { AuthService, ErrorHandlerService, SettingApiService } from '@shared/services';
import { TestResultTable } from '../test-result-table/test-result-table';

@Component({
    selector: 'learner-result',
    imports: [CommonModule, TestResultTable, GenericTagComponent],
    templateUrl: './learner-result.html',
    styleUrls: ['./learner-result.scss']
})
export class LearnerResult {
    dateFormat = DateHelper.DATE_FORMAT_HTML;
    DEFAULT_BG_GRAY_COLOR = '#dddddd';
    DEFAULT_BG_WHITE_COLOR = '#ffffff';
    DEFAULT_TEXT_COLOR = '#333333';

    settings: any[] = [];
    @Input() listData: any[] = [];

    constructor(
        private settingApiService: SettingApiService,
        private errorHandlerService: ErrorHandlerService,
        private authService: AuthService
    ) {}

    ngOnInit() {
        this.getlevelSetting();
    }

    getlevelSetting() {
        this.settings = [];
        this.settingApiService.getAll([SettingType.COURSE_LEVEL, SettingType.TEST_RESULT]).subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.settings = data;
            }
        });
    }

    isEventTest(eventType: any) {
        return [LearnerEventType.TEST_MID, LearnerEventType.TEST_FINAL].includes(eventType);
    }

    mappingEventLabel(eventType: string) {
        return EVENT_TYPE_MAP[eventType] || 'N/A';
    }

    outcomeSetting(outcome: string) {
        const index = this.settings.findIndex((i) => i.code == outcome && i.type == SettingType.TEST_RESULT);
        if (index == -1) {
            return {
                name: StringHelper.EMPTY,
                bgColor: this.DEFAULT_BG_WHITE_COLOR,
                textColor: this.DEFAULT_TEXT_COLOR
            };
        }
        return this.settings[index];
    }

    colorLevelTag(value: string) {
        const index = this.settings.findIndex((i) => i.code == value && i.type == SettingType.COURSE_LEVEL);
        if (index == -1) {
            return {
                bgColor: this.DEFAULT_BG_GRAY_COLOR,
                textColor: this.DEFAULT_TEXT_COLOR,
                bgChildColor: this.DEFAULT_BG_WHITE_COLOR
            };
        }
        return this.settings[index];
    }

    colorTestResultTag(value: string) {
        const index = this.settings.findIndex((i) => i.code == value && i.type == SettingType.COURSE_LEVEL);
        if (index == -1) {
            return {
                bgColor: this.DEFAULT_BG_WHITE_COLOR,
                textColor: this.DEFAULT_TEXT_COLOR
            };
        }
        return this.settings[index];
    }

    private getDateRange(item: any): { startDate: string; endDate: string } {
        if (!item?.details || item.details.length === 0) {
            return { startDate: '', endDate: '' };
        }

        const eventDates = item.details
            .map((detail: any) => detail.eventDate)
            .filter((date: any) => date);

        if (eventDates.length === 0) {
            return { startDate: '', endDate: '' };
        }

        const minDate = Math.min(...eventDates.map((date: string) => new Date(date).getTime()));
        const maxDate = Math.max(...eventDates.map((date: string) => new Date(date).getTime()));

        const startDate = new Date(minDate).toISOString();
        const endDate = minDate !== maxDate ? new Date(maxDate).toISOString() : '';

        return { startDate, endDate };
    }

    getStartDate(item: any): string {
        return this.getDateRange(item).startDate;
    }

    getEndDate(item: any): string {
        return this.getDateRange(item).endDate;
    }

    hasTestViewPermission(): boolean {
        return this.authService.hasPermission(PERMISSIONS.COURSE_EDIT.code);
    }
}
