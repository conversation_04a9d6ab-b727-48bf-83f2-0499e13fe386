import { Injectable } from '@angular/core';
import { StepperConfig, StepperConfigRegistry, StepperEntityType } from '@shared/components/generic-stepper';

// Module-level variables (shared across all imports of this module)
const stepperConfigRegistry: StepperConfigRegistry = {
  'employee-creation': () => import('../components/generic-stepper/configs/employee-creation.config').then(m => m.employeeCreationStepperConfig),
  // Add more configs here as needed - each will be lazy loaded
};

// Module-level cache for configs (shared across all imports of this module)
const stepperConfigCache = new Map<string, StepperConfig>();

/**
 * Standalone function to get stepper configuration
 * @param entityType The type of entity for the stepper
 * @param onSuccess Callback for successful completion
 * @param onCancel Callback for cancellation
 * @returns Promise that resolves to the stepper configuration
 */
export const getStepperConfig = async (
  entityType: StepperEntityType,
  onSuccess: () => void,
  onCancel: () => void
): Promise<StepperConfig> => {
  // Create cache key based on entity type and callback references
  const cacheKey = `${entityType}_${onSuccess.toString()}_${onCancel.toString()}`;

  // Check cache first
  if (stepperConfigCache.has(cacheKey)) {
    return stepperConfigCache.get(cacheKey)!;
  }

  const configFactory = stepperConfigRegistry[entityType];
  if (!configFactory) {
    throw new Error(`Unsupported entity type: ${entityType}. Available types: ${Object.keys(stepperConfigRegistry).join(', ')}`);
  }

  // Dynamically import and create the config
  const configFunction = await configFactory();
  const config = configFunction(onSuccess, onCancel);

  // Cache in module-level variable
  stepperConfigCache.set(cacheKey, config);
  return config;
};

/**
 * Standalone function to clear stepper config cache
 * Useful for testing or when configs need to be refreshed
 */
export const clearStepperConfigCache = (): void => {
  stepperConfigCache.clear();
};

/**
 * Optional service class for backward compatibility or DI scenarios
 * Delegates to standalone functions
 */
@Injectable({
  providedIn: 'root'
})
export class StepperConfigService {
  /**
   * Get stepper configuration for the specified entity type
   * @param entityType The type of entity for the stepper
   * @param onSuccess Callback for successful completion
   * @param onCancel Callback for cancellation
   * @returns Promise that resolves to the stepper configuration
   */
  async getConfig(
    entityType: StepperEntityType,
    onSuccess: () => void,
    onCancel: () => void
  ): Promise<StepperConfig> {
    return getStepperConfig(entityType, onSuccess, onCancel);
  }

  /**
   * Clear the configuration cache
   */
  clearCache(): void {
    clearStepperConfigCache();
  }
}
