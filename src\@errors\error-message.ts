export const errorCode = {
    UNKNOW: '000001',
    DATA_PROCESS: '000002',
    REQUEST_INVALID: '000003',
    ENTITY_EXISTED: '000004',
    UNAUTHORIZED: '000005',
    FORBIDDEN: '000006',
    NOT_FOUND: '000007',
    MISSING_MIDDLEWARE: '000008',
    TOKEN_EXPIRED: '000009',
    GOOGLE_SHEET_ERR: '000010',
    ACCOUNT_NOT_HAVE_EMAIL: '000011',
    RESET_PASSWORD_TOKEN_EXPIRED: '000012',
    EMAIL_NOT_MATCH: '000013',
    PERMISSIONS_CHANGED: '000014',
    RANGE_DATE_INVALID: '000015',
};

export const errorMessage = {
    // System
    [errorCode.UNKNOW]: 'System error occured. Please try again later.',
    [errorCode.DATA_PROCESS]: 'Processing data has error',
    [errorCode.REQUEST_INVALID]: 'Request is invalid',
    [errorCode.ENTITY_EXISTED]: 'Entity already exists',
    [errorCode.UNAUTHORIZED]: 'Authorization required',
    [errorCode.FORBIDDEN]: 'Forbidden request',
    [errorCode.NOT_FOUND]: 'Entity not found',
    [errorCode.MISSING_MIDDLEWARE]: 'Make sure to apply tenantsMiddleware',
    [errorCode.TOKEN_EXPIRED]: 'Token expired',
    [errorCode.GOOGLE_SHEET_ERR]: 'Google sheet sync data has error(s)',
    [errorCode.ACCOUNT_NOT_HAVE_EMAIL]: 'Account not have email',
    [errorCode.RESET_PASSWORD_TOKEN_EXPIRED]: 'Reset token expired',
    [errorCode.EMAIL_NOT_MATCH]: 'There is no active email associated with the username',
    [errorCode.PERMISSIONS_CHANGED]: 'User permissions changed. Please re-login',
    [errorCode.RANGE_DATE_INVALID]: 'Range date invalid',
};
