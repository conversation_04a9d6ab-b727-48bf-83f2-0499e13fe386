import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { NeuralNetworkBgComponent } from '@shared/components/neural-network-bg/neural-network-bg.component';
import { FORM_IMPORTS } from '@shared/constants/import.constant';
import { AuthService } from '@shared/services';
import { NgxSpinnerService } from 'ngx-spinner';
import { finalize } from 'rxjs';

@Component({
    selector: 'app-login',
    standalone: true,
    imports: [CommonModule, NeuralNetworkBgComponent, ...FORM_IMPORTS],
    templateUrl: './login.component.html'
})
export class LoginComponent {
    fb: FormBuilder = inject(FormBuilder);
    constructor(
        private authService: AuthService,
        private spinner: NgxSpinnerService,
        private router: Router
    ) {}

    loginForm = this.fb.group({
        username: [null, [Validators.required]],
        password: [null, [Validators.required]]
    });

    onLogin() {
        const { username, password } = this.loginForm.value;
        if (!username || !password) {
            return;
        }
        this.spinner.show();
        this.authService
            .login(username, password)
            .pipe(finalize(() => this.spinner.hide()))
            .subscribe();
    }

    routeForgotPassword() {
        this.router.navigateByUrl('/auth/forgot-password');
    }
}
