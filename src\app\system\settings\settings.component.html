<div class="settings-container card flex flex-row gap-4">
    <!-- Settings Sidebar -->
    <div class="settings-sidebar pr-4 w-64" style="border-right: 1px solid var(--surface-border)">
            <h3 class="text-xl font-semibold mb-4">Tấ<PERSON> cả cài đặt</h3>
            <p-panelMenu    [model]="settingsMenu" 
                            multiple="true" transitionOptions="100ms cubic-bezier(0.86, 0, 0.07, 1)" styleClass="border-l w-full">
                <ng-template #item let-item>
                    <a *ngIf="!item.routerLink" pRipple
                        class="flex items-center px-4 py-2 cursor-pointer group font-bold">
                        <i [class]="item.icon + ' text-primary group-hover:text-inherit'"></i>
                        <span class="ml-2">
                            {{ item.label }}
                        </span>
                    </a>
                    <a *ngIf="item.routerLink" pRipple
                        [routerLink]="item.routerLink"
                        routerLinkActive="active-route"
                        [routerLinkActiveOptions]="{exact: false}"
                        class="flex items-center px-4 py-2 cursor-pointer group">
                        <i [class]="item.icon + ' text-primary group-hover:text-inherit'"></i>
                        <span class="ml-2">
                            {{ item.label }}
                        </span>
                    </a>
                </ng-template>
            </p-panelMenu>
    </div>

    <!-- Settings Content -->
    <div class="settings-content flex-1">
        <router-outlet></router-outlet>
    </div>
</div>
