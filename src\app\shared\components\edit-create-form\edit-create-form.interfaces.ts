import { ValidatorFn } from '@angular/forms';
import { SelectConfig } from '@shared/components/generic-select';
import { TreeSelectConfig } from '@shared/components/generic-treeselect';

export interface FormFieldConfig {
  key: string;
  label: string | null;
  required?: boolean;
  width?: 'full' | 'half';
  type: 'text' | 'textarea' | 'datepicker' | 'checkbox' | 'toggleSwitch' | 'select' | 'treeselect' | 'static' | 'custom';
  validators?: ValidatorFn[];
  placeholder?: string;
  tooltip?: string | ((editMode: boolean, formValue: any) => string); // For pTooltip
  customErrorMessage?: string; // Custom error message
  disabled?: boolean | ((editMode: boolean, formValue: any) => boolean);
  hidden?: boolean | ((editMode: boolean, formValue: any) => boolean);
  config?: SelectConfig | TreeSelectConfig; // For generic-p-select config, generic-p-treeselect config, etc.
  templateRef?: string; // For @ViewChild references
  rows?: number; // For textarea
  dependsOn?: string; // Field key that this field depends on for dependency tracking
  autoFormat?: string; // For autoFormat directive (e.g., 'vnPhone')
  showDividerAfter?: boolean; // Whether to show a divider after this field
  value?: (data: any) => string; // For static fields to display computed values
  labelPosition?: 'default' | 'inline'; // Position of the label: 'default' (top) or 'inline' (next to field value)
}

export interface FieldGroup extends Array<FormFieldConfig> {}

export interface FormConfig {
  fields: FormFieldConfig[];
}

// Type for form entity types - add new types here as configs are created
export type FormEntityType = 
  | 'employeeInfo' 
  | 'employeePosition' 
  | 'employeeProbation' 
  | 'employeePositionDialog' 
  | 'probationDialog' 
  | 'employeeDialog' 
  | 'retireEmployeePosition' 
  | 'position';

// Factory function type for form configs
export type FormConfigFactory = () => FormConfig;

// Registry type for dynamic imports
export type FormConfigRegistry = Record<FormEntityType, () => Promise<FormConfigFactory>>;
