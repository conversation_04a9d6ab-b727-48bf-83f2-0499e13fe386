import { inject } from '@angular/core';
import { PositionApiService } from '@shared/services';
import { DialogHandlerConfig, EditCreateDialogConfig } from '../edit-create-dialog.interfaces';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { positionFormConfig } from '@shared/components/edit-create-form';

export const positionDialogConfig = (onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig => {
  const positionApiService = inject(PositionApiService);

  return {
    width: '550px',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    formConfig: positionFormConfig(),
    actions: [defaultCancelAction, defaultConfirmAction],
    handlerConfig: {
      service: positionApiService,
      createMethod: 'createPosition',
      updateMethod: 'updatePosition',
      entityLabel: 'vị trí',
      commonDataTransform: (formValue: any) => ({
        code: formValue.code,
        name: formValue.name,
        roleId: formValue.roleId,
        departmentId: formValue.departmentId
      }),
      updateDataTransform: (formValue: any) => ({
        id: formValue.id
      }),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id
      }),
      onSuccess,
      onCancel
    } as DialogHandlerConfig
  };
};
