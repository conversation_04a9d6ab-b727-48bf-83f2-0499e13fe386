import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { IsString, IsNotEmpty, IsOptional, IsEmail, IsDateString } from 'class-validator';

export class ProfileDTO {
    @ApiProperty({ description: 'fullname' })
    @Expose()
    @IsString()
    @IsNotEmpty()
    readonly fullname: string;

    @ApiProperty({ description: 'nickname' })
    @Expose()
    @IsOptional()
    @IsString()
    readonly nickname: string;

    @ApiProperty({ description: 'birthday' })
    @Expose()
    @IsOptional()
    @IsDateString({ strict: true })
    readonly birthday: string;

    @ApiProperty({ description: 'identification' })
    @Expose()
    @IsOptional()
    @IsString()
    readonly identification: string;

    @ApiProperty({ description: 'phone' })
    @Expose()
    @IsOptional()
    @IsString()
    readonly phone: string;

    @ApiProperty({ description: 'email' })
    @Expose()
    @IsOptional()
    @IsEmail()
    @Transform(({ value }) => value?.toLowerCase())
    readonly email: string;
}
