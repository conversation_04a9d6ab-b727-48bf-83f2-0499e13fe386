import { Connection } from 'mongoose';
import {
    CenterLocationSchema,
    CourseSchema,
    SyncErrorDataSchema,
    LearnerEventSchema,
    LearnerSchema,
    SettingSchema,
    UserSchema,
    PartnerSchema,
    PermissionSchema,
    RoleSchema,
    EmployeeSchema,
    ProfileSchema,
    DepartmentSchema,
    PositionSchema,
    EmployeePositionSchema,
    EmployeePositionHistorySchema,
    ProbationSchema,
} from './schemas';
import { MONGO_CONST } from './mongodb.constants';

export const MONGO_COLLECTION_PROVIDERS = [
    {
        provide: MONGO_CONST.USER_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.USER_COLLECTION, UserSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.COURSE_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.COURSE_COLLECTION, CourseSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.LEARNER_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.LEARNER_COLLECTION, LearnerSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.LEARNER_EVENT_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.LEARNER_EVENT_COLLECTION, LearnerEventSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.SETTING_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.SETTING_COLLECTION, SettingSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.SYNC_ERROR_DATA_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.SYNC_ERROR_DATA_COLLECTION, SyncErrorDataSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.CENTER_LOCATION_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.CENTER_LOCATION_COLLECTION, CenterLocationSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.PARTNER_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.PARTNER_COLLECTION, PartnerSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.PERMISSION_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.PERMISSION_COLLECTION, PermissionSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.ROLE_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.ROLE_COLLECTION, RoleSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.EMPLOYEE_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.EMPLOYEE_COLLECTION, EmployeeSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.PROFILE_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.PROFILE_COLLECTION, ProfileSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.DEPARTMENT_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.DEPARTMENT_COLLECTION, DepartmentSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.POSITION_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.POSITION_COLLECTION, PositionSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.EMPLOYEE_POSITION_COLLECTION, EmployeePositionSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.EMPLOYEE_POSITION_HISTORY_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.EMPLOYEE_POSITION_HISTORY_COLLECTION, EmployeePositionHistorySchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
    {
        provide: MONGO_CONST.PROBATION_COLLECTION,
        useFactory: async (connection: Connection) => connection.model(MONGO_CONST.PROBATION_COLLECTION, ProbationSchema),
        inject: [MONGO_CONST.CON_CORE_TOKEN],
    },
];
