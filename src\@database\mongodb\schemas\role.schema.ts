import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

export interface IRoleDocument {
    id: string;
    code: string;
    name: string;
    permissions: string[];
}

@Schema({ versionKey: false, timestamps: true })
export class RoleDocument extends Audit {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ required: true })
    name: string;
    @Prop({ required: true })
    permissions: string[];
}

export const RoleSchema = SchemaFactory.createForClass(RoleDocument);
