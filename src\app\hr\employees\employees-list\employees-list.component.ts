import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { EditCreateDialogComponent, EditCreateDialogConfig, employeeDialogConfig } from '@shared/components/edit-create-dialog';
import { GenericPTableComponent, TableConfig, TableDataLoadEvent, employeeTableConfig } from '@shared/components/generic-p-table';
import { GenericPStepperComponent, StepperConfig, employeeCreationStepperConfig } from '@shared/components/generic-stepper';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';
import {
    EmployeeApiService,
    ErrorHandlerService,
    ToastService,
    mapFunctions
} from '@shared/services';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DrawerModule } from 'primeng/drawer';
import { EmployeeDetailComponent } from './employee-detail/employee-detail.component';

@Component({
    selector: 'app-employees-list',
    standalone: true,
    imports: [
        CommonModule,
        DrawerModule,
        EditCreateDialogComponent,
        GenericPStepperComponent,
        EmployeeDetailComponent,
        GenericTagComponent,
        GenericPTableComponent
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './employees-list.component.html',
    styleUrls: ['./employees-list.component.scss'],
})
export class EmployeesListComponent implements OnInit {
    // Table configuration
    employeeLazyScrollPTableConfig!: TableConfig;
    editEmployeeDialogConfig!: EditCreateDialogConfig;
    employeeCreationStepperConfig!: StepperConfig;

    employees: any[] = [];

    employeeCreationStepper: boolean = false;
    editMode: boolean = false;
    selectedEmployee: any = null;

    // Properties for simple edit dialog
    editEmployeeDialog: boolean = false;
    selectedEmployeeForEdit: any = null;

    // Properties for row selection and drawer
    selectedEmployeeRow: any = null;
    drawerVisible: boolean = false;

    // Pagination properties - now managed by table component
    totalRecords: number = 0;

    // ViewChild for table component
    @ViewChild(GenericPTableComponent) tableComponent!: GenericPTableComponent;

    constructor(
        private employeeApiService: EmployeeApiService,
        private errorHandlerService: ErrorHandlerService,
        private toastService: ToastService,
        private confirmationService: ConfirmationService
    ) {
        // Initialize table configuration using direct import
        this.employeeLazyScrollPTableConfig = mapFunctions(employeeTableConfig(), {
            OPEN_NEW: () => this.openNew(),
            EDIT: (item: any) => this.editEmployee(item),
            DELETE: (item: any) => this.deleteEmployee(item),
            ON_SELECT: (item: any, originalEvent: Event) => this.onRowSelect({ data: item, originalEvent }),
            ON_UNSELECT: (item: any, originalEvent: Event) => this.onRowUnselect({ data: item, originalEvent })
        });

        // Initialize edit employee dialog configuration using direct import
        this.editEmployeeDialogConfig = employeeDialogConfig(
            () => this.onEditDialogSuccess(),
            () => this.onEditDialogCancel()
        );

        // Initialize stepper configuration
        this.employeeCreationStepperConfig = employeeCreationStepperConfig(
            () => this.onStepperSuccess(),
            () => this.onStepperCancel()
        );
    }

    ngOnInit() {
    }

    // Table Functions
    onTableDataLoaded(event: TableDataLoadEvent) {
        this.employees = event.items;
        this.totalRecords = event.totalRecords;
    }

    openNew() {
        this.editMode = false;
        this.selectedEmployee = null;
        this.employeeCreationStepper = true;
    }

    editEmployee(employee: any) {
        this.selectedEmployeeForEdit = employee;
        this.editEmployeeDialog = true;
    }

    deleteEmployee(employee: any) {
        this.confirmationService.confirm({
            message: 'Bạn có chắc chắn muốn xóa nhân viên này?',
            header: 'Xác nhận',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.employeeApiService.deleteEmployee(employee.code).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Xóa nhân viên thành công');
                            this.tableComponent?.refresh();
                        }
                    }
                });
            }
        });
    }

    onRowSelect(event: any) {
        this.selectedEmployeeRow = event.data;
        this.drawerVisible = true;
    }

    onRowUnselect(_event: any) {
        this.selectedEmployeeRow = null;
        this.drawerVisible = false;
    }

    // Stepper functions
    onStepperSuccess() {
        this.employeeCreationStepper = false;
    }

    onStepperCancel() {
        this.employeeCreationStepper = false;
    }

    // Drawer & form functions
    closeDrawer() {
        this.selectedEmployeeRow = null;
        this.drawerVisible = false;
    }

    // Edit-create-dialog functions
    onEditDialogSuccess() {
        this.editEmployeeDialog = false;
        this.tableComponent?.refresh();
        this.selectedEmployeeForEdit = null;
    }

    onEditDialogCancel() {
        this.editEmployeeDialog = false;
        this.selectedEmployeeForEdit = null;
    }
}
