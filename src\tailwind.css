@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

    .card {
        @apply shadow;
    }

    .p-inputtext,
    .p-select,
    .p-multiselect,
    .p-treeselect,
    .p-calendar,
    .p-inputnumber-input {
        @apply h-11;
    }

    .p-select .p-inputtext,
    .p-multiselect-label,
    .p-treeselect-label,
    .p-calendar .p-inputtext {
        @apply h-11;
    }

    .p-multiselect-label {
        @apply flex items-center;
    }

    span.p-placeholder {
        @apply flex items-center;
    }

    .p-datepicker .p-button {
        @apply h-11;
    }

    a.active-route {
        background: var(--theme-main-background);
    }

    a.active-route-tab {
        background: var(--p-menubar-item-focus-background);
    }

    .p-datatable-table .p-datatable-tbody td.p-datatable-frozen-column {
        background: inherit;
    }
}