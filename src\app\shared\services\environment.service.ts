import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { environment } from '@environments/environment';
import { API_ENDPOINTS, ApiEndpoint } from '@shared/constants/api-endpoints.constant';

@Injectable({ providedIn: 'root' })
export class EnvironmentService {
    private readonly STORAGE_KEY = 'selected_api_endpoint';
    
    private apiUrlSubject = new BehaviorSubject<string>(this.getInitialApiUrl());
    public apiUrl$ = this.apiUrlSubject.asObservable();

    constructor() {
        // Initialize with stored value or default environment
        const storedUrl = this.getStoredApiUrl();
        if (storedUrl && storedUrl !== environment.API_URL) {
            this.setApiUrl(storedUrl);
        }
    }

    private getInitialApiUrl(): string {
        const storedUrl = this.getStoredApiUrl();
        return storedUrl || environment.API_URL;
    }

    private getStoredApiUrl(): string | null {
        if (typeof localStorage !== 'undefined') {
            return localStorage.getItem(this.STORAGE_KEY);
        }
        return null;
    }

    private storeApiUrl(url: string): void {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(this.STORAGE_KEY, url);
        }
    }

    getCurrentApiUrl(): string {
        return this.apiUrlSubject.value;
    }

    setApiUrl(url: string): void {
        this.apiUrlSubject.next(url);
        this.storeApiUrl(url);
    }

    getAvailableEndpoints(): ApiEndpoint[] {
        return API_ENDPOINTS;
    }

    getCurrentEndpoint(): ApiEndpoint | undefined {
        const currentUrl = this.getCurrentApiUrl();
        return API_ENDPOINTS.find(endpoint => endpoint.url === currentUrl);
    }

    resetToDefault(): void {
        this.setApiUrl(environment.API_URL);
        if (typeof localStorage !== 'undefined') {
            localStorage.removeItem(this.STORAGE_KEY);
        }
    }
}
