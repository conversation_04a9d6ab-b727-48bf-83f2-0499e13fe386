import { <PERSON>pt<PERSON><PERSON><PERSON><PERSON>, <PERSON>Helper } from '@app/shared/helpers';
import { Bc<PERSON>rror, RequestInvalidError, UnauthorizedError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { MailerService } from './mailer.service';
import { ForgotPasswordDTO, JwtPayloadDTO, ResetPasswordDTO } from '@app/models/dto';
import { UserRepository } from '@database/mongodb/repositories';
import { errorCode } from '@errors/error-message';

@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);
    constructor(
        private userRepository: UserRepository,
        private jwtService: JwtService,
        private mailerService: MailerService,
    ) {}

    public async signIn(username: string, password: string): Promise<any> {
        this.logger.debug('signIn', username);
        const user = await this.getByUsername(username);
        if (!user) {
            throw new UnauthorizedError('Username invalid');
        }
        const isPasswordMatching = CryptopHelper.verifyPassword(password, user.salt, user.password);
        if (!isPasswordMatching) {
            throw new UnauthorizedError('Wrong credentials provided');
        }
        const payload: JwtPayloadDTO = { sub: user.id, username: user.username, perKey: user.permissionsKey, type: user.type };
        return {
            token: await this.jwtService.signAsync(payload),
        };
    }

    public async forgotPassword(dto: ForgotPasswordDTO) {
        this.logger.debug('forgotPassword', dto);
        const { username, email } = dto;
        const userInfo: any = await this.userRepository.getUserInfo(username);
        if (!userInfo) {
            throw new RequestInvalidError('username invalid');
        }
        if (!userInfo || StringHelper.isEmpty(userInfo.email)) {
            throw new BcError(errorCode.ACCOUNT_NOT_HAVE_EMAIL);
        }
        if ((userInfo.email as string).toLowerCase() != email) {
            throw new BcError(errorCode.EMAIL_NOT_MATCH);
        }
        const resetCode = `${CryptopHelper.generateRandom(8)}${StringHelper.HYPHEN}${Date.now()}`;
        await this.userRepository.updateOne({ username }, { resetCode });
        await this.mailerService.sendForgotPasswordUser(userInfo.email, userInfo.fullname, resetCode);
        return { email: userInfo.email };
    }

    public async resetPassword(dto: ResetPasswordDTO) {
        this.logger.debug('resetPassword', dto.code);
        const user = await this.userRepository.findOne({ resetCode: dto.code, deletedAt: null });
        if (!user) {
            throw new RequestInvalidError('Code not found');
        }
        const splitCode = dto.code.split(StringHelper.HYPHEN);
        if (+splitCode[1] > Date.now() + 60 * 60 * 1000) {
            // Expired in 1h
            throw new RequestInvalidError('Code is expired');
        }
        const { salt, hash } = CryptopHelper.hashPassword(dto.newPassword);
        await this.userRepository.updateOne({ id: user.id }, { password: hash, salt, resetCode: null });
    }

    async getByUsername(username: string) {
        return this.userRepository.findOne({ username, deletedAt: null });
    }
}
