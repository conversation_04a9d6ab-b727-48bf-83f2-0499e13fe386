<p-dialog [(visible)]="visible"
          [style]="{width: dialogWidth}"
          [header]="dialogHeader"
          [modal]="dialogConfig.modal !== false"
          [dismissableMask]="dialogConfig.dismissableMask !== false"
          [styleClass]="dialogConfig.styleClass || 'p-fluid'"
          (onHide)="onDialogHide()">

    <ng-template pTemplate="content">
        <edit-create-form
          [editMode]="editMode"
          [initialData]="transformedInitialData"
          [formConfig]="formConfig"
          (formChange)="onFormChange($event)">

          <!-- Custom Field Template Slot -->
          <ng-container *ngIf="customFieldTemplate && form">
            <ng-container *ngTemplateOutlet="customFieldTemplate; context: { $implicit: form, editMode: editMode }">
            </ng-container>
          </ng-container>
        </edit-create-form>
    </ng-template>

    <ng-template pTemplate="footer">
        <!-- Custom Actions -->
        <ng-container *ngFor="let action of dialogConfig.actions">
            <p-button *ngIf="action.useDefaultStyle === 'cancel'"
                      defaultCancelButton
                      [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false, editMode) : false"
                      (onClick)="executeAction(action)">
            </p-button>

            <p-button *ngIf="action.useDefaultStyle === 'confirm'"
                      defaultConfirmButton
                      [type]="action.type || 'button'"
                      [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false, editMode) : false"
                      (onClick)="executeAction(action)">
            </p-button>

            <p-button *ngIf="!action.useDefaultStyle"
                      [label]="action.label || ''"
                      [icon]="action.icon || ''"
                      [severity]="action.severity"
                      [type]="action.type || 'button'"
                      [disabled]="action.disabled ? action.disabled(form?.value || {}, form?.valid || false, editMode) : false"
                      (onClick)="executeAction(action)">
            </p-button>
        </ng-container>

        <!-- Default Actions -->
        <ng-container *ngIf="showDefaultActions">
            <p-button defaultCancelButton
                      (onClick)="executeAction(getDefaultCancelAction())">
            </p-button>
            <p-button defaultConfirmButton
                      [type]="'submit'"
                      [disabled]="getDefaultSaveAction().disabled!(form?.value || {}, form?.valid || false, editMode)"
                      (onClick)="executeAction(getDefaultSaveAction())">
            </p-button>
        </ng-container>
    </ng-template>
</p-dialog>