{"name": "ws-be", "version": "1.0.0", "description": "", "author": "weset", "private": true, "license": "UNLICENSE", "scripts": {"start:watch": "nodemon -e ts --watch main.js", "start:live": "nodemon -e ts main.js", "build": "tsc -p tsconfig.json", "start": "cross-env HOST_ENV=local ts-node src/main", "start:docker": "cross-env HOST_ENV=docker ts-node src/main", "start:prod": "cross-env HOST_ENV=production ts-node src/main", "start:debug": "cross-env HOST_ENV=local tsc-watch -p tsconfig.build.json --onSuccess \"node --inspect-brk dist/main.js\"", "start:dev": "cross-env HOST_ENV=dev tsc-watch -p tsconfig.build.json --onSuccess \"node dist/main.js\"", "test": "jest --config=jest.json", "test:watch": "jest --watch --config=jest.json", "test:coverage": "jest --config=jest.json --coverage --coverageDirectory=coverage", "e2e": "jest --config=e2e/jest-e2e.json --forceExit", "e2e:watch": "jest --watch --config=e2e/jest-e2e.json"}, "dependencies": {"@googleapis/sheets": "^9.8.0", "@nestjs/axios": "3.0.2", "@nestjs/common": "10.3.7", "@nestjs/config": "3.2.3", "@nestjs/core": "10.3.7", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "10.2.0", "@nestjs/mongoose": "10.0.6", "@nestjs/platform-express": "10.3.7", "@nestjs/schedule": "4.0.2", "@nestjs/swagger": "7.3.1", "@nestjs/typeorm": "10.0.2", "axios": "1.7.2", "body-parser": "1.20.2", "class-transformer": "0.5.1", "class-validator": "0.14.1", "content-filter": "1.1.2", "dotenv": "16.0.3", "exceljs": "4.4.0", "express": "4.19.2", "google-auth-library": "9.15.1", "handlebars": "4.7.7", "helmet": "6.0.0", "lodash": "4.17.21", "module-alias": "2.2.2", "moment": "2.29.4", "mongodb": "5.9.2", "mongoose": "8.3.3", "morgan": "1.10.0", "multer": "1.4.5-lts.1", "mysql2": "3.11.5", "nodemailer": "6.9.13", "nodemon": "^3.1.4", "reflect-metadata": "0.1.13", "rxjs": "7.5.7", "sharp": "0.33.4", "swagger-ui-express": "4.5.0", "typeorm": "0.3.20", "uuid": "9.0.0"}, "devDependencies": {"@nestjs/cli": "9.5.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "10.3.7", "@types/express": "^4.17.13", "@types/jest": "29.2.3", "@types/lodash": "4.14.187", "@types/multer": "1.4.12", "@types/node": "^20.12.7", "@types/supertest": "^2.0.11", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "7.6.0", "@typescript-eslint/parser": "7.6.0", "cross-env": "7.0.3", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-prettier": "5.1.3", "jest": "29.2.2", "prettier": "3.2.5", "source-map-support": "^0.5.20", "supertest": "6.3.4", "ts-jest": "29.0.3", "ts-loader": "9.4.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.0", "typescript": "^4.8.4"}}