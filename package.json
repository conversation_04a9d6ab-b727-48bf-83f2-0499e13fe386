{"name": "weset-fe", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve", "compress:gzip": "gzipper compress ./dist/weset-fe/browser", "serve:prod": "npx serve -s dist/weset-fe/browser -l 4200", "start:prod": "npm run build:prod && npm run serve:prod", "start:prod-compress": "npm run build:prod && npm run compress:gzip && npm run serve:prod", "start:prod-nocompress": "ng build --configuration production --stats-json && npx serve -s dist/weset-fe/browser -l 4200 --no-compression", "build": "ng build", "build:dev": "ng build --configuration development --stats-json", "build:dev-optimized": "ng build --configuration development-optimized --stats-json", "build:prod": "ng build --configuration production --stats-json", "build:prod-compress": "ng build --configuration production --stats-json && gzipper compress ./dist/weset-fe/browser", "build:prod-nocompress": "ng build --configuration production --stats-json --no-compression", "build:miniapp": "ng build --configuration production-mobile && node miniapp-postbuild.js", "watch": "ng build --watch --configuration development", "format": "prettier --write \"**/*.{js,mjs,ts,mts,d.ts,html}\" --cache", "test": "ng test", "test:debug": "ng test --browsers=Chrome", "docs:index": "node scripts/generate-docs-index.js", "docs:serve": "npx http-server docs -p 8080 -o", "docs:open": "start docs/README.md"}, "private": true, "dependencies": {"@angular/animations": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@primeng/themes": "^19.0.5", "chart.js": "4.4.2", "crypto-js": "^4.2.0", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "moment": "^2.30.1", "ngx-spinner": "^19.0.0", "primeclt": "^0.1.5", "primeicons": "^7.0.0", "primeng": "^19.0.8", "rxjs": "~7.8.0", "tailwindcss-primeui": "^0.5.1", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.0.6", "@angular/cli": "^19.0.6", "@angular/compiler-cli": "^19.0.0", "@fullhuman/postcss-purgecss": "^7.0.2", "@types/crypto-js": "^4.2.2", "@types/jasmine": "~5.1.0", "@types/leaflet": "^1.9.16", "@types/node": "^22.13.10", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^4.2.1", "gzipper": "^7.2.0", "jasmine-core": "~5.4.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.49", "prettier": "^3.0.0", "purgecss": "^7.0.2", "serve": "^14.2.4", "tailwindcss": "^3.4.17", "typescript": "~5.6.2"}}