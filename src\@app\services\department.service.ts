import { JwtPayloadDTO, SearchOptionsDTO } from '@app/models/dto';
import { DepartmentDTO } from '@app/models/dto/department.dto';
import { PaginationRequest } from '@app/models/pagination';
import { <PERSON><PERSON>y<PERSON><PERSON>per, StringHelper } from '@app/shared/helpers';
import { DepartmentRepository } from '@database/mongodb/repositories';
import { EntityExistedError, NotFoundError, RequestInvalidError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class DepartmentService {
    private readonly logger = new Logger(DepartmentService.name);

    constructor(private departmentRepository: DepartmentRepository) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.departmentRepository.findWithPagination(request);
    }

    async getRoots() {
        const items = await this.departmentRepository.findAll({ deletedAt: null, parentId: null });
        return { items };
    }

    async getAllDepartments() {
        return await this.departmentRepository.findAll({ deletedAt: null });
    }

    async getDepartmentOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.departmentRepository.getOptions(request, body);
    }

    async createDepartment(dto: DepartmentDTO, logged: JwtPayloadDTO) {
        const departments = await this.departmentRepository.findAll({ deletedAt: null });
        if (departments.findIndex(i => i.code == dto.code) != -1) {
            throw new EntityExistedError('Department code exist');
        }
        if (!StringHelper.isEmpty(dto.parentId) && departments.findIndex(i => i.id == dto.parentId) == -1) {
            throw new RequestInvalidError('Parent department not exist');
        }
        await this.departmentRepository.create({
            code: dto.code,
            name: dto.name,
            parentId: dto.parentId,
            createdBy: logged.username,
            updatedBy: logged.username,
        });
    }

    async updateDepartment(dto: DepartmentDTO, logged: JwtPayloadDTO) {
        const departments = await this.departmentRepository.findAll({ deletedAt: null });
        const index = departments.findIndex(i => i.code == dto.code);
        if (index == -1) {
            throw new NotFoundError('Department code not found');
        }
        if (dto.parentId == departments[index].id) {
            throw new RequestInvalidError('Cannot move department into itself');
        }
        const parents = departments.filter(i => i.id == dto.parentId);
        if (ArrayHelper.isEmpty(parents)) {
            throw new RequestInvalidError('Parent department does not exist');
        }
        await this.departmentRepository.updateOne({ code: dto.code }, { name: dto.name, parentId: dto.parentId, updatedBy: logged.username });
    }

    // Implement After
    async deleteDepartment(code: string, logged: JwtPayloadDTO) {}

    // buildTree(depts: any[]) {
    //     const map = new Map<string, any>();
    //     const roots: any[] = [];

    //     // Khởi tạo map
    //     depts.forEach(d => {
    //         map.set(d._id.toHexString(), { ...d, children: [] });
    //     });

    //     // Gắn con vào cha
    //     map.forEach(d => {
    //         if (d.parentId) {
    //             const parent = map.get(d.parentId.toHexString());
    //             if (parent) parent.children!.push(d);
    //         } else {
    //             roots.push(d);
    //         }
    //     });
    //     return roots;
    // }
}
