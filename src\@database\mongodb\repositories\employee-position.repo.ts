import { EmployeePositionStatusEnum, ProbationStatusEnum } from '@app/enums';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { Array<PERSON><PERSON>per, DateHelper, StringHelper } from '@app/shared/helpers';
import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { EmployeePositionDocument } from '../schemas';
import { SearchOptionsDTO } from '@app/models/dto';

@Injectable()
export class EmployeePositionRepository extends GenericRepository<EmployeePositionDocument> {
    private readonly logger = new Logger(EmployeePositionRepository.name);

    constructor(
        @Inject(MONGO_CONST.EMPLOYEE_POSITION_COLLECTION)
        private readonly employeePositionModel: Model<EmployeePositionDocument>,
    ) {
        super(employeePositionModel);
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const employeeId = andQ?.employeeId || null;
        const positionName = orQ?.positionName || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        }
        if (!StringHelper.isEmpty(employeeId)) {
            matchQ['employeeId'] = employeeId;
        }
        if (!StringHelper.isEmpty(positionName)) {
            matchQ['$or'] = [{ 'position.name': { $regex: positionName, $options: 'i' } }];
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.employeePositionModel.aggregate([
            { $lookup: { from: MONGO_CONST.POSITION_COLLECTION, localField: 'positionId', foreignField: 'id', as: 'position' } },
            { $unwind: '$position' },
            { $match: matchQ },
            { $lookup: { from: MONGO_CONST.EMPLOYEE_COLLECTION, localField: 'employeeId', foreignField: 'id', as: 'employee' } },
            { $unwind: '$employee' },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    employeeCode: '$employee.code',
                    positionCode: '$position.code',
                    positionName: '$position.name',
                    fromDate: 1,
                    toDate: 1,
                    status: 1,
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async getEmployeePositionExtProbation(id: string) {
        return await this.employeePositionModel.aggregate([
            {
                $match: { id, deletedAt: null },
            },
            {
                $lookup: {
                    from: MONGO_CONST.PROBATION_COLLECTION,
                    localField: 'id',
                    foreignField: 'employeePositionId',
                    as: 'probation',
                },
            },
            {
                $unwind: {
                    path: '$probation',
                    preserveNullAndEmptyArrays: true,
                },
            },
        ]);
    }

    async findWithPaginationAggregated(request: PaginationRequest) {
        const itemsPipeline = [];
        if (request.sort && Object.keys(request.sort).length > 0) {
            itemsPipeline.push({ $sort: request.sort });
        }
        itemsPipeline.push({ $skip: request.offset });
        itemsPipeline.push({ $limit: request.limit });
        itemsPipeline.push(
            {
                $lookup: {
                    from: MONGO_CONST.POSITION_COLLECTION,
                    let: { letPositionId: '$positionId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letPositionId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'position',
                },
            },
            {
                $unwind: {
                    path: '$position',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.DEPARTMENT_COLLECTION,
                    let: { letDepartmentId: '$departmentId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letDepartmentId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'department',
                },
            },
            {
                $unwind: {
                    path: '$department',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_COLLECTION,
                    let: { letEmployeeId: '$employeeId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letEmployeeId'],
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.PROFILE_COLLECTION,
                                localField: 'id',
                                foreignField: 'id',
                                as: 'profile',
                            },
                        },
                        {
                            $unwind: '$profile',
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: '$profile.fullname',
                            },
                        },
                    ],
                    as: 'employee',
                },
            },
            {
                $unwind: {
                    path: '$employee',
                    preserveNullAndEmptyArrays: true,
                },
            },
        );
        const result = await this.employeePositionModel.aggregate([
            { $match: request.query },
            {
                $facet: {
                    total: [{ $count: 'count' }],
                    items: itemsPipeline,
                },
            },
        ]);
        if (ArrayHelper.isEmpty(result)) {
            return new PaginationResponse({ total: 0, items: [], offset: request.offset, limit: request.limit, sort: request.sort });
        }
        const data = result[0];
        const totalCount = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total: totalCount, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async checkExistingPosition(employeeId: string, positionId: string, date: Date) {
        const positionExist = await this.employeePositionModel.aggregate([
            {
                $match: {
                    $and: [
                        { employeeId },
                        { positionId },
                        { fromDate: { $lte: date } },
                        {
                            $or: [
                                {
                                    toDate: null,
                                },
                                {
                                    toDate: { $gte: date },
                                },
                            ],
                        },
                    ],
                },
            },
        ]);
        return positionExist.length > 0;
    }

    async getEmployeePositionByIdFull(id: string) {
        return await this.employeePositionModel.aggregate([
            {
                $match: { id },
            },
            {
                $lookup: {
                    from: MONGO_CONST.POSITION_COLLECTION,
                    localField: 'positionId',
                    foreignField: 'id',
                    as: 'position',
                },
            },
            {
                $unwind: '$position',
            },
        ]);
    }

    async getEmployeePositionAvailableUpdate() {
        return await this.employeePositionModel.aggregate([
            { $match: { status: { $not: { $in: [EmployeePositionStatusEnum.INACTIVE, EmployeePositionStatusEnum.PROBATION_CANCELLED] } }, deletedAt: null } },
            {
                $group: {
                    _id: '$employeeId',
                    employeePositionIds: { $push: '$id' },
                },
            },
            {
                $project: {
                    _id: 0,
                    employeeIds: '$_id',
                    employeePositionIds: 1,
                },
            },
        ]);
    }

    async onChangeStatus(id: string): Promise<any> {
        const employeePosition = await this.getEmployeePositionExtProbation(id);

        if (ArrayHelper.isEmpty(employeePosition)) {
            return;
        }
        return await this.employeePositionModel.updateOne({ id }, { status: this.mappingStatus(employeePosition[0]) }, { new: true }).exec();
    }

    mappingStatus(data: any): EmployeePositionStatusEnum {
        const today = DateHelper.getStartOfDate().getTime();
        if (today < data.fromDate.getTime()) {
            return EmployeePositionStatusEnum.TO_ONBOARD;
        }
        if (data.toDate != null) {
            if (today > data.toDate.getTime()) {
                return EmployeePositionStatusEnum.INACTIVE;
            }
            return EmployeePositionStatusEnum.TO_INACTIVE;
        }

        if (!data.probation) {
            return EmployeePositionStatusEnum.ACTIVE;
        }

        if (data.probation?.status == ProbationStatusEnum.PASS) {
            if (today >= data.probation?.toDate.getTime()) {
                return EmployeePositionStatusEnum.ACTIVE;
            }
            return EmployeePositionStatusEnum.PROBATION_PASS;
        }
        if (data.probation?.status == ProbationStatusEnum.FAIL) {
            return EmployeePositionStatusEnum.PROBATION_FAIL;
        }
        if (data.probation?.status == ProbationStatusEnum.CANCELLED) {
            return EmployeePositionStatusEnum.PROBATION_CANCELLED;
        }
        if (data.probation?.status == ProbationStatusEnum.RESULT_PENDING) {
            return EmployeePositionStatusEnum.PROBATION_PENDING;
        }
        if (data.probation?.status == ProbationStatusEnum.IN_PROGRESS) {
            return EmployeePositionStatusEnum.PROBATION;
        }
    }
}
