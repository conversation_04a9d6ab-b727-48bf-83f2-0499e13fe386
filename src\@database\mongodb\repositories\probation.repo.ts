import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { ProbationDocument } from '../schemas';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { ArrayHelper } from '@app/shared/helpers';

@Injectable()
export class ProbationRepository extends GenericRepository<ProbationDocument> {
    private readonly context = ProbationRepository.name;

    constructor(
        @Inject(MONGO_CONST.PROBATION_COLLECTION)
        private readonly probationModel: Model<ProbationDocument>,
    ) {
        super(probationModel);
    }

    async findWithPaginationAggregate(request: PaginationRequest) {
        const itemsPipeline = [];
        if (request.sort && Object.keys(request.sort).length > 0) {
            itemsPipeline.push({ $sort: request.sort });
        }
        itemsPipeline.push({ $skip: request.offset });
        itemsPipeline.push({ $limit: request.limit });
        itemsPipeline.push(
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_POSITION_COLLECTION,
                    let: { letEmployeePositionId: '$employeePositionId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letEmployeePositionId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                employeeId: 1,
                                positionId: 1,
                                departmentId: 1,
                            },
                        },
                    ],
                    as: 'employeePosition',
                },
            },
            {
                $unwind: {
                    path: '$employeePosition',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.POSITION_COLLECTION,
                    let: { letPositionId: '$employeePosition.positionId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letPositionId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'position',
                },
            },
            {
                $unwind: {
                    path: '$position',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.DEPARTMENT_COLLECTION,
                    let: { letDepartmentId: '$employeePosition.departmentId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letDepartmentId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'department',
                },
            },
            {
                $unwind: {
                    path: '$department',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.EMPLOYEE_COLLECTION,
                    let: { letEmployeeId: '$employeePosition.employeeId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letEmployeeId'],
                                },
                            },
                        },
                        {
                            $lookup: {
                                from: MONGO_CONST.PROFILE_COLLECTION,
                                localField: 'id',
                                foreignField: 'id',
                                as: 'profile',
                            },
                        },
                        {
                            $unwind: '$profile',
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: '$profile.fullname',
                            },
                        },
                    ],
                    as: 'employee',
                },
            },
            {
                $unwind: {
                    path: '$employee',
                    preserveNullAndEmptyArrays: true,
                },
            },
        );
        const result = await this.probationModel.aggregate([
            { $match: request.query },
            {
                $facet: {
                    total: [{ $count: 'count' }],
                    items: itemsPipeline,
                },
            },
        ]);
        if (ArrayHelper.isEmpty(result)) {
            return new PaginationResponse({ total: 0, items: [], offset: request.offset, limit: request.limit, sort: request.sort });
        }
        const data = result[0];
        const totalCount = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total: totalCount, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }
}
