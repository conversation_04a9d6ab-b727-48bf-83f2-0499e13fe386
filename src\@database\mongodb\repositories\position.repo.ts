import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { PositionDocument } from '../schemas';
import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { ArrayHelper, StringHelper } from '@app/shared/helpers';
import { SearchOptionsDTO } from '@app/models/dto';

@Injectable()
export class PositionRepository extends GenericRepository<PositionDocument> {
    private readonly logger = new Logger(PositionRepository.name);

    constructor(
        @Inject(MONGO_CONST.POSITION_COLLECTION)
        private readonly positionModel: Model<PositionDocument>,
    ) {
        super(positionModel);
    }

    async getOptions(request: PaginationRequest, body: SearchOptionsDTO): Promise<PaginationResponse<any>> {
        const { offset, limit, sort } = request;
        const { orQ, andQ } = body;
        const id = andQ?.id || null;
        const rootDepartmentId = andQ?.rootDepartmentId || null;
        const code = orQ?.code || null;
        const name = orQ?.name || null;

        const matchQ = { deletedAt: null };
        if (!StringHelper.isEmpty(id)) {
            matchQ['id'] = id;
        }
        if (!StringHelper.isEmpty(rootDepartmentId)) {
            matchQ['departmentId'] = rootDepartmentId;
        }
        if (!StringHelper.isEmpty(code)) {
            matchQ['$or'] = [{ code: { $regex: code, $options: 'i' } }];
        }
        if (!StringHelper.isEmpty(name)) {
            if (ArrayHelper.isEmpty(matchQ['$or'])) {
                matchQ['$or'] = [{ name: { $regex: name, $options: 'i' } }];
            } else {
                matchQ['$or'].push({ name: { $regex: name, $options: 'i' } });
            }
        }
        this.logger.debug(`matchQ: ${JSON.stringify(matchQ)}`);
        let result = await this.positionModel.aggregate([
            { $lookup: { from: MONGO_CONST.DEPARTMENT_COLLECTION, localField: 'departmentId', foreignField: 'id', as: 'department' } },
            { $unwind: '$department' },
            {
                $match: matchQ,
            },
            {
                $project: {
                    _id: 0,
                    id: 1,
                    code: 1,
                    name: 1,
                    departmentName: '$department.name',
                },
            },
            {
                $facet: {
                    total: [
                        {
                            $count: 'count',
                        },
                    ],
                    items: [{ $sort: sort }, { $skip: offset }, { $limit: limit }],
                },
            },
        ]);
        const data = result[0];
        const total = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async findWithPaginationAggregated(request: PaginationRequest) {
        const itemsPipeline = [];
        if (request.sort && Object.keys(request.sort).length > 0) {
            itemsPipeline.push({ $sort: request.sort });
        }
        itemsPipeline.push({ $skip: request.offset });
        itemsPipeline.push({ $limit: request.limit });
        itemsPipeline.push(
            {
                $lookup: {
                    from: MONGO_CONST.ROLE_COLLECTION,
                    let: { letRoleId: '$roleId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letRoleId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'role',
                },
            },
            {
                $unwind: {
                    path: '$role',
                    preserveNullAndEmptyArrays: true,
                },
            },
            {
                $lookup: {
                    from: MONGO_CONST.DEPARTMENT_COLLECTION,
                    let: { letDepartmentId: '$departmentId' },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: ['$id', '$$letDepartmentId'],
                                },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                code: 1,
                                name: 1,
                            },
                        },
                    ],
                    as: 'department',
                },
            },
            {
                $unwind: {
                    path: '$department',
                    preserveNullAndEmptyArrays: true,
                },
            },
        );
        const result = await this.positionModel.aggregate([
            { $match: request.query },
            {
                $facet: {
                    total: [{ $count: 'count' }],
                    items: itemsPipeline,
                },
            },
        ]);
        if (ArrayHelper.isEmpty(result)) {
            return new PaginationResponse({ total: 0, items: [], offset: request.offset, limit: request.limit, sort: request.sort });
        }
        const data = result[0];
        const totalCount = ArrayHelper.isEmpty(data.total) ? 0 : data.total[0].count;
        return new PaginationResponse({ total: totalCount, items: data.items, offset: request.offset, limit: request.limit, sort: request.sort });
    }

    async findIdsByPositionName(text: string) {
        const result = await this.positionModel.aggregate([{ $match: { name: { $regex: text, $options: 'i' } } }, { $group: { _id: 1, ids: { $push: '$id' } } }]);
        return result.length > 0 ? result[0].ids : [];
    }

    async getRolesByPostionIds(ids: string[]) {
        const result = await this.positionModel.aggregate([
            { $match: { id: { $in: ids } } },
            {
                $lookup: {
                    from: MONGO_CONST.ROLE_COLLECTION,
                    localField: 'roleId',
                    foreignField: 'id',
                    as: 'role',
                },
            },
            { $unwind: '$role' },
            {
                $group: {
                    _id: null,
                    roles: { $push: '$role' },
                },
            },
        ]);
        return ArrayHelper.isEmpty(result) ? [] : result[0].roles;
    }
}
