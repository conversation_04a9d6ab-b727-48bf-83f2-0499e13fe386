import {
  Directive,
  AfterViewInit,
  Renderer2,
  Input,
  Optional
} from '@angular/core';
import { Dialog } from 'primeng/dialog';
import { ConfirmationService } from 'primeng/api';

/**
 * Unified directive for confirming dialog close actions.
 * Handles mask clicks, close button clicks, and ESC key presses.
 * Usage: [confirmClose]="myConfirmFunction"
 */
@Directive({
  selector: '[confirmClose]',
  providers: [ConfirmationService]
})
export class ConfirmCloseDirective implements AfterViewInit {
  /**
   * Function to call for confirmation before closing.
   * Receives (event, acceptCallback) parameters.
   * If not provided, uses default confirmation dialog.
   */
  @Input('confirmClose')
  confirmCloseFn?: (event: Event | MouseEvent | KeyboardEvent | undefined, accept: () => void) => void;

  constructor(
    @Optional() private dialog: Dialog,
    private renderer: Renderer2,
    private confirmation: ConfirmationService
  ) { }

  ngAfterViewInit() {
    if (!this.dialog) {
      console.warn('confirmClose used outside of a p-dialog');
      return;
    }

    this.setupMaskCloseConfirmation();
    this.setupCloseButtonConfirmation();
    this.setupEscapeKeyConfirmation();
  }

  private setupMaskCloseConfirmation() {
    // Override enableModality to handle mask clicks
    const origEnable = this.dialog.enableModality.bind(this.dialog);
    this.dialog.enableModality = () => {
      origEnable();

      // Remove default mask click listener
      if (this.dialog.maskClickListener) {
        this.dialog.maskClickListener();
      }

      // Attach custom mask click listener
      this.dialog.maskClickListener = this.renderer.listen(
        this.dialog.wrapper,
        'mousedown',
        (event: MouseEvent) => {
          if (
            (this.dialog as any).wrapper.isSameNode(event.target) &&
            this.dialog!.dismissableMask
          ) {
            const accept = () => this.dialog.close(event);
            this.confirmAction(event, accept);
          }
        }
      );
    };

    // If already visible, re-run enableModality
    if (this.dialog.visible) {
      this.dialog.enableModality();
    }
  }

  private setupCloseButtonConfirmation() {
    // Monkey-patch close() to intercept close button clicks
    const origClose = this.dialog.close.bind(this.dialog);
    this.dialog.close = (event: Event) => {
      const accept = () => origClose(event);
      this.confirmAction(event, accept);
    };
  }

  private setupEscapeKeyConfirmation() {
    // Override enableModality to handle ESC key
    const origEnable = this.dialog.enableModality;
    this.dialog.enableModality = () => {
      origEnable.call(this.dialog);

      // Remove default escape listener
      if (this.dialog.documentEscapeListener) {
        this.dialog.documentEscapeListener();
      }

      // Attach custom ESC listener
      this.dialog.documentEscapeListener = this.renderer.listen(
        'document',
        'keydown',
        (event: KeyboardEvent) => {
          if (event.key === 'Escape' && this.dialog.closeOnEscape) {
            const accept = () => this.dialog.close(event);
            this.confirmAction(event, accept);
          }
        }
      );
    };

    if (this.dialog.visible) {
      this.dialog.enableModality();
    }
  }

  private confirmAction(event: Event | MouseEvent | KeyboardEvent | undefined, accept: () => void) {
    if (this.confirmCloseFn) {
      this.confirmCloseFn(event, accept);
    } else {
      this.confirmation.confirm({
        message: 'Bạn có chắc muốn thoát?',
        accept
      });
    }
  }
}

