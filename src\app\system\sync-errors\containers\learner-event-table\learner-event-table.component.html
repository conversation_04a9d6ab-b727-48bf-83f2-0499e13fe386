<p-table [value]="data" [scrollable]="true" scrollHeight="400px" responsiveLayout="scroll">
    <ng-template #header>
        <tr>
            <th pSortableColumn="eventDate">
                <div class="flex items-center gap-1">
                    eventDate
                    <p-sortIcon field="eventDate"/>
                </div>
            </th>
            <th pSortableColumn="learnerCode">
                <div class="flex items-center gap-1">
                    learnerCode
                    <p-sortIcon field="learnerCode"/>
                </div>
            </th>
            <th pSortableColumn="eventStr">
                <div class="flex items-center gap-1">
                    eventStr
                    <p-sortIcon field="eventStr"/>
                </div>
            </th>
            <th pSortableColumn="info1">
                <div class="flex items-center gap-1">
                    info1
                    <p-sortIcon field="info1"/>
                </div>
            </th>
            <th pSortableColumn="info2">
                <div class="flex items-center gap-1">
                    info2
                    <p-sortIcon field="info2"/>
                </div>
            </th>
            <th style="min-width: 20rem" alignFrozen="right" pSortableColumn="error" pFrozenColumn [frozen]="true">
                error
                <p-sortIcon field="error"/>
            </th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body" let-childItem>
        <tr>
            <td>{{ childItem.eventDate }}</td>
            <td>{{ childItem.learnerCode }}</td>
            <td>{{ childItem.eventStr }}</td>
            <td>{{ childItem.info1 }}</td>
            <td>{{ childItem.info2 }}</td>
            <td style="min-width: 20rem" alignFrozen="right" pFrozenColumn [frozen]="true">{{ childItem.error }}</td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="numberOfCols">Không có dữ liệu</td>
        </tr>
    </ng-template>
</p-table>