import { CommonModule } from '@angular/common';
import { Component, Renderer2, OnInit, OnDestroy } from '@angular/core';
import { NavigationEnd, Router, RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { MenuModule } from 'primeng/menu';
import { StyleClassModule } from 'primeng/styleclass';
import { LayoutService } from '../service/layout.service';
import { AvatarModule } from 'primeng/avatar';
import { AuthService } from '@shared/services';
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { ChangePasswordComponent } from '@/app/auth/change-password/change-password.component';
import { AppFloatingConfigurator } from '../app-floatingconfigurator/app-floatingconfigurator';
import { ButtonModule } from 'primeng/button';
import { Breadcrumb } from 'primeng/breadcrumb';
import { filter, Subscription, tap } from 'rxjs';
import { RippleModule } from 'primeng/ripple';
@Component({
    selector: 'app-topbar',
    standalone: true,
    imports: [RouterModule,
        CommonModule,
        StyleClassModule,
        MenuModule,
        AvatarModule,
        DynamicDialogModule,
        ButtonModule,
        AppFloatingConfigurator,
        Breadcrumb,
        RippleModule],
    providers: [DialogService],
    templateUrl: './app-topbar.html',
    styleUrl: './app-topbar.scss'
})
export class AppTopbar implements OnInit, OnDestroy {
    userInfo: { username: string; fullname: string } = { username: '', fullname: '' };
    items!: MenuItem[];
    breadcrumbItems: MenuItem[] = [];
    home: MenuItem = { icon: 'pi pi-home', label: 'Trang chủ', routerLink: '/' };
    shortName: string = 'A';
    private routerSubscription: Subscription | undefined;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
        private dialogService: DialogService,
        private renderer: Renderer2,
        private router: Router
    ) { }

    ngOnInit() {
        this.loadUserInfo();
        this.items = [
            {
                label: 'Đổi mật khẩu',
                icon: 'pi pi-lock',
                command: () => {
                    this.changePassword();
                }
            },
            {
                label: 'Đăng xuất',
                icon: 'pi pi-power-off',
                command: () => {
                    this.logout();
                }
            }
        ];

        this.createBreadcrumb(this.router.url);
        // Subscribe to route changes to update breadcrumb
        this.routerSubscription = this.router.events
            .pipe(filter(event => event instanceof NavigationEnd))
            .subscribe(() => {
                setTimeout(() => {
                    this.createBreadcrumb(this.router.url);
                }, 0);
            });
    }

    ngOnDestroy() {
        if (this.routerSubscription) {
            this.routerSubscription.unsubscribe();
        }
    }

    createBreadcrumb(url: string) {
        this.breadcrumbItems = [];

        if (!url || url === '/') {
            return;
        }

        // Split the URL into segments and build breadcrumb items
        const segments = url.split('/').filter(segment => segment);
        let currentPath = '';

        segments.forEach((segment, index) => {
            currentPath += `/${segment}`;

            // Get a user-friendly label based on the segment
            const label = this.getReadableLabel(segment);

            // For the last segment, don't add routerLink (current page)
            if (index === segments.length - 1) {
                this.breadcrumbItems.push({ label, url: 'javascript:void(0)' });
            } else {
                this.breadcrumbItems.push({ label, routerLink: currentPath });
            }
        });
    }

    getReadableLabel(segment: string): string {
        // Map of common URL segments to user-friendly names
        const labelMap: Record<string, string> = {
            'admin': 'Quản trị',
            'learner': 'Học viên',
            'learners': 'Học viên',
            'system': 'Hệ thống',
            'histories': 'Quá trình học',
            'about': 'Thông tin liên hệ',
            'centers': 'Danh sách trung tâm',
            'sync-errors': 'Log lỗi',
            'learner-lookup': 'Tra cứu học viên',
            'settings': 'Cài đặt',
            'hr': 'Nhân sự',
            'employees': 'Nhân viên',
            'positions': 'Vị trí',
            'departments': 'Phòng ban',
            'list': 'Danh sách',
        };

        // Return mapped label if available, otherwise format the segment
        if (labelMap[segment]) {
            return labelMap[segment];
        }

        // Format the label (capitalize first letter, replace hyphens with spaces)
        let label = segment.charAt(0).toUpperCase() + segment.slice(1);
        return label.replace(/-/g, ' ');
    }

    loadUserInfo() {
        this.authService
            .getUserInfo()
            .pipe(
                tap((response) => {
                    this.userInfo = response.data;
                    this.shortName = this.userInfo?.fullname.charAt(0).toUpperCase();
                })
            )
            .subscribe();
    }

    changePassword() {
        const header = 'Thay đổi mật khẩu';
        const ref = this.dialogService.open(ChangePasswordComponent, {
            header,
            width: '400px',
            height: 'auto',
            closable: true,
            modal: true,
            dismissableMask: true,
            closeOnEscape: true
        });
        setTimeout(() => {
            this.renderer.removeClass(document.body, 'p-overflow-hidden');
        }, 0);

        ref.onClose.subscribe((data) => {
            if (data) {
                ref.destroy();
            }
        });
    }

    logout() {
        this.authService.logout();
    }



}
