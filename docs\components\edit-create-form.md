# Edit Create Form Component Usage

## Basic Usage

Replace your existing step components with the generic edit-create-form:

### Before (Employee Info Step):
```typescript
<app-employee-info-step
  [editMode]="editMode"
  [employeeData]="employeeData"
  [isDisabled]="isDisabled"
  (formChange)="onEmployeeFormChange($event)">
</app-employee-info-step>
```

### After (Generic Form):
```typescript
<edit-create-form
  [editMode]="editMode"
  [initialData]="employeeData"
  [isDisabled]="isDisabled"
  [formConfig]="employeeInfoConfig"
  (formChange)="onEmployeeFormChange($event)">
</edit-create-form>
```

## Component Setup

```typescript
import { Component } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { EditCreateFormComponent, FormConfig } from '@shared/components/edit-create-form';
import { EMPLOYEE_INFO_FORM_CONFIG } from './employee-info-step.config';
import { EMPLOYEE_POSITION_FORM_CONFIG } from './employee-position-step.config';
import { EMPLOYEE_PROBATION_FORM_CONFIG } from './employee-probation-step.config';

@Component({
  selector: 'app-employee-creation',
  standalone: true,
  imports: [EditCreateFormComponent],
  template: `
    <!-- Step 1: Employee Info -->
    <edit-create-form
      [editMode]="editMode"
      [initialData]="employeeData"
      [isDisabled]="isDisabled"
      [formConfig]="employeeInfoConfig"
      (formChange)="onEmployeeFormChange($event)">
    </edit-create-form>

    <!-- Step 2: Employee Position -->
    <edit-create-form
      [editMode]="editMode"
      [initialData]="employeePositionData"
      [isDisabled]="isDisabled"
      [formConfig]="employeePositionConfig"
      (formChange)="onPositionFormChange($event)">
    </edit-create-form>

    <!-- Step 3: Employee Probation -->
    <edit-create-form
      [editMode]="editMode"
      [initialData]="probationData"
      [isDisabled]="isDisabled"
      [formConfig]="employeeProbationConfig"
      (formChange)="onProbationFormChange($event)">
    </edit-create-form>
  `
})
export class EmployeeCreationComponent {
  editMode = false;
  isDisabled = false;
  
  // Data objects
  employeeData: any = null;
  employeePositionData: any = null;
  probationData: any = null;

  // Form configurations
  employeeInfoConfig = EMPLOYEE_INFO_FORM_CONFIG;
  employeePositionConfig = EMPLOYEE_POSITION_FORM_CONFIG;
  employeeProbationConfig = EMPLOYEE_PROBATION_FORM_CONFIG;

  // Form instances
  employeeForm: FormGroup | null = null;
  positionForm: FormGroup | null = null;
  probationForm: FormGroup | null = null;

  onEmployeeFormChange(form: FormGroup) {
    this.employeeForm = form;
  }

  onPositionFormChange(form: FormGroup) {
    this.positionForm = form;
  }

  onProbationFormChange(form: FormGroup) {
    this.probationForm = form;
  }
}
```

## Configuration Examples

### Employee Info Config
```typescript
export const EMPLOYEE_INFO_FORM_CONFIG: FormConfig = {
  fields: [
    {
      key: 'code',
      label: 'Mã nhân viên',
      type: 'text',
      required: true,
      width: 'half',
      disabled: (editMode: boolean) => editMode,
      validators: [Validators.pattern(/^[A-Z0-9-]+$/)],
      customErrorMessage: 'Mã nhân viên là bắt buộc và chỉ chứa chữ hoa, số, dấu gạch ngang'
    },
    {
      key: 'isActive',
      label: 'Kích hoạt',
      type: 'toggleSwitch',
      width: 'half',
      placeholder: 'Kích hoạt tài khoản',
      tooltip: 'Bật/tắt trạng thái tài khoản'
    },
    {
      key: 'code',
      label: 'Mã nhân viên',
      type: 'text',
      required: true,
      width: 'half',
      disabled: (editMode: boolean, formValue: any) => editMode && formValue.code,
      tooltip: (editMode: boolean, formValue: any) =>
        editMode && formValue.code ? 'Không thể sửa mã nhân viên đã có' : ''
    },
    // ... more fields
  ]
};
```

### Employee Position Config (with dependencies)
```typescript
export const EMPLOYEE_POSITION_FORM_CONFIG: FormConfig = {
  fields: [
    {
      key: 'rootDepartmentId',
      label: 'Phòng ban gốc',
      type: 'select',
      required: true,
      config: {
        apiEndpoint: '/departments/root',
        optionLabel: 'name',
        optionValue: 'id'
      }
    },
    {
      key: 'positionId',
      label: 'Vị trí',
      type: 'select',
      required: true,
      dependsOn: 'rootDepartmentId', // This field depends on rootDepartmentId
      disabled: (editMode: boolean, formValue: any) => !formValue?.rootDepartmentId,
      config: {
        apiEndpoint: '/positions',
        dependencyParam: 'departmentId'
      }
    }
  ]
};
```

### Divider Configuration Examples
```typescript
export const FORM_WITH_DIVIDERS_CONFIG: FormConfig = {
  fields: [
    // Basic info section
    {
      key: 'code',
      label: 'Mã nhân viên',
      type: 'text',
      required: true,
      width: 'half'
    },
    {
      key: 'fullname',
      label: 'Họ tên',
      type: 'text',
      required: true,
      width: 'half',
      showDividerAfter: true // Show divider after this field to separate sections
    },

    // Contact info section
    {
      key: 'email',
      label: 'Email',
      type: 'text',
      width: 'half'
    },
    {
      key: 'phone',
      label: 'Điện thoại',
      type: 'text',
      width: 'half',
      showDividerAfter: true // Another section separator
    },

    // Additional info section
    {
      key: 'notes',
      label: 'Ghi chú',
      type: 'textarea',
      width: 'full'
      // No divider after the last field
    }
  ]
};
```



## Field Types Supported

- `text` - Text input with optional directives (phoneFormat, etc.)
- `textarea` - Multi-line text input
- `select` - Dropdown using generic-p-select with API support
- `treeselect` - Tree dropdown using generic-p-treeselect
- `datepicker` - Date picker with default formatting
- `checkbox` - Boolean checkbox input
- `toggleSwitch` - Boolean toggle switch input

## Field Configuration Options

- `key` - Form control name
- `label` - Display label
- `type` - Field type
- `required` - Whether field is required
- `validators` - Additional validators
- `width` - 'full' or 'half' width
- `disabled` - Boolean or function to determine disabled state
- `config` - Configuration for select/treeselect components
- `placeholder` - Placeholder text
- `tooltip` - Tooltip text (string) or conditional tooltip function `(editMode: boolean, formValue: any) => string`
- `dependsOn` - Field key this field depends on
- `directives` - Array of directive names to apply
- `customErrorMessage` - Custom validation error message
- `showDividerAfter` - Whether to show a divider after this specific field
- `labelPosition` - 'default' | 'inline' (default: 'default') - Position of the label: 'default' shows label at the top, 'inline' shows label next to the field value in bold

## Conditional Tooltips

Tooltips can be conditional based on the form state. Use a function instead of a string:

```typescript
{
  key: 'code',
  label: 'Mã nhân viên',
  type: 'text',
  disabled: (editMode: boolean, formValue: any) => editMode && formValue.code,
  tooltip: (editMode: boolean, formValue: any) =>
    editMode && formValue.code ? 'Không thể sửa mã nhân viên đã có' : ''
}
```

The tooltip function receives:
- `editMode` - Whether the form is in edit mode
- `formValue` - Current form values (including disabled fields)

Return an empty string to hide the tooltip.
