import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { SchemaConfig } from '../mongodb.constants';
import { Audit } from './audit.schema';

export interface IProfileDocument {
    id?: string;
    fullname: string;
    nickname?: string;
    birthday?: Date;
    identification?: string;
    phone?: string;
    email?: string;
    textSearch?: string;
}

@Schema({ versionKey: false, timestamps: true, toJSON: SchemaConfig.ToJSON, toObject: SchemaConfig.ToObject })
export class ProfileDocument extends Audit implements IProfileDocument {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ index: true, required: true })
    fullname: string;
    @Prop({ index: true, default: null })
    nickname: string;
    @Prop({ index: true, default: null })
    birthday: Date;
    @Prop({ index: true, default: null })
    identification: string;
    @Prop({ index: true, default: null })
    phone: string;
    @Prop({ index: true, default: null })
    email: string;
    @Prop({ index: true, default: null })
    textSearch: string;
}

export const ProfileSchema = SchemaFactory.createForClass(ProfileDocument);
ProfileSchema.index({ textSearch: 'text' }, { default_language: 'none' });
