<p-toolbar styleClass="mb-4 !border-none">
    <ng-template #start>
        <h3 class="text-xl font-semibold">Quản lý vị trí</h3>
    </ng-template>
    <ng-template #end>
        <p-button pRipple
                  styleClass="h-10"
                  icon="pi pi-plus"
                  label="Thêm vị trí"
                  (onClick)="openNew()" />
    </ng-template>
</p-toolbar>

<p-table [value]="positions"
         [lazy]="true"
         [paginator]="true"
         [rows]="itemPerPage"
         [totalRecords]="totalRecords"
         [rowsPerPageOptions]="itemPerPageOptions"
         [showCurrentPageReport]="true"
         currentPageReportTemplate="{first} - {last} of {totalRecords} vị trí"
         (onLazyLoad)="onLazyLoad($event)">
    <ng-template pTemplate="header">
        <tr>
            <th pSortableColumn="code">Mã vị trí <p-sortIcon field="code" /></th>
            <th pSortableColumn="name">Tên vị trí <p-sortIcon field="name" /></th>
            <th pSortableColumn="departmentName">Phòng ban <p-sortIcon field="departmentName" /></th>
            <th pSortableColumn="roleName">Vai trò <p-sortIcon field="roleName" /></th>
            <th>Hành động</th>
        </tr>
    </ng-template>
    <ng-template pTemplate="body"
                 let-position>
        <tr>
            <td>{{ position.code }}</td>
            <td>{{ position.name }}</td>
            <td>{{ position.department.name }}</td>
            <td>{{ position.role.name }}</td>
            <td>
                <div class="flex gap-2">
                    <button pButton
                            icon="pi pi-pencil"
                            class="p-button-text p-button-sm"
                            (click)="editPosition(position)"></button>
                    <button pButton
                            icon="pi pi-trash"
                            class="p-button-text p-button-danger p-button-sm"
                            (click)="deletePosition(position)"></button>
                </div>
            </td>
        </tr>
    </ng-template>
    <ng-template pTemplate="emptymessage">
        <tr>
            <td colspan="5"
                class="text-center p-4">Chưa có vị trí nào.</td>
        </tr>
    </ng-template>
</p-table>

<!-- Reusable Edit Create Dialog -->
<edit-create-dialog #editCreateDialog
                    [(visible)]="positionDialog"
                    [editMode]="editMode"
                    [dialogConfig]="dialogConfig"
                    [initialData]="selectedPosition">
</edit-create-dialog>

<p-confirmDialog [style]="{width: '450px'}" dismissableMask="true"/>