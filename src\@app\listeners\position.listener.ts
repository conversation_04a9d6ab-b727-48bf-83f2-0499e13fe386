import { EVENTS } from '@app/constants';
import { Array<PERSON><PERSON>per, DateHelper } from '@app/shared/helpers';
import { EmployeePositionRepository, PositionRepository } from '@database/mongodb/repositories';
import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2, OnEvent } from '@nestjs/event-emitter';

@Injectable()
export class PositionListener {
    private logger = new Logger(PositionListener.name);
    constructor(
        private employeePositionRepository: EmployeePositionRepository,
        private positionRepository: PositionRepository,
        private eventEmitter: EventEmitter2,
    ) {}

    @OnEvent(EVENTS.ROLE_PERMISSION_CHANGED)
    async handleUpdatePermissionEmp(data: { roleId: string }) {
        this.logger.log(`Handle UpdatePermissionEmp - ${JSON.stringify(data)}`);
        const { roleId } = data;
        const positions = await this.positionRepository.findAll({ roleId, deletedAt: null });
        if (ArrayHelper.isEmpty(positions)) return;
        const today = DateHelper.getStartOfDate();
        const availableEmployees = await this.employeePositionRepository.findAll({
            positionId: { $in: positions.map(i => i.id) },
            fromDate: { $lte: today },
            $or: [{ toDate: null }, { toDate: { $gte: today } }],
        });
        if (ArrayHelper.isEmpty(availableEmployees)) return;
        availableEmployees.forEach(i => {
            this.eventEmitter.emit(EVENTS.EMPLOYEE_POSITION_ROLE_CHANGED, { employeeId: i.employeeId });
        });
    }
}
