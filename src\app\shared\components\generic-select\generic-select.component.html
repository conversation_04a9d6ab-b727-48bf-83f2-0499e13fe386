<p-select #genericSelect
          [id]="id"
          [options]="options"
          [placeholder]="config.placeholder || placeholder"
          optionLabel="label"
          optionValue="value"
          [disabled]="disabled"
          [filter]="filter"
          filterBy="label"
          (onFilter)="onFilter($event)"
          [virtualScroll]="virtualScroll && !isStaticMode"
          [lazy]="lazy && !isStaticMode"
          [virtualScrollItemSize]="virtualScrollItemSize"
          [virtualScrollOptions]="virtualScrollOptions"
          [loading]="loading"
          [appendTo]="appendTo"
          [styleClass]="styleClass"
          [showClear]="showClear"
          [ngModel]="value"
          (ngModelChange)="onValueChange($event)">

    <!-- Custom templates -->
    <ng-template let-item pTemplate="selectedItem">
        <ng-container *ngIf="selectedItemTemplate; else builtInSelectedItem">
            <ng-container *ngTemplateOutlet="selectedItemTemplate; context: { $implicit: item }"></ng-container>
        </ng-container>
        <ng-template #builtInSelectedItem>
            <ng-container [ngSwitch]="config.templateName">
                <!-- Employee template -->
                <div *ngSwitchCase="'employee'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.fullname }} ({{ item.nickname }})</div>
                </div>
                <!-- Position template -->
                <div *ngSwitchCase="'position'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.departmentName" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Department template -->
                <div *ngSwitchCase="'department'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Role template -->
                <div *ngSwitchCase="'role'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Course template -->
                <div *ngSwitchCase="'course'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.type" severity="info" />
                    <div>{{ item.name }}</div>
                    <p-tag [value]="item.level" severity="secondary" />
                    <div class="ml-auto italic">{{ item.teacher }}</div>
                </div>
                <!-- Employee Position template -->
                <div *ngSwitchCase="'employeePosition'" class="flex items-center gap-2 w-full">
                    <div>{{ item.positionName || 'N/A' }}</div>
                    <generic-p-tag [value]="item.status || 'N/A'" type="employeePositionStatus" styleClass="w-fit" />
                    <div class="text-sm italic">{{ ((item.fromDate | date:'dd/MM/yyyy') || '') + ' - ' + ((item.toDate | date:'dd/MM/yyyy') || '')}}</div>
                </div>
                <!-- Default template -->
                <div *ngSwitchDefault>{{ item.label }}</div>
            </ng-container>
        </ng-template>
    </ng-template>

    <ng-template let-item #item>
        <ng-container *ngIf="itemTemplate; else builtInItem">
            <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item }"></ng-container>
        </ng-container>
        <ng-template #builtInItem>
            <ng-container [ngSwitch]="config.templateName">
                <!-- Employee template -->
                <div *ngSwitchCase="'employee'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.fullname }} {{ item.nickname ? ' (' + item.nickname + ')' : '' }}</div>
                </div>
                <!-- Position template -->
                <div *ngSwitchCase="'position'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.departmentName" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Department template -->
                <div *ngSwitchCase="'department'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Role template -->
                <div *ngSwitchCase="'role'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.code" severity="secondary" />
                    <div>{{ item.name }}</div>
                </div>
                <!-- Course template -->
                <div *ngSwitchCase="'course'" class="flex items-center gap-2 w-full">
                    <p-tag [value]="item.type" severity="info" />
                    <div>{{ item.name }}</div>
                    <p-tag [value]="item.level" severity="secondary" />
                    <div class="ml-auto italic">{{ item.teacher }}</div>
                </div>
                <!-- Employee Position template -->
                <div *ngSwitchCase="'employeePosition'" class="flex items-center gap-2 w-full">
                    <div>{{ item.positionName || 'N/A' }}</div>
                    <generic-p-tag [value]="item.status || 'N/A'" type="employeePositionStatus" styleClass="w-fit" />
                    <div class="text-sm italic">{{ ((item.fromDate | date:'dd/MM/yyyy') || '') + ' - ' + ((item.toDate | date:'dd/MM/yyyy') || '')}}</div>
                </div>
                <!-- Default template -->
                <div *ngSwitchDefault>{{ item.label }}</div>
            </ng-container>
        </ng-template>
    </ng-template>

    <ng-template #empty>
        <ng-container *ngIf="loading; else noResults">
            <span class="italic">Loading — please wait…</span>
        </ng-container>
        <ng-template #noResults>
            <span class="italic">No results found.</span>
        </ng-template>
    </ng-template>
</p-select>