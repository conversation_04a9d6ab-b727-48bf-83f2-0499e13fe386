import { SchemaConfig } from '@database/mongodb';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { v4 } from 'uuid';
import { Audit } from './audit.schema';

export interface ILearnerDocument {
    id?: string;
    code: string;
    ec?: string;
}

@Schema({ versionKey: false, timestamps: true, toJSON: SchemaConfig.ToJSON, toObject: SchemaConfig.ToObject })
export class LearnerDocument extends Audit implements ILearnerDocument {
    @Prop({ unique: true, index: true, default: v4 })
    id: string;
    @Prop({ unique: true, index: true, required: true })
    code: string;
    @Prop({ index: true })
    ec: string;
}

export const LearnerSchema = SchemaFactory.createForClass(LearnerDocument);
