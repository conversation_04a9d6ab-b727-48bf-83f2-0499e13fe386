version: '3.8'

services:
    portal-fe:
        build:
            context: {{ app_dir }}
            dockerfile: Dockerfile
{% if branch == 'dev' %}
            args:
                - BUILD_ENV=dev
        image: portal-fe-dev
        container_name: 'portal-fe-dev'
        ports:
            - "4201:80"
{% else %}
            args:
                - BUILD_ENV=prod
        image: portal-fe
        container_name: 'portal-fe'
        ports:
            - "4200:80"
{% endif %}
        restart: unless-stopped
        networks:
            - ws-shared-network
networks:
    ws-shared-network:
        external: true