version: '3.8'

services:
    portal-be:
        build:
            context: {{ app_dir }}
            dockerfile: Dockerfile
        
{% if branch == 'dev' %}
        image: portal-be-dev
        container_name: 'portal-be-dev'
        env_file:
            - ./.env.dev
        ports:
            - "8801:8800"
{% else %}
        image: portal-be
        container_name: 'portal-be'
        env_file:
            - ./.env
        ports:
            - "8800:8800"
{% endif %}
        restart: unless-stopped
        networks:
            - ws-shared-network
        healthcheck:
            test: ["CMD", "curl", "-f", "http://localhost:8800/health"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
    mongodb:
        image: mongo:4.0
{% if branch == 'dev' %}
        container_name: mongodb-dev
        ports:
            - "47018:27017"
        volumes:
            - ~/mongodb_dev_data:/data/db
{% else %}
        container_name: mongodb
        ports:
            - "47017:27017"
        volumes:
            - ~/mongodb_data:/data/db
{% endif %}
        restart: unless-stopped
        environment:
            MONGO_INITDB_ROOT_USERNAME: admin
            MONGO_INITDB_ROOT_PASSWORD: kOGHJHqRSR8nDt0
        networks:
            - ws-shared-network
        healthcheck:
            test: ["CMD", "mongo", "--eval", "db.adminCommand('ping')"]
            interval: 30s  # Kiểm tra mỗi 30 giây
            timeout: 10s   # Thời gian chờ phản hồi
            retries: 3     # Thử lại tối đa 3 lần nếu thất bại
            start_period: 5s  # Thời gian chờ trước khi bắt đầu kiểm tra
networks:
    ws-shared-network:
        driver: bridge
        name: ws-shared-network