import { TagType } from '@shared/components/generic-tag/generic-tag.component';

export interface ColumnConfig {
    field: string;
    header: string;
    width?: string;
    sortable?: boolean;
    type?: 'text' | 'date' | 'tag' | 'custom';
    tagType?: TagType; // for generic-p-tag
    dateFormat?: string;
}

// New action interface similar to edit-create-dialog
export interface TableAction<T = any> {
    label?: string;
    icon?: string;
    tooltip?: string;
    severity?: 'primary' | 'secondary' | 'success' | 'info' | 'warn' | 'danger';
    styleClass?: string;
    disabled?: (item: T) => boolean;
    onClick: (item: T, event?: Event) => void | Promise<void>;
    useDefaultStyle?: 'add' | 'edit' | 'delete';
    confirmMessage?: string; // For delete confirmation
    location: 'row' | 'toolbar'; // Extensible location system
}

export interface SelectionConfig<T = any> {
    mode: 'single' | 'multiple' | 'none';
    metaKeySelection?: boolean;
    dataKey?: string;
    onSelect?: (item: T, originalEvent: Event) => void;
    onUnselect?: (item: T, originalEvent: Event) => void;
}

export interface TableFeatures {
    virtualScroll?: boolean;
    pagination?: boolean;
    toolbar?: boolean;
    selection?: SelectionConfig;
    loading?: boolean;
}

export interface TableConfig<T = any> {
    // Data source configuration
    service: any;
    method: string;

    // Table metadata
    title: string;
    entityName: string; // for empty message, pagination text

    // Column definitions
    columns: ColumnConfig[];

    // Features configuration
    features?: TableFeatures;

    // Action buttons configuration - embedded functions like edit-create-dialog
    actions?: TableAction<T>[];

    // Additional service parameters
    additionalParams?: any;

    // Custom query builder for complex filtering
    queryBuilder?: (params: any) => any;

    // Response transformer
    transformResponse?: (response: any) => any;

    // Virtual scroll configuration
    virtualScrollItemSize?: number;
    virtualScrollOptions?: any;

    // Pagination configuration
    itemsPerPage?: number;
    itemsPerPageOptions?: number[];
}

export interface TableDataLoadEvent {
    items: any[];
    totalRecords: number;
    loading: boolean;
}

export interface TableActionEvent<T = any> {
    action: 'add' | 'edit' | 'delete' | 'custom';
    item?: T;
    customAction?: string;
    event?: Event;
}

export interface TableLazyLoadParams {
    first: number;
    rows: number;
    sortField?: string | string[] | null | undefined;
    sortOrder?: number | undefined | null;
    filters?: any;
}

// Type for supported table entity types
export type TableEntityType = 'probation' | 'employeePosition' | 'employee';

export type TableConfigRegistry = Record<TableEntityType, () => Promise<TableConfigFactory>>;

// Factory function type for form configs
export type TableConfigFactory = () => TableConfig;

// Function mapping interface
export interface TableFunctionMap {
    OPEN_NEW?: () => void;
    EDIT?: (item: any) => void;
    DELETE?: (item: any) => void;
    ON_SELECT?: (item: any, event: Event) => void;
    ON_UNSELECT?: (item: any, event: Event) => void;
    RETIRE?: (item: any) => void;
    [key: string]: any; // Allow custom function mappings
}

// Placeholder constants for function mapping
export const PLACEHOLDER_FUNCTIONS = {
    OPEN_NEW: '__PLACEHOLDER_OPEN_NEW__' as any,
    EDIT: '__PLACEHOLDER_EDIT__' as any,
    DELETE: '__PLACEHOLDER_DELETE__' as any,
    ON_SELECT: '__PLACEHOLDER_ON_SELECT__' as any,
    ON_UNSELECT: '__PLACEHOLDER_ON_UNSELECT__' as any,
    RETIRE: '__PLACEHOLDER_RETIRE__' as any
};