<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Frontend Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .nav-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .nav-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .nav-card h3 {
            color: #667eea;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .nav-card ul {
            list-style: none;
        }

        .nav-card li {
            margin-bottom: 0.5rem;
        }

        .nav-card a {
            color: #555;
            text-decoration: none;
            padding: 0.3rem 0;
            display: block;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .nav-card a:hover {
            background-color: #f0f2ff;
            color: #667eea;
            padding-left: 0.5rem;
        }

        .quick-start {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .quick-start h2 {
            color: #333;
            margin-bottom: 1rem;
        }

        .quick-links {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .quick-link {
            background: #667eea;
            color: white;
            padding: 0.7rem 1.2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .quick-link:hover {
            background: #5a6fd8;
        }

        .footer {
            text-align: center;
            padding: 2rem;
            color: #666;
            border-top: 1px solid #eee;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .nav-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-links {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 Portal Frontend Documentation</h1>
            <p>Comprehensive guide to components, services, and best practices</p>
        </div>

        <div class="quick-start">
            <h2>🚀 Quick Start</h2>
            <p>New to the project? Start here:</p>
            <div class="quick-links">
                <a href="./README.md" class="quick-link">📖 Main Documentation</a>
                <a href="./navigation.md" class="quick-link">🧭 Navigation Guide</a>
                <a href="./index.md" class="quick-link">📋 Complete Index</a>
                <a href="./architecture/project-overview.md" class="quick-link">🏗️ Project Overview</a>
            </div>
        </div>

        <div class="nav-grid">
            <div class="nav-card">
                <h3>🧩 Components</h3>
                <ul>
                    <li><a href="./components/README.md">📋 Components Overview</a></li>
                    <li><a href="./components/edit-create-dialog.md">💬 Edit-Create Dialog</a></li>
                    <li><a href="./components/generic-stepper.md">📊 Generic Stepper</a></li>
                    <li><a href="./components/generic-p-table.md">📋 Generic P-Table</a></li>
                    <li><a href="./components/generic-popover.md">💭 Generic Popover</a></li>
                    <li><a href="./components/edit-create-form.md">📝 Edit-Create Form</a></li>
                </ul>
            </div>

            <div class="nav-card">
                <h3>🔧 Services</h3>
                <ul>
                    <li><a href="./services/README.md">📋 Services Overview</a></li>
                    <li><a href="./services/edit-create-form-config.md">⚙️ Form Config Service</a></li>
                    <li><a href="./services/popover-config.md">⚙️ Popover Config Service</a></li>
                </ul>
            </div>

            <div class="nav-card">
                <h3>📚 Examples & Patterns</h3>
                <ul>
                    <li><a href="./examples/common-patterns.md">🎯 Common Patterns</a></li>
                    <li><a href="./examples/configuration-examples.md">⚙️ Configuration Examples</a></li>
                    <li><a href="./examples/component-integration.md">🔗 Component Integration</a></li>
                    <li><a href="./examples/best-practices.md">✨ Best Practices</a></li>
                </ul>
            </div>

            <div class="nav-card">
                <h3>🏗️ Architecture</h3>
                <ul>
                    <li><a href="./architecture/project-overview.md">📖 Project Overview</a></li>
                    <li><a href="./architecture/getting-started.md">🚀 Getting Started</a></li>
                    <li><a href="./architecture/project-structure.md">📁 Project Structure</a></li>
                    <li><a href="./architecture/development-guidelines.md">📋 Development Guidelines</a></li>
                </ul>
            </div>

            <div class="nav-card">
                <h3>🧪 Testing</h3>
                <ul>
                    <li><a href="./testing/testing-guidelines.md">📋 Testing Guidelines</a></li>
                    <li><a href="./testing/unit-testing.md">🔬 Unit Testing</a></li>
                    <li><a href="./testing/integration-testing.md">🔗 Integration Testing</a></li>
                </ul>
            </div>

            <div class="nav-card">
                <h3>🚀 Deployment</h3>
                <ul>
                    <li><a href="./deployment/build-deployment.md">🏗️ Build & Deployment</a></li>
                    <li><a href="./deployment/troubleshooting.md">🔧 Troubleshooting</a></li>
                    <li><a href="./deployment/performance.md">⚡ Performance</a></li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>📅 Last Updated: <span id="lastUpdated"></span></p>
            <p>💡 Tip: Use Ctrl+F to search for specific topics</p>
        </div>
    </div>

    <script>
        // Set last updated date
        document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString();
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchTerm = prompt('Search documentation:');
                if (searchTerm) {
                    // Simple search implementation
                    const links = document.querySelectorAll('a');
                    for (let link of links) {
                        if (link.textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                            link.style.backgroundColor = '#ffeb3b';
                            link.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            break;
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>
