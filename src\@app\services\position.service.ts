import { JwtPayloadDTO, PositionDTO, SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { DepartmentRepository, PositionRepository, RoleRepository } from '@database/mongodb/repositories';
import { EntityExistedError, NotFoundError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class PositionService {
    private readonly logger = new Logger(PositionService.name);

    constructor(
        private positionRepository: PositionRepository,
        private departmentRepository: DepartmentRepository,
        private roleRepository: RoleRepository,
    ) {}

    async findWithPagination(request: PaginationRequest) {
        request.query['deletedAt'] = null;
        return await this.positionRepository.findWithPaginationAggregated(request);
    }

    async getPositionOptions(request: PaginationRequest, body: SearchOptionsDTO) {
        return await this.positionRepository.getOptions(request, body);
    }

    async createPosition(dto: PositionDTO, logged: JwtPayloadDTO) {
        const [positionEntity, departmentEntity, roleEntity] = await Promise.all([
            this.positionRepository.findOne({ code: dto.code, deletedAt: null }),
            this.departmentRepository.findOne({ id: dto.departmentId, deletedAt: null }),
            this.roleRepository.findOne({ id: dto.roleId, deletedAt: null }),
        ]);
        if (positionEntity) {
            throw new EntityExistedError('Position code exist');
        }
        if (!departmentEntity || !roleEntity) {
            throw new NotFoundError('department or role not found');
        }
        await this.positionRepository.create({
            code: dto.code,
            name: dto.name,
            departmentId: departmentEntity.id,
            roleId: roleEntity.id,
            createdBy: logged.username,
            updatedBy: logged.username,
        });
    }

    async updatePosition(dto: PositionDTO, logged: JwtPayloadDTO) {
        const positionEntity = await this.positionRepository.findOne({ code: dto.code, deletedAt: null });
        if (!positionEntity) {
            throw new NotFoundError('Position code not found');
        }
        if (dto.departmentId != positionEntity.departmentId) {
            const departmentEntity = await this.departmentRepository.findOne({ id: dto.departmentId, deletedAt: null });
            if (!departmentEntity) {
                throw new NotFoundError('department not found');
            }
        }
        if (dto.roleId != positionEntity.roleId) {
            const roleEntity = await this.roleRepository.findOne({ id: dto.roleId, deletedAt: null });
            if (!roleEntity) {
                throw new NotFoundError('role not found');
            }
        }
        await this.positionRepository.updateOne(
            { code: dto.code },
            {
                name: dto.name,
                departmentId: dto.departmentId,
                roleId: dto.roleId,
                updatedBy: logged.username,
            },
        );
    }

    // Implement After
    async deletePosition(code: string, logged: JwtPayloadDTO) {}
}
