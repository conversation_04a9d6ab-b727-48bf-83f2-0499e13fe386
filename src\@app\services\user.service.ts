import { UserType } from '@app/enums';
import { ChangePasswordDTO, JwtPayloadDTO } from '@app/models/dto';
import { CreateUserForLearnerDTO } from '@app/models/dto/user.dto';
import { Array<PERSON>elper, CryptopHelper, StringHelper } from '@app/shared/helpers';
import { UserRepository } from '@database/mongodb/repositories';
import { IUserDocument, UserDocument } from '@database/mongodb/schemas';
import { NotFoundError, UnauthorizedError } from '@errors/error-base';
import { Injectable, Logger } from '@nestjs/common';
import { v4 } from 'uuid';

@Injectable()
export class UserService {
    private readonly logger = new Logger(UserService.name);
    private readonly DEFAULT_PASSWWORD = '12345678';
    constructor(private userRepository: UserRepository) {}

    async initAdmin() {
        const { salt, hash } = CryptopHelper.hashPassword(this.DEFAULT_PASSWWORD);
        await this.userRepository.create({
            id: v4(),
            username: 'ADMIN',
            password: hash,
            salt,
            type: UserType.EMPLOYEE,
            isAdmin: true,
        });
    }

    async createUser(data: IUserDocument, logged: JwtPayloadDTO): Promise<UserDocument> {
        const { salt, hash } = CryptopHelper.hashPassword(this.DEFAULT_PASSWWORD);
        return await this.userRepository.create({
            id: StringHelper.isEmpty(data.id) ? v4() : data.id,
            username: data.username.toUpperCase(),
            permissions: data.permissions,
            permissionsKey: data.permissionsKey,
            password: hash,
            salt,
            type: data.type,
            createdBy: logged.username,
            updatedBy: logged.username,
        });
    }

    async createManyUserForLearner(data: CreateUserForLearnerDTO[], logged: JwtPayloadDTO) {
        this.logger.debug('createManyUserForLearner');
        if (ArrayHelper.isEmpty(data)) return;
        const { salt, hash } = CryptopHelper.hashPassword(this.DEFAULT_PASSWWORD);
        const users = [];
        const convertData = data.map(i => {
            return { id: i.id, code: i.code.toUpperCase() };
        });
        const existedUsers = await this.userRepository.findExistingByUniqueField('username');
        const newUsers = convertData.filter(u => !existedUsers.includes(u.code));
        newUsers.forEach(c => {
            users.push({
                id: c.id,
                username: c.code.toUpperCase(),
                password: hash,
                salt,
                type: UserType.LEARNER,
                createdBy: logged ? logged.username : null,
                updatedBy: logged ? logged.username : null,
            });
        });
        return await this.userRepository.insertMany(users);
    }

    async changePassword(dto: ChangePasswordDTO, logged: JwtPayloadDTO) {
        this.logger.debug('changePassword', logged.username);
        const user = await this.userRepository.findOne({ id: logged.sub });
        if (!user) {
            throw new NotFoundError('User not existed');
        }
        const isPasswordMatching = CryptopHelper.verifyPassword(dto.oldPassword, user.salt, user.password);
        if (!isPasswordMatching) {
            throw new UnauthorizedError('Wrong credentials provided');
        }
        const { salt, hash } = CryptopHelper.hashPassword(dto.newPassword);
        await this.userRepository.updateOne(
            { id: user.id },
            {
                salt,
                password: hash,
                updatedBy: logged.username,
            },
        );
    }

    async getUserAvailable(username: string) {
        const user = await this.userRepository.findOne({ username, deletedAt: null });
        if (!user) {
            throw new UnauthorizedError('User not exists');
        }
        return user;
    }

    async getInfo(username: string) {
        const data = await this.userRepository.getUserInfo(username);
        if (!data) {
            throw new NotFoundError('User not found');
        }
        return data;
    }
}
