import { Auth<PERSON><PERSON>roller } from './auth.controller';
import { CenterLocationController } from './center-location.controller';
import { CourseController } from './course.controller';
import { HealthController } from './health.controller';
import { LearnerEventController } from './learner-event.controller';
import { LearnerController } from './learner.controller';
import { PartnerController } from './partner.controller';
import { SettingController } from './setting.controller';
import { SyncController } from './sync.controller';
import { UserController } from './user.controller';
import { RoleController } from './role.controller';
import { PermissionController } from './permisison.controller';
import { EmployeeController } from './employee.controller';
import { DepartmentController } from './department.controller';
import { PositionController } from './position.controller';
import { EmployeePositionController } from './employee-position.controller';
import { ProbationController } from './probation.controller';

export const APP_CONTRO<PERSON>ERS = [
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Au<PERSON><PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roller,
    User<PERSON><PERSON>roller,
    <PERSON><PERSON>Controller,
    CenterLocationController,
    SyncController,
    PartnerController,
    PermissionController,
    RoleController,
    EmployeeController,
    DepartmentController,
    PositionController,
    EmployeePositionController,
    ProbationController,
];
