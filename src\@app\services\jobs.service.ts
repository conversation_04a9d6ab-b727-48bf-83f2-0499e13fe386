import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { CourseService } from './course.service';
import { LearnerService } from './learner.service';
import { TeleBotService } from '@app/shared/services/tele-bot.service';
import { LearnerEventService } from './learner-event.service';
import { ConfigService } from '@nestjs/config';
import { EmployeePositionRepository, EmployeeRepository } from '@database/mongodb/repositories';
import { ArrayHelper } from '@app/shared/helpers';

@Injectable()
export class JobsService {
    private readonly logger = new Logger(JobsService.name);
    private readonly isProd: boolean;

    constructor(
        private teleBotService: TeleBotService,
        private courseService: CourseService,
        private learnerService: LearnerService,
        private learnerEventService: LearnerEventService,
        private configService: ConfigService,
        private employeePositionRepository: EmployeePositionRepository,
        private employeeRepository: EmployeeRepository,
    ) {
        this.isProd = this.configService.get('NODE_ENV') === 'prod';
        this.logger.debug(`JobsService initialized in ${this.isProd ? 'production' : 'development'} mode`);
    }

    @Cron('0 0 * * *', {
        timeZone: 'Asia/Ho_Chi_Minh',
    })
    async syncAll() {
        if (!this.isProd) return;
        this.teleBotService.notifyMessage(`sync all start`);
        await this.courseService.syncCourseFromSheet();
        await this.learnerService.syncLearnerFromSheetToPortal();
        await this.learnerService.syncLearnerFromSheetToMoodle();
        await this.learnerEventService.processSyncLearnerEvent();
        await this.learnerService.syncGroupMembersToMoodle();
        this.teleBotService.notifyMessage(`sync all end`);
    }

    @Cron('0 1 * * *', {
        timeZone: 'Asia/Ho_Chi_Minh',
    })
    async syncStatusEmployee() {
        this.teleBotService.notifyMessage(`sync status employee start`);
        const results = await this.employeePositionRepository.getEmployeePositionAvailableUpdate();
        if (ArrayHelper.isEmpty(results)) return;
        const { employeeIds, employeePositionIds } = results[0];
        for (let i = 0; i < employeePositionIds.length; i++) {
            await this.employeePositionRepository.onChangeStatus(employeePositionIds[i]?.id);
        }
        for (let z = 0; z < employeeIds.length; z++) {
            await this.employeeRepository.onChangeStatus(employeeIds[z]?.id);
        }
        this.teleBotService.notifyMessage(`sync status employee end`);
    }

    async syncCourse() {
        this.teleBotService.notifyMessage(`sync course start`);
        await this.courseService.syncCourseFromSheet();
        this.teleBotService.notifyMessage(`sync course end`);
    }

    async syncLearner() {
        this.teleBotService.notifyMessage(`sync learner start`);
        await this.learnerService.syncLearnerFromSheetToPortal();
        await this.learnerService.syncLearnerFromSheetToMoodle();
        this.teleBotService.notifyMessage(`sync learner end`);
    }

    async syncLearnerEvent() {
        this.teleBotService.notifyMessage(`sync learner-event start`);
        await this.learnerEventService.processSyncLearnerEvent();
        this.teleBotService.notifyMessage(`sync learner-event end`);
    }

    async syncGroupMembersToMoodle() {
        this.teleBotService.notifyMessage(`sync GroupMembersToMoodle start`);
        await this.learnerService.syncGroupMembersToMoodle();
        this.teleBotService.notifyMessage(`sync GroupMembersToMoodle end`);
    }
}
