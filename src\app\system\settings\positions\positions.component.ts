import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { EditCreateDialogComponent, EditCreateDialogConfig, positionDialogConfig } from '@shared/components/edit-create-dialog';
import { PageConstant } from '@shared/constants';
import { ErrorHandlerService, ISort, PaginationRequest, PositionApiService, SORT_VAL, ToastService } from '@shared/services';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToolbarModule } from 'primeng/toolbar';

@Component({
    selector: 'app-positions',
    standalone: true,
    imports: [
        CommonModule,
        TableModule,
        ButtonModule,
        ConfirmDialogModule,
        ToolbarModule,
        EditCreateDialogComponent
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './positions.component.html',
    styleUrl: './positions.component.scss'
})
export class PositionsComponent implements OnInit {
    positions: any[] = [];
    loading: boolean = false;

    // Dialog properties
    positionDialog: boolean = false;
    editMode: boolean = false;
    selectedPosition: any = null;
    dialogConfig!: EditCreateDialogConfig;

    // Pagination properties
    totalRecords: number = 0;
    itemPerPage: number = PageConstant.ITEM_PER_PAGE;
    itemPerPageOptions: number[] = PageConstant.ITEM_PER_PAGE_OPTIONS;
    offset: number = 0;
    sort: ISort = { field: null, order: 1 };

    constructor(
        private positionApiService: PositionApiService,
        private errorHandlerService: ErrorHandlerService,
        private toastService: ToastService,
        private confirmationService: ConfirmationService
    ) {
        // Load dialog configuration in constructor - synchronous and immediate
        this.dialogConfig = positionDialogConfig(
            () => this.onDialogSuccess(),
            () => this.onDialogCancel()
        );
    }

    ngOnInit() {
        this.loadPositions();
    }



    loadPositions() {
        this.loading = true;

        const sortStr = this.sort.field ? `${this.sort.field},${SORT_VAL[`${this.sort.order}`]}` : '';
        const params: PaginationRequest = {
            limit: this.itemPerPage,
            offset: this.offset,
            sort: sortStr,
            query: null
        };

        this.positionApiService.getPositions(params).subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.positions = data.items || [];
                    this.totalRecords = data.total || 0;
                }
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            }
        });
    }

    onLazyLoad(event: TableLazyLoadEvent) {
        this.offset = event.first || 0;
        this.itemPerPage = event.rows || PageConstant.ITEM_PER_PAGE;
        this.sort = {
            field: event.sortField,
            order: event.sortOrder || 1
        };
        this.loadPositions();
    }

    openNew() {
        this.editMode = false;
        this.selectedPosition = null;
        this.positionDialog = true;
    }

    editPosition(position: any) {
        this.editMode = true;
        this.selectedPosition = position;
        this.positionDialog = true;
    }

    deletePosition(position: any) {
        this.confirmationService.confirm({
            message: 'Bạn có chắc chắn muốn xóa vị trí này?',
            header: 'Xác nhận',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.positionApiService.deletePosition(position.code).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Xóa vị trí thành công');
                            this.loadPositions();
                        }
                    }
                });
            }
        });
    }

    onDialogSuccess() {
        this.positionDialog = false;
        this.selectedPosition = null;
        this.loadPositions();
    }

    onDialogCancel() {
        this.positionDialog = false;
        this.selectedPosition = null;
    }
}
