import { TreeSelectConfig } from './generic-treeselect.component';
import { TreeNode } from 'primeng/api';

// Department Tree Select Configuration - All departments
export const DEPARTMENT_TREE_SELECT_CONFIG: TreeSelectConfig = {
    service: null, // Will be injected
    method: 'getAllDepartments',
    treeBuilder: buildDepartmentTree,
    valueField: 'id',
    placeholder: 'Chọn phòng ban',
    selectionMode: 'single'
};

// Department Tree Select Configuration - Root departments only
export const ROOT_DEPARTMENT_TREE_SELECT_CONFIG: TreeSelectConfig = {
    service: null, // Will be injected
    method: 'getRoots',
    treeBuilder: (departments: any[]) => {
        // For root departments, we don't need hierarchy, just convert to tree nodes
        return departments.map(dept => ({
            key: dept.id,
            data: {
                id: dept.id,
                code: dept.code,
                name: dept.name,
                parentId: dept.parentId
            },
            label: dept.name,
            children: []
        }));
    },
    valueField: 'id',
    placeholder: 'Chọn phòng ban gốc',
    selectionMode: 'single'
};

// Helper function to build department tree from nested structure (with childs property)
export function buildDepartmentTreeFromNested(departments: any[]): TreeNode[] {
    if (!departments || departments.length === 0) {
        return [];
    }

    const buildNode = (dept: any): TreeNode => {
        const node: TreeNode = {
            key: dept.id,
            data: {
                id: dept.id,
                code: dept.code,
                name: dept.name,
                parentId: dept.parentId
            },
            label: dept.name,
            children: []
        };

        // If this department has children, recursively build them
        if (dept.childs && dept.childs.length > 0) {
            node.children = dept.childs.map((child: any) => buildNode(child));
        }

        return node;
    };

    return departments.map(dept => buildNode(dept));
}

// Department Tree Select Configuration - Using new POST getOptions API
export const DEPARTMENT_BY_PARENT_TREE_SELECT_CONFIG: TreeSelectConfig = {
    service: null, // Will be injected
    method: 'getOptions',
    usePostMethod: true,
    idField: 'id',
    treeBuilder: buildDepartmentTreeFromNested,
    valueField: 'id',
    placeholder: 'Chọn phòng ban',
    selectionMode: 'single',
    bodyBuilder: (searchTerm: string, baseQuery: any, idQuery?: any) => {
        const body: any = {};
        const trimmedSearchTerm = searchTerm.trim();

        if (idQuery && idQuery.id) {
            // When searching by ID (for pre-selected values)
            body.andQ = { id: idQuery.id };
        } else if (searchTerm && trimmedSearchTerm) {
            // When searching by text (user typing in filter)
            body.orQ = {
                name: trimmedSearchTerm
            };
        }

        // Add any base query filters (always apply these)
        if (baseQuery && Object.keys(baseQuery).length > 0) {
            if (!body.andQ) {
                body.andQ = {};
            }
            body.andQ = { ...body.andQ, ...baseQuery };
        }

        return body;
    },
    onDependencyChange: (parentId: any) => {
        return parentId ? { id: parentId } : {};
    }
};

// Helper function to build department tree structure
export function buildDepartmentTree(departments: any[]): TreeNode[] {
    if (!departments || departments.length === 0) {
        return [];
    }

    // Create a map of departments by id for quick lookup
    const deptMap = new Map<string, TreeNode>();

    // First create all tree nodes
    departments.forEach(dept => {
        const node: TreeNode = {
            key: dept.id,
            data: {
                id: dept.id,
                code: dept.code,
                name: dept.name,
                parentId: dept.parentId
            },
            label: dept.name,
            children: []
        };
        deptMap.set(dept.id, node);
    });

    // Then build the hierarchy
    const rootNodes: TreeNode[] = [];
    departments.forEach(dept => {
        const node = deptMap.get(dept.id);
        if (!node) return;

        if (dept.parentId && deptMap.has(dept.parentId)) {
            // This is a child node
            const parentNode = deptMap.get(dept.parentId);
            if (parentNode && parentNode.children) {
                parentNode.children.push(node);
            }
        } else {
            // This is a root node
            rootNodes.push(node);
        }
    });

    return rootNodes;
}