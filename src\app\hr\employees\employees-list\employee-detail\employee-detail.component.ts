import { CommonModule } from '@angular/common';
import { Component, Input, OnChanges, OnInit, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EditCreateDialogComponent, EditCreateDialogConfig } from '@shared/components/edit-create-dialog';
import { employeePositionDialogFormConfig, probationDialogFormConfig } from '@shared/components/edit-create-form';
import { GenericPopoverComponent, PopoverConfig, employeeRetirePopoverConfig } from '@shared/components/generic-popover';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';
import { PROBATION_STATUS_OPTIONS } from '@shared/enums/probation-status.enum';
import { EmployeeApiService, EmployeePositionApiService, ErrorHandlerService, ProbationApiService, ToastService } from '@shared/services';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DatePickerModule } from 'primeng/datepicker';
import { DividerModule } from 'primeng/divider';
import { PopoverModule } from 'primeng/popover';
import { TableModule } from 'primeng/table';
import { TagModule } from 'primeng/tag';
import { TooltipModule } from 'primeng/tooltip';

@Component({
    selector: 'app-employee-detail',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        TableModule,
        TagModule,
        DividerModule,
        CardModule,
        ButtonModule,
        PopoverModule,
        DatePickerModule,
        TooltipModule,
        ConfirmDialogModule,
        GenericTagComponent,
        EditCreateDialogComponent,
        GenericPopoverComponent,
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './employee-detail.component.html',
    styleUrls: ['./employee-detail.component.scss']
})
export class EmployeeDetailComponent implements OnInit, OnChanges {
    @Input() employeeId: string | null = null;

    employee: any = null;
    loading: boolean = false;

    // Dialog and form properties
    employeePositionDialog: boolean = false;
    probationDialog: boolean = false;
    editMode: boolean = false;
    probationEditMode: boolean = false;
    selectedEmployeePosition: any = null;
    selectedProbation: any = null;
    selectedEmployeePositionForRetire: any = null;
    selectedEmployeePositionForProbation: any = null;

    // Dialog configurations
    employeePositionDialogConfig!: EditCreateDialogConfig;
    probationDialogConfig!: EditCreateDialogConfig;
    retirePopoverConfig!: PopoverConfig;

    // ViewChild for popovers
    @ViewChild('probationPopover') probationPopover: any;
    @ViewChild('genericRetirePopover') genericRetirePopover!: GenericPopoverComponent;

    statusOptions = PROBATION_STATUS_OPTIONS;

    constructor(
        private employeeApiService: EmployeeApiService,
        private employeePositionApiService: EmployeePositionApiService,
        private probationApiService: ProbationApiService,
        private confirmationService: ConfirmationService,
        private toastService: ToastService,
        private errorHandlerService: ErrorHandlerService
    ) {
        // Employee Position Dialog Configuration
        this.employeePositionDialogConfig = {
            width: '650px',
            createHeader: 'Thêm lịch sử công tác',
            editHeader: 'Sửa lịch sử công tác',
            modal: true,
            dismissableMask: true,
            styleClass: 'p-fluid',
            formConfig: employeePositionDialogFormConfig(),
            actions: [
                {
                    onClick: 'cancel',
                    useDefaultStyle: 'cancel'
                },
                {
                    onClick: 'submit',
                    disabled: (formValue: any, formValid: boolean) => !formValid,
                    useDefaultStyle: 'confirm', type: 'submit'
                }
            ]
        };

        // Probation Dialog Configuration
        this.probationDialogConfig = {
            width: '550px',
            createHeader: 'Thêm thử việc',
            editHeader: 'Sửa thử việc',
            modal: true,
            dismissableMask: true,
            styleClass: 'p-fluid',
            formConfig: probationDialogFormConfig(),
            actions: [
                {
                    onClick: 'cancel',
                    useDefaultStyle: 'cancel'
                },
                {
                    onClick: 'submit',
                    disabled: (formValue: any, formValid: boolean) => !formValid,
                    useDefaultStyle: 'confirm', type: 'submit'
                }
            ]
        };

        // Initialize retire popover configuration using direct import
        this.retirePopoverConfig = employeeRetirePopoverConfig(
            () => this.onPopoverSuccess(),
            () => this.onPopoverCancel()
        );
    }

    ngOnInit() {
        if (this.employeeId) {
            this.loadEmployeeDetails();
        }
    }

    ngOnChanges() {
        if (this.employeeId) {
            this.loadEmployeeDetails();
        } else {
            this.employee = null;
        }
    }

    loadEmployeeDetails() {
        if (!this.employeeId) return;

        this.loading = true;
        this.employeeApiService.getEmployeeById(this.employeeId).subscribe({
            next: (response) => {
                const { hasError, data } = this.errorHandlerService.handleInternal(response);
                if (!hasError) {
                    this.employee = data;
                }
                this.loading = false;
            },
            error: () => {
                this.loading = false;
            }
        });
    }

    // Employee Position methods
    editEmployeePosition(employeePosition: any) {
        this.editMode = true;
        this.selectedEmployeePosition = {
            id: employeePosition.id,
            positionId: employeePosition.positionId,
            departmentId: employeePosition.departmentId,
            fromDate: new Date(employeePosition.fromDate)
        };
        this.employeePositionDialog = true;
    }

    deleteEmployeePosition(employeePosition: any) {
        this.confirmationService.confirm({
            message: 'Bạn có chắc chắn muốn xóa lịch sử công tác này?',
            header: 'Xác nhận',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                this.employeePositionApiService.deleteEmployeePosition(employeePosition.id).subscribe({
                    next: (response) => {
                        const { hasError } = this.errorHandlerService.handleInternal(response);
                        if (!hasError) {
                            this.toastService.showSuccess('Xóa lịch sử công tác thành công');
                            this.loadEmployeeDetails(); // Reload employee data
                        }
                    }
                });
            }
        });
    }

    showRetirePopover(employeePosition: any, popover: any, event?: Event) {
        // Enhance the data with employee information for consistency
        this.selectedEmployeePositionForRetire = {
            ...employeePosition,
            employee: {
                code: this.employee?.code,
                name: this.employee?.profile?.fullname
            }
        };
        this.genericRetirePopover.show(event);
    }

    hideRetirePopover() {
        this.genericRetirePopover.hide();
        this.selectedEmployeePositionForRetire = null;
    }

    showProbationPopover(employeePosition: any, popover: any, event?: Event) {
        this.selectedEmployeePositionForProbation = employeePosition;
        popover.show(event);
    }

    hideProbationPopover() {
        this.selectedEmployeePositionForProbation = null;
    }

    addNewProbation() {
        if (!this.selectedEmployeePositionForProbation) return;

        this.probationEditMode = false;
        this.selectedProbation = {
            employeePositionId: this.selectedEmployeePositionForProbation.id,
            fromDate: null,
            toDate: null,
            deadline: null,
            status: 'IN_PROGRESS',
            isManual: false
        };
        this.probationDialog = true;
    }

    onPopoverSuccess() {
        this.selectedEmployeePositionForRetire = null;
        this.loadEmployeeDetails(); // Reload employee data
    }

    onPopoverCancel() {
        this.selectedEmployeePositionForRetire = null;
    }

    handleEmployeePositionSave(formValue: any, editMode: boolean) {
        if (editMode) {
            const updateData = {
                id: this.selectedEmployeePosition.id,
                positionId: formValue.positionId,
                departmentId: formValue.departmentId,
                fromDate: formValue.fromDate
            };

            this.employeePositionApiService.updateEmployeePosition(updateData).subscribe({
                next: (response) => {
                    const { hasError } = this.errorHandlerService.handleInternal(response);
                    if (!hasError) {
                        this.toastService.showSuccess('Cập nhật lịch sử công tác thành công');
                        this.employeePositionDialog = false;
                        this.loadEmployeeDetails(); // Reload employee data
                    }
                }
            });
        }
    }

    handleEmployeePositionCancel() {
        this.employeePositionDialog = false;
        this.selectedEmployeePosition = null;
    }

    // Probation methods
    editProbation(probation: any) {
        this.probationEditMode = true;
        this.selectedProbation = {
            id: probation.id,
            employeePositionId: probation.employeePositionId,
            fromDate: probation.fromDate ? new Date(probation.fromDate) : null,
            toDate: probation.toDate ? new Date(probation.toDate) : null,
            deadline: probation.deadline ? new Date(probation.deadline) : null,
            status: probation.status,
            isManual: probation.isManual
        };
        this.probationDialog = true;
    }

    handleProbationSave(formValue: any, editMode: boolean) {
        if (editMode) {
            // Exclude fromDate from the data when editing
            const { fromDate, ...probationData } = {
                ...formValue,
                toDate: formValue.toDate ? formValue.toDate.toISOString().split('T')[0] : null,
                deadline: formValue.deadline ? formValue.deadline.toISOString().split('T')[0] : null,
                isManual: formValue.isManual === 'true' || formValue.isManual === true
            };

            this.probationApiService.updateProbation(probationData).subscribe({
                next: (response) => {
                    const { hasError } = this.errorHandlerService.handleInternal(response);
                    if (!hasError) {
                        this.toastService.showSuccess('Cập nhật thử việc thành công');
                        this.probationDialog = false;
                        this.loadEmployeeDetails(); // Reload employee data
                    }
                }
            });
        } else {
            // Exclude id & fromDate from data when creating
            const { id, fromDate, ...probationData } = {
                ...formValue,
                toDate: formValue.toDate ? formValue.toDate.toISOString().split('T')[0] : null,
                deadline: formValue.deadline ? formValue.deadline.toISOString().split('T')[0] : null,
                isManual: formValue.isManual === 'true' || formValue.isManual === true
            };

            this.probationApiService.createProbation(probationData).subscribe({
                next: (response) => {
                    const { hasError } = this.errorHandlerService.handleInternal(response);
                    if (!hasError) {
                        this.toastService.showSuccess('Tạo thử việc thành công');
                        this.probationDialog = false;
                        this.loadEmployeeDetails(); // Reload employee data
                    }
                }
            });
        }
    }

    handleProbationCancel() {
        this.probationDialog = false;
        this.selectedProbation = null;
    }
}
