import { Injectable } from '@angular/core';
import { TableConfig, TableConfigRegistry, TableEntityType, TableFunctionMap, PLACEHOLDER_FUNCTIONS } from '@shared/components/generic-p-table';



// Module-level variables (shared across all imports of this module)
const configCache = new Map<TableEntityType, TableConfig>();

const configRegistry: TableConfigRegistry = {
  'probation': () => import('../components/generic-p-table').then(m => m.probationTableConfig),
  'employeePosition': () => import('../components/generic-p-table').then(m => m.employeePositionTableConfig),
  'employee': () => import('../components/generic-p-table').then(m => m.employeeTableConfig),
  // Add more configs here as needed - each will be lazy loaded
};

/**
 * Gets table configuration for a specific entity type
 * @param entityType The type of entity table to create
 * @returns Promise that resolves to complete table configuration
 */
export const getTableConfig = async (entityType: TableEntityType): Promise<TableConfig> => {
  // Check module-level cache first
  if (configCache.has(entityType)) {
    return configCache.get(entityType)!;
  }

  const configFactory = configRegistry[entityType];
  if (!configFactory) {
    throw new Error(`Unsupported entity type: ${entityType}. Available types: ${Object.keys(configRegistry).join(', ')}`);
  }

  // Dynamically import and create the config
  const configFunction = await configFactory();
  const config = configFunction();

  // Cache in module-level variable
  configCache.set(entityType, config);
  return config;
};

/**
 * Gets table configuration with mapped functions for a specific entity type
 * @param entityType The type of entity table to create
 * @param functionMap Map of placeholder strings to actual functions
 * @returns Promise that resolves to complete table configuration with mapped functions
 */
export const getTableConfigWithFunctions = async (entityType: TableEntityType, functionMap: TableFunctionMap): Promise<TableConfig> => {
  const config = await getTableConfig(entityType);
  return mapFunctions(config, functionMap);
};

/**
 * Optional service class for backward compatibility or DI scenarios
 * Delegates to standalone functions
 */
@Injectable({
  providedIn: 'root'
})
export class GenericPTableConfigService {
  async getTableConfig(entityType: TableEntityType): Promise<TableConfig> {
    return getTableConfig(entityType);
  }

  async getTableConfigWithFunctions(entityType: TableEntityType, functionMap: TableFunctionMap): Promise<TableConfig> {
    return getTableConfigWithFunctions(entityType, functionMap);
  }

  mapFunctions(config: TableConfig, functionMap: TableFunctionMap): TableConfig {
    return mapFunctions(config, functionMap);
  }
}
/**
   * Maps string placeholders to actual functions in a table configuration
   * @param config The table configuration with string placeholders
   * @param functionMap Map of placeholder strings to actual functions
   * @returns Table configuration with mapped functions
   */
export const mapFunctions = (config: TableConfig, functionMap: TableFunctionMap): TableConfig  => {
  // Create a deep clone to avoid mutating the original config
  const mappedConfig = JSON.parse(JSON.stringify(config));

  // Restore the service reference (can't be cloned via JSON)
  mappedConfig.service = config.service;

  // Map action functions
  if (mappedConfig.actions) {
    mappedConfig.actions.forEach((action: any) => {
      Object.keys(functionMap).forEach(key => {
        if (action.onClick === PLACEHOLDER_FUNCTIONS[key as keyof typeof PLACEHOLDER_FUNCTIONS]) {
          action.onClick = functionMap[key as keyof typeof PLACEHOLDER_FUNCTIONS];
        }
      });
    });
  }

  // Map selection functions
  if (mappedConfig.features?.selection) {
    Object.keys(functionMap).forEach(key => {
      if (mappedConfig.features!.selection!.onSelect === PLACEHOLDER_FUNCTIONS[key as keyof typeof PLACEHOLDER_FUNCTIONS]) {
        mappedConfig.features!.selection!.onSelect = functionMap[key as keyof typeof PLACEHOLDER_FUNCTIONS];
      }
      if (mappedConfig.features!.selection!.onUnselect === PLACEHOLDER_FUNCTIONS[key as keyof typeof PLACEHOLDER_FUNCTIONS]) {
        mappedConfig.features!.selection!.onUnselect = functionMap[key as keyof typeof PLACEHOLDER_FUNCTIONS];
      }
    });
  }

  return mappedConfig;
}