{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "noLib": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2019", "sourceMap": true, "allowJs": true, "resolveJsonModule": true, "esModuleInterop": true, "incremental": true, "alwaysStrict": true, "noImplicitAny": false, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "types": ["node"], "typeRoots": ["node_modules/@types"], "outDir": "./dist", "baseUrl": "./src", "rootDir": "./src", "paths": {"@app/*": ["@app/*"], "@database/*": ["@database/*"], "@modules/*": ["modules/*"], "@setup/*": ["@setup/*"], "@errors/*": ["@errors/*"]}}, "include": ["src/**/*.ts", "src/@database/mongodb/schemas/.schema.ts"], "exclude": ["node_modules", "**/*.spec.ts", "documentation", "dist"]}