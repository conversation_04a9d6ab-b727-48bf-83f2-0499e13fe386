export enum EmployeeStatusEnum {
    NO_POSITION = 'NO_POSITION',
    TO_ONBOARD = 'TO_ONBOARD',
    PROBATION = 'PROBATION',
    PROBATION_OVERDUE = 'PROBATION_OVERDUE',
    PROBATION_PENDING = 'PROBATION_PENDING',
    PROBATION_FAIL = 'PROBATION_FAIL',
    PROBATION_PASS = 'PROBATION_PASS',
    PROBATION_CANCELLED = 'PROBATION_CANCELLED',
    ACTIVE = 'ACTIVE', // Đang làm
    TO_INACTIVE = 'TO_INACTIVE', // Sẽ nghỉ
    INACTIVE = 'INACTIVE',
    TO_TRANSFER = 'TO_TRANSFER',
    MIXED = 'MIXED', // Có nhiều trạng thái khác nhau
}

export enum EmployeePositionStatusEnum {
    TO_ONBOARD = 'TO_ONBOARD',
    PROBATION = 'PROBATION',
    PROBATION_OVERDUE = 'PROBATION_OVERDUE',
    PROBATION_PENDING = 'PROBATION_PENDING',
    PROBATION_FAIL = 'PROBATION_FAIL',
    PROBATION_PASS = 'PROBATION_PASS',
    PROBATION_CANCELLED = 'PROBATION_CANCELLED',
    ACTIVE = 'ACTIVE', // Đang làm
    TO_INACTIVE = 'TO_INACTIVE', // Sẽ nghỉ
    INACTIVE = 'INACTIVE',
}

export const EMPLOYEE_POSITION_STATUS_PRIORITY = {
    [EmployeePositionStatusEnum.ACTIVE]: 1,
    [EmployeePositionStatusEnum.PROBATION]: 2,
    [EmployeePositionStatusEnum.PROBATION_PENDING]: 2,
    [EmployeePositionStatusEnum.PROBATION_FAIL]: 2,
    [EmployeePositionStatusEnum.PROBATION_PASS]: 2,
    [EmployeePositionStatusEnum.TO_ONBOARD]: 3,
    [EmployeePositionStatusEnum.TO_INACTIVE]: 3,
    [EmployeePositionStatusEnum.INACTIVE]: 4,
};

export enum ProbationStatusEnum {
    IN_PROGRESS = 'IN_PROGRESS',
    RESULT_PENDING = 'RESULT_PENDING',
    PASS = 'PASS',
    FAIL = 'FAIL',
    CANCELLED = 'CANCELLED',
}
