export class DateHelper {
    public static FORMAT_DATE_ISO = 'YYYY-MM-DD';
    public static FORMAT_DATE_VI_ISO = 'DD-MM-YYYY';
    public static FORMAT_DATE_VI = 'DD/MM/YYYY';
    public static FORMAT_DAY_MONTH = 'DD/MM';

    public static isToday(timestamp: number): boolean {
        const today = new Date();
        const date = new Date(timestamp);
        return date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate();
    }

    public static getStartOfDate(date: Date = new Date()) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }

    public static getDateRange(fromISODate: string, toISODate: string): Date[] {
        const start = new Date(fromISODate);
        const end = new Date(toISODate);
        const dateList = [];

        while (start <= end) {
            dateList.push(new Date(start));
            start.setDate(start.getDate() + 1); // Move to next day
        }

        return dateList;
    }

    public static isValidISODate(dateString: string): boolean {
        const isoDateRegex = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(Z|([+-]\d{2}:\d{2})))?$/;
        return isoDateRegex.test(dateString);
    }
}
