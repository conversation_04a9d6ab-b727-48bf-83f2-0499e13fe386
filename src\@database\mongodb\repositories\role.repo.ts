import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { RoleDocument } from '../schemas';
import { GenericRepository } from './generic.repo';
import { MONGO_CONST } from '../mongodb.constants';
import { ArrayHelper } from '@app/shared/helpers';

@Injectable()
export class RoleRepository extends GenericRepository<RoleDocument> {
    private readonly context = RoleRepository.name;

    constructor(
        @Inject(MONGO_CONST.ROLE_COLLECTION)
        private readonly roleModel: Model<RoleDocument>,
    ) {
        super(roleModel);
    }

    async getPermissionsByRoles(roleCodes: String[]): Promise<string[]> {
        const result = await this.roleModel
            .aggregate([
                {
                    $match: {
                        code: { $in: roleCodes },
                    },
                },
                { $unwind: '$permissions' },
                { $group: { _id: null, permissions: { $addToSet: '$permissions' } } },
                { $project: { _id: 0, permissions: 1 } },
            ])
            .exec();
        return ArrayHelper.isEmpty(result) ? [] : result[0]?.permissions;
    }
}
