<p-dialog [(visible)]="visible"
          [style]="{width: dialogWidth}"
          [header]="dialogHeader"
          [modal]="stepperConfig.modal !== false"
          [dismissableMask]="stepperConfig.dismissableMask !== false"
          [styleClass]="stepperConfig.styleClass || 'p-fluid'"
          [confirmClose]="hideDialog.bind(this)"
          (onHide)="onDialogHide()">

    <ng-template pTemplate="content">
        <p-stepper [value]="stepperState.currentStep"
                   [linear]="isLinear">
            <p-step-list>
                <p-step *ngFor="let step of stepperConfig.steps"
                        [value]="step.value">
                    {{ step.label }}
                </p-step>
            </p-step-list>

            <p-step-panels>
                <p-step-panel *ngFor="let step of stepperConfig.steps"
                              [value]="step.value">
                    <ng-template pTemplate="content" let-activateCallback="activateCallback">

                        <!-- Form-based step content -->
                        <div *ngIf="step.formConfig && getStepFormConfig(step) as formConfig">
                            <edit-create-form [editMode]="getStepEditMode(step)"
                                              [initialData]="getStepInitialData(step)"
                                              [isDisabled]="getStepIsDisabled(step)"
                                              [formConfig]="formConfig"
                                              (formChange)="onFormChange($event, step.value)">
                            </edit-create-form>
                        </div>

                        <!-- Step Actions -->
                        <div class="flex pt-4 justify-between">
                            <!-- Left actions -->
                            <div class="flex gap-2">
                                <p-button *ngFor="let action of getVisibleLeftActions()"
                                          [label]="getActionLabel(action)"
                                          [icon]="getActionIcon(action)"
                                          [severity]="action.severity"
                                          styleClass="h-10"
                                          [disabled]="isActionDisabled(action)"
                                          [loading]="isActionLoading(action)"
                                          (onClick)="executeAction(action, activateCallback)">
                                </p-button>
                            </div>

                            <!-- Right actions -->
                            <div class="flex gap-2">
                                <p-button *ngFor="let action of getVisibleRightActions()"
                                          [label]="getActionLabel(action)"
                                          [icon]="getActionIcon(action)"
                                          [severity]="action.severity"
                                          [iconPos]="action.icon && getActionLabel(action) ? 'right' : 'left'"
                                          styleClass="h-10"
                                          [disabled]="isActionDisabled(action)"
                                          [loading]="isActionLoading(action)"
                                          (onClick)="executeAction(action, activateCallback)">
                                </p-button>
                            </div>
                        </div>

                    </ng-template>
                </p-step-panel>
            </p-step-panels>
        </p-stepper>
    </ng-template>
</p-dialog>

<p-confirmDialog [style]="{width: '450px'}"
                 dismissableMask="true" />