<div class="flex flex-col w-full h-full">
    <div class="grid grid-cols-12 gap-4 h-full">
        <!-- Left side: Course selection and learner list -->
        <div class="col-span-12 lg:col-span-4">
            <div class="flex flex-col gap-4 h-full">
                <!-- Course Selection -->
                <div class="flex flex-col gap-2">
                    <label class="block text-surface-900 dark:text-surface-0 font-medium">Chọn khóa học</label>
                    <generic-p-select
                                      [config]="courseSelectConfig"
                                      [(ngModel)]="selectedCourse"
                                      (ngModelChange)="onCourseChange($event)"
                                      placeholder="Chọn khóa học..."
                                      styleClass="w-full">
                    </generic-p-select>
                </div>

                <!-- Learners List -->
                <div class="flex flex-col gap-2 flex-1" *ngIf="selectedCourse">
                    <label class="block text-surface-900 dark:text-surface-0 font-medium">
                        <PERSON>h sách học viên
                        <span class="text-sm text-gray-500" *ngIf="learners.length > 0">({{ learners.length }} học viên)</span>
                    </label>
                    <div class="flex-1 min-h-0">
                        <p-listbox [options]="learners"
                                   [(ngModel)]="selectedLearner"
                                   (onChange)="onLearnerSelect($event)"
                                   optionLabel="label"
                                   [filter]="true"
                                   filterBy="label"
                                   filterPlaceholder="Tìm kiếm học viên..."
                                   scrollHeight="calc(100vh - 27rem)"
                                   [virtualScroll]="true"
                                   [virtualScrollItemSize]="55.97"
                                   styleClass="w-full h-full"
                                   listStyleClass="w-full h-full"
                                   emptyMessage="Không có học viên nào trong khóa học này"
                                   emptyFilterMessage="Không tìm thấy học viên phù hợp">

                            <ng-template let-learner #item>
                                <div class="flex flex-col gap-1">
                                    <div class="flex items-center gap-2">
                                        <generic-p-tag [value]="learner.code" />
                                        <span>{{ learner.fullname }}</span>
                                    </div>
                                    <div class="text-sm font-bold italic text-gray-500 ml-1" *ngIf="learner.ec">{{ learner.ec }}
                                    </div>
                                </div>
                            </ng-template>
                            <ng-template #empty>
                                <ng-container *ngIf="loading; else noResults">
                                    <span class="italic">Loading — please wait…</span>
                                </ng-container>
                                <ng-template #noResults>
                                    <span class="italic">No results found.</span>
                                </ng-template>
                            </ng-template>
                        </p-listbox>
                    </div>
                </div>

                <!-- Empty state when no course selected -->
                <div class="flex flex-col items-center justify-center flex-1 text-gray-500" *ngIf="!selectedCourse">
                    <i class="pi pi-graduation-cap text-4xl mb-2"></i>
                    <p>Vui lòng chọn khóa học để xem danh sách học viên</p>
                </div>
            </div>
        </div>

        <!-- Right side: Learner details -->
        <div class="flex flex-col min-h-0 h-full col-span-12 lg:col-span-8">
            <div class="flex flex-col flex-1 min-h-0 h-full gap-2">
                <div class="flex flex-none" *ngIf="selectedLearner">
                    <label class="block text-surface-900 dark:text-surface-0 font-medium">
                        Lịch sử học tập - {{ selectedLearner.code }}
                    </label>
                </div>

                <!-- Learner result component -->
                <div class="flex flex-col flex-1 min-h-0 overflow-y-auto" *ngIf="selectedLearner && learnerData.length > 0">
                    <learner-result [listData]="learnerData"></learner-result>
                </div>

                <!-- Empty state when no learner selected -->
                <div class="flex flex-col items-center justify-center flex-1 text-gray-500" *ngIf="!selectedLearner && selectedCourse">
                    <i class="pi pi-user text-4xl mb-2"></i>
                    <p>Vui lòng chọn học viên để xem lịch sử học tập</p>
                </div>

                <!-- Empty state when no course selected -->
                <div class="flex flex-col items-center justify-center flex-1 text-gray-500" *ngIf="!selectedCourse">
                    <i class="pi pi-info-circle text-4xl mb-2"></i>
                    <p>Chọn khóa học và học viên để xem thông tin chi tiết</p>
                </div>

                <!-- Empty state when learner selected but no data -->
                <div class="flex flex-col items-center justify-center flex-1 text-gray-500" *ngIf="selectedLearner && learnerData.length === 0">
                    <i class="pi pi-exclamation-triangle text-4xl mb-2"></i>
                    <p>Không có dữ liệu lịch sử học tập cho học viên này</p>
                </div>
            </div>
        </div>
    </div>
</div>