import { PaginationRequest, PaginationResponse } from '@app/models/pagination';
import { ArrayHelper } from '@app/shared/helpers';
import * as _ from 'lodash';
import { ClientSession, FilterQuery, Model, UpdateQuery } from 'mongoose';

export abstract class GenericRepository<T> {
    private readonly baseModel: Model<T>;

    constructor(baseModel: Model<T>) {
        this.baseModel = baseModel;
    }

    private queryBuilder(model: Object) {
        let query = {};
        if (!_.isEmpty(model)) {
            for (let key of Object.keys(model)) {
                query[key] = model[key];
            }
        }
        return query;
    }

    async findWithPagination(request: PaginationRequest, projection = null, populate = []): Promise<PaginationResponse<any>> {
        const { query, offset, limit, sort } = request;
        const total = await this.baseModel.countDocuments(query).exec();
        const items = await this.baseModel.find(query, projection).populate(populate).sort(sort).skip(offset).limit(limit).exec();

        return new PaginationResponse({ total, items, offset, limit, sort });
    }

    async findAll(queryModel: FilterQuery<T> = {}, projection = null, populate = []): Promise<T[]> {
        const query = this.queryBuilder(queryModel);
        return await this.baseModel.find(query, projection).populate(populate).exec();
    }

    async findOne(queryModel: FilterQuery<T> = {}, projection = null, populate = []): Promise<T> {
        const query = this.queryBuilder(queryModel);
        return await this.baseModel.findOne(query, projection).populate(populate).exec();
    }

    //============Cmd=================
    async create(model): Promise<T> {
        return await this.baseModel.create(model);
    }

    async insertMany(models: any[], ordered: boolean = true) {
        return await this.baseModel.insertMany(models, { ordered });
    }

    async updateOne(filter: FilterQuery<T>, model: UpdateQuery<T>, upsert = false): Promise<T> {
        return await this.baseModel.findOneAndUpdate(filter, model, { new: true, upsert });
    }

    async updateMany(filter: FilterQuery<T>, update: UpdateQuery<T>, upsert = false): Promise<any> {
        return await this.baseModel.updateMany(filter, update, { new: true, upsert });
    }

    async deleteOne(filter: FilterQuery<T>): Promise<T> {
        return await this.baseModel.findOneAndDelete(filter);
    }

    async deleteMany(filter: FilterQuery<T>): Promise<any> {
        return await this.baseModel.deleteMany(filter);
    }

    async findExistingByUniqueField(fieldName: string, filter: FilterQuery<T> = {}): Promise<string[]> {
        const project = {
            _id: 0,
            [fieldName]: 1,
        };
        const result = await this.baseModel.aggregate([
            {
                $match: filter,
            },
            {
                $project: project,
            },
            {
                $group: {
                    _id: null,
                    existeds: { $push: `$${fieldName}` },
                },
            },
        ]);
        return ArrayHelper.isEmpty(result) ? [] : result[0].existeds;
    }

    async bulkWrite(operations: any[]): Promise<any> {
        return await this.baseModel.bulkWrite(operations);
    }
}
