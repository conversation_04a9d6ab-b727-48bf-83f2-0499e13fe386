.center-location {
  display: flex;
  flex-direction: row;
  height: calc(100vh - 8rem);

  .content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  .center-list {
    overflow-y: auto;
    height: 100%;

    .sticky-search {
      position: sticky;
      top: 0;
      padding: 0px 14px 4px;
      background: var(--surface-card);
      border-radius: 8px;

      input-search-text ::ng-deep .flex {
        box-shadow: 0 2px 5px #00000019
      }
    }

    .center-item {
      ul {
        list-style: none;
        padding: 0;
        margin: 0;
      }
  
      li {
        padding: 10px;
        border-bottom: 1px solid #ddd;
        cursor: pointer;
  
        &:hover {
          background-color: var(--bg-item-horver);
        }
      }
  
      .center-name {
        font-size: 16px;
        font-weight: 700;
        color: var(--primary-color);
        margin: 0 0 0.5rem 0;
      }

      .center-address {
        font-size: 14px;
        color: var(--text-second);
        margin: 0 0 0.5rem 0;
      }
  
      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        justify-content: flex-end;
      }
    
      .google-link {
        display: inline-block;
        padding: 6px 20px;
        color: var(--primary-color);
        border-radius: 20px;
        border: 2px solid var(--primary-color);
        font-weight: bold;
        transition: background-color 0.3s, transform 0.3s;
      }
    
      .google-link:hover {
        background-color: var(--primary-color);
        color: #ffffff;
        transform: translateY(-3px);
      }
    }
  }
}
#map {
  flex: 1;
  height: 100%
}