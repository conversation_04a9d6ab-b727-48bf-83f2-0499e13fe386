import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SettingType } from '@shared/enums';
import { RequestHelper } from '@shared/helpers';
import { Observable } from 'rxjs';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class SyncApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/syncs`;
    }

    getAll(types?: SettingType[]): Observable<any> {
        const params = RequestHelper.createRequestOption({ types });
        return this.httpClient.get<any>(`${this.url}`, {
            params
        });
    }

    getAllErrors(req: PaginationRequest, body: any): Observable<any> {
        const params = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/errors`, body, {
            params
        });
    }

    syncAll(): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/all`, {});
    }

    syncCourse(): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/course`, {});
    }

    syncLearner(): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/learner`, {});
    }

    syncLearnerEvent(): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/learner-event`, {});
    }

    syncMoodleLearnerGroup(): Observable<any> {
        return this.httpClient.post<any>(`${this.url}/group-members-moodle`, {});
    }
}
