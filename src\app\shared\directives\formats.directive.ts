import { Directive, Input, ElementRef, <PERSON>Ini<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';

@Directive({
    selector: '[autoFormat]'
})
export class AutoFormatDirective implements OnInit, OnDestroy {
    @Input() autoFormat?: string;

    private isFormatting = false;
    private inputListener?: (event: Event) => void;

    private readonly ALLOWED_TYPES = ['vnPhone'];

    constructor(private el: ElementRef) {}

    ngOnInit() {
        // Skip if no autoFormat specified or empty string
        if (!this.autoFormat || this.autoFormat.trim() === '') {
            return;
        }

        // Validate format type
        if (!this.ALLOWED_TYPES.includes(this.autoFormat)) {
            console.error(`AutoFormatDirective: Invalid format type '${this.autoFormat}'. Allowed types: ${this.ALLOWED_TYPES.join(', ')}`);
            return;
        }

        // Attach the appropriate listener based on format type
        this.attachListener();
    }

    private attachListener() {
        switch (this.autoFormat) {
            case 'vnPhone':
                this.inputListener = this.onVnPhoneInput.bind(this);
                this.el.nativeElement.addEventListener('input', this.inputListener);
                break;
            default:
                return;
        }
    }

    private onVnPhoneInput(event: Event) {
        if (this.isFormatting) return;
        this.isFormatting = true;

        const input = this.el.nativeElement;
        let value = input.value.replace(/[^\d+]+/g, ''); // remove non-digits and +

        if (!/^(\+|0)/g.test(value)) {
            value = '';
        } else if (value.startsWith('84')) {
            value = '+' + value;
        } else if (value.startsWith('0')) {
            value = value.replace(/^0/,'+84');
        }

        // Apply basic formatting (0999 999 999)
        value = value.replace(/ /g,'');
        if (value.startsWith('+84')) {
            value = '+84 ' +
                value.substring(3, 6).replace(/0/,'') + ' ' +
                value.substring(6, 9) + ' ' +
                value.substring(9, 12) +
                value.substring(12,value.length > 12 ? value.length : 12).replace(/./g,'?');
        } else {
            value = value.substring(0, 4) + ' ' + value.substring(4, 7) + ' ' + value.substring(7, 10);
        }

        input.value = value.trim();
        event.target?.dispatchEvent(new Event('input'));
        this.isFormatting = false;
    }

    ngOnDestroy() {
        // Clean up listener when directive is destroyed
        if (this.inputListener) {
            this.el.nativeElement.removeEventListener('input', this.inputListener);
        }
    }
}