
<div class="flex flex-col gap-4">
    <div>
        <span class="text-sm text-muted-color font-semibold">Primary</span>
        <div class="pt-2 flex gap-2 flex-wrap justify-start">
            @for (primaryColor of primaryColors(); track primaryColor.name) {
                <button
                    type="button"
                    [title]="primaryColor.name"
                    (click)="updateColors($event, 'primary', primaryColor)"
                    [ngClass]="{ 'outline-primary': primaryColor.name === selectedPrimaryColor() }"
                    class="border-none w-5 h-5 rounded-full p-0 cursor-pointer outline-none outline-offset-1"
                    [style]="{
                        'background-color': primaryColor?.name === 'noir' ? 'var(--text-color)' : primaryColor?.palette?.['500']
                    }"
                ></button>
            }
        </div>
    </div>
    <div>
        <span class="text-sm text-muted-color font-semibold">Surface</span>
        <div class="pt-2 flex gap-2 flex-wrap justify-start">
            @for (surface of surfaces; track surface.name) {
                <button
                    type="button"
                    [title]="surface.name"
                    (click)="updateColors($event, 'surface', surface)"
                    [ngClass]="{ 'outline-primary': selectedSurfaceColor() ? selectedSurfaceColor() === surface.name : layoutService.layoutConfig().darkTheme ? surface.name === 'zinc' : surface.name === 'slate' }"
                    class="border-none w-5 h-5 rounded-full p-0 cursor-pointer outline-none outline-offset-1"
                    [style]="{
                        'background-color': surface?.name === 'noir' ? 'var(--text-color)' : surface?.palette?.['500']
                    }"
                ></button>
            }
        </div>
    </div>
    <div class="flex flex-col gap-2">
        <span class="text-sm text-muted-color font-semibold">Presets</span>
        <p-selectbutton [options]="presets" [ngModel]="selectedPreset()" (ngModelChange)="onPresetChange($event)" [allowEmpty]="false" size="small" />
    </div>
    <div *ngIf="showMenuModeButton()" class="flex flex-col gap-2">
        <span class="text-sm text-muted-color font-semibold">Menu Mode</span>
        <p-selectbutton [ngModel]="menuMode()" (ngModelChange)="onMenuModeChange($event)" [options]="menuModeOptions" [allowEmpty]="false" size="small" />
    </div>
    <div *ngIf="isAdmin()" class="flex flex-col gap-2">
        <span class="text-sm text-muted-color font-semibold">API Server</span>
        <p-select
            [ngModel]="selectedEndpoint()"
            (ngModelChange)="onApiEndpointChange($event)"
            [options]="availableEndpoints"
            optionLabel="name"
            placeholder="Select API Server"
            size="small"
            class="w-full">
            <ng-template pTemplate="selectedItem" let-option>
                <div class="flex flex-col">
                    <span class="font-medium">{{ option.name }}</span>
                    <span class="text-xs text-muted-color">{{ option.description }}</span>
                </div>
            </ng-template>
            <ng-template pTemplate="item" let-option>
                <div class="flex flex-col">
                    <span class="font-medium">{{ option.name }}</span>
                    <span class="text-xs text-muted-color">{{ option.url }}</span>
                    <span class="text-xs text-muted-color">{{ option.description }}</span>
                </div>
            </ng-template>
        </p-select>
    </div>
</div>
