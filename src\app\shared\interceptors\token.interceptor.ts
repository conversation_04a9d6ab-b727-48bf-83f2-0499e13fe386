import { <PERSON>tt<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { LocalStorageService, STORAGE_KEY } from '@shared/services';
import { EnvironmentService } from '@shared/services';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {
    constructor(
        private storageService: LocalStorageService,
        private environmentService: EnvironmentService
    ) {}

    intercept(request: HttpRequest<any>, next: HttpHand<PERSON>): Observable<HttpEvent<any>> {
        const currentApiUrl = this.environmentService.getCurrentApiUrl();
        if (!request.url || (request.url.startsWith('http') && !(currentApiUrl && request.url.startsWith(currentApiUrl)))) {
            return next.handle(request);
        }

        const token: string | null = this.storageService.getData(STORAGE_KEY.TOKEN);
        if (token) {
            request = request.clone({
                setHeaders: {
                    Authorization: `Bearer ${token}`
                }
            });
        }
        return next.handle(request);
    }
}
