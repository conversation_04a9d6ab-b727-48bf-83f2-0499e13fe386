import { HTTP_INTERCEPTORS, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { ApplicationConfig } from '@angular/core';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { APP_BASE_HREF } from '@angular/common';
import Aura from '@primeng/themes/aura';
import { providePrimeNG } from 'primeng/config';
import { appRoutes } from './app.routes';
import { MessageService } from 'primeng/api';
import { TokenInterceptor } from '@shared/interceptors/token.interceptor';
import { definePreset } from '@primeng/themes';

const myPreset = definePreset(Aura, {
    semantic: {
        primary: {
            50: '{blue.50}',
            100: '{blue.100}',
            200: '{blue.200}',
            300: '{blue.300}',
            400: '{blue.400}',
            500: '{blue.500}',
            600: '{blue.600}',
            700: '{blue.700}',
            800: '{blue.800}',
            900: '{blue.900}',
            950: '{blue.950}'
        }
    }
});

// Function to determine the base href dynamically
function getBaseHref(): string {
    // For browser environments
    if (typeof window !== 'undefined') {
        const path = window.location.pathname;
        const pathSegments = path.split('/').filter(Boolean);

        // If hosted under "/zapps/appId"
        const appIndex = pathSegments.indexOf('zapps');
        if (appIndex !== -1 && pathSegments.length > appIndex + 1) {
            const baseHref = `/zapps/${pathSegments[appIndex + 1]}/`;
            console.log(`[AppConfig] Setting base href to: ${baseHref}`);
            return baseHref;
        }
    }

    // Default: root path "/"
    console.log('[AppConfig] Setting base href to: /');
    return '/';
}

export const appConfig: ApplicationConfig = {
    providers: [
        MessageService,
        provideRouter(
            appRoutes,
            withInMemoryScrolling({
                anchorScrolling: 'enabled',
                scrollPositionRestoration: 'enabled'
            }),
            withEnabledBlockingInitialNavigation()
        ),
        provideHttpClient(withInterceptorsFromDi()),
        { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
        { provide: APP_BASE_HREF, useFactory: getBaseHref },
        provideAnimationsAsync(),
        providePrimeNG(
            {
                theme: { preset: myPreset, options: { darkModeSelector: '.app-dark' } },
                ripple: true,
            },
        )
    ]
};