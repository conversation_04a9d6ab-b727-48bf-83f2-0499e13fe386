import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output, OnChanges, SimpleChanges, AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { StepperModule } from 'primeng/stepper';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService } from 'primeng/api';
import { EditCreateFormComponent } from '../edit-create-form';

import { ConfirmCloseDirective } from '../../directives/confirm-close-dialog.directive';
import { StepperHandlerService } from '../../services/stepper-handler.service';
import {
  StepperConfig,
  StepConfig,
  StepAction,
  StepperState,
  StepFormConfig
} from './stepper.interfaces';

@Component({
  selector: 'generic-p-stepper',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DialogModule,
    ButtonModule,
    StepperModule,
    ConfirmDialogModule,
    EditCreateFormComponent,
    ConfirmCloseDirective
  ],
  providers: [ConfirmationService],
  templateUrl: './generic-stepper.component.html',
  styleUrls: ['./generic-stepper.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class GenericPStepperComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() visible: boolean = false;
  @Input() stepperConfig: StepperConfig = {
    entityType: '',
    steps: [],
    actions: []
  };
  @Input() initialData: any = null;
  @Input() editMode: boolean = false;

  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() onComplete = new EventEmitter<StepperState>();
  @Output() onCancel = new EventEmitter<void>();
  @Output() onStepChange = new EventEmitter<{ step: number, stepperState: StepperState }>();

  stepperState: StepperState = {
    currentStep: 1,
    forms: {},
    stepData: {},
    stepModes: {},
    isLoading: false,
    initialData: null
  };

  // Resolved step configs - only updated when step changes
  resolvedStepConfigs: { [stepValue: number]: StepFormConfig } = {};

  // Function map for action onClick handlers
  actionFunctionMap: Record<string, (activateCallback?: (step: number) => void, currentStepHandlerConfig?: any) => void | Promise<void>> = {};

  constructor(
    private confirmationService: ConfirmationService,
    private cdr: ChangeDetectorRef,
    private stepperHandlerService: StepperHandlerService
  ) {
    // Initialize action function map
    this.actionFunctionMap = {
      'goBack': (activateCallback) => this.stepperHandlerService.goBack(this.stepperState, activateCallback),
      'enableEdit': () => this.stepperHandlerService.enableEdit(this.stepperState),
      'goNext': (activateCallback, currentStepHandlerConfig) =>
        this.stepperHandlerService.goNext(this.stepperState, this.stepperConfig, activateCallback, currentStepHandlerConfig),
      'skipStep': () => this.stepperHandlerService.skipStep(this.stepperState, this.stepperConfig, () => this.hideDialog()),
      'cancel': () => this.stepperHandlerService.cancel(this.stepperConfig)
    };
  }

  ngOnInit() {

  }

  ngAfterViewInit() {
    this.initializeStepper();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['visible'] && changes['visible'].currentValue) {
      this.initializeStepper();
      this.cdr.markForCheck();
    }
    if (changes['initialData'] || changes['editMode']) {
      this.updateStepperState();
      this.cdr.markForCheck();
    }
  }

  private initializeStepper() {
    this.stepperState = {
      currentStep: 1,
      forms: {},
      stepData: {},
      stepModes: {},
      isLoading: false,
      initialData: this.initialData
    };

    // Initialize step modes - all steps start in 'create' mode
    this.stepperConfig.steps.forEach(step => {
      this.stepperState.stepModes[step.value] = 'create';
    });

    // Resolve all step configs once at initialization
    this.resolveAllStepConfigs();
  }

  private updateStepperState() {
    this.stepperState.initialData = this.initialData;
    // Re-resolve configs when stepper state changes
    this.resolveAllStepConfigs();
  }

  private resolveAllStepConfigs() {
    this.stepperConfig.steps.forEach(step => {
      if (step.formConfig) {
        this.resolvedStepConfigs[step.value] = this.resolveStepConfig(step);
      }
    });
  }

  private updateCurrentStepConfig() {
    const currentStep = this.stepperConfig.steps.find(s => s.value === this.stepperState.currentStep);
    if (currentStep?.formConfig) {
      this.resolvedStepConfigs[this.stepperState.currentStep] = this.resolveStepConfig(currentStep);
    }
  }

  private resolveStepConfig(step: StepConfig): StepFormConfig {
    if (!step.formConfig) return { fields: [] };

    console.log(`Resolving config for step ${step.value} with step.formConfig.isDisabled: `, step.formConfig.isDisabled);

    const hasEditModeFunction = typeof step.formConfig.editMode === 'function';
    const hasIsDisabledFunction = typeof step.formConfig.isDisabled === 'function';

    if (!hasEditModeFunction && !hasIsDisabledFunction) {
      return step.formConfig;
    }

    const config = { ...step.formConfig };

    if (hasEditModeFunction) {
      config.editMode = (step.formConfig.editMode as Function)(this.stepperState);
    }

    if (hasIsDisabledFunction) {
      console.log(`Resolving isDisabled for step ${step.value} with this.stepperState.currentStep: `, this.stepperState.currentStep);
      config.isDisabled = (step.formConfig.isDisabled as Function)(this.stepperState);
      console.log(`Step ${step.value} isDisabled result: `, config.isDisabled);
      console.log(`Step ${step.value} mode: `, this.stepperState.stepModes[step.value]);
    }

    return config;
  }



  onFormChange(form: FormGroup, stepValue: number) {
    this.stepperState.forms[stepValue] = form;
    this.cdr.markForCheck();
  }



  executeAction(action: StepAction, activateCallback?: (step: number) => void) {
    // Find the current step's handler config
    const currentStep = this.stepperConfig.steps.find(s => s.value === this.stepperState.currentStep);
    const currentStepHandlerConfig = currentStep?.handlerConfig;
    console.log('currentStep ', currentStep);

    // Store current state to detect changes
    const previousStep = this.stepperState.currentStep;
    const previousStepMode = this.stepperState.stepModes[this.stepperState.currentStep];
    console.log('previousStep ', previousStep);

    // Map function name to actual function call
    const actionFunction = this.actionFunctionMap[action.onClick];
    if (actionFunction) {
      const result = actionFunction(activateCallback, currentStepHandlerConfig);
      if (result instanceof Promise) {
        // Handle async actions (like goNext)
        result.then(() => {
          this.checkAndResolveConfigs(previousStep, previousStepMode);
        }).catch(error => {
          console.error('Action failed:', error);
          this.checkAndResolveConfigs(previousStep, previousStepMode);
        });
      } else {
        // Handle sync actions (like goBack, enableEdit)
        this.checkAndResolveConfigs(previousStep, previousStepMode);
      }
    } else {
      console.error(`Action function '${action.onClick}' not found in function map`);
    }
  }

  private checkAndResolveConfigs(previousStep: number, previousStepMode: string) {
    // Re-resolve configs if step changed
    console.log('this.stepperState.currentStep ', this.stepperState.currentStep);
    console.log('previousStep ', previousStep);
    if (this.stepperState.currentStep !== previousStep) {
      this.resolveAllStepConfigs();
    }
    // If only step mode changed, update just the current step's resolved config
    else if (this.stepperState.stepModes[this.stepperState.currentStep] !== previousStepMode) {
      this.updateCurrentStepConfig();
    }

    this.cdr.markForCheck();
  }

  shouldShowAction(action: StepAction): boolean {
    const currentStep = this.stepperState.currentStep;

    // Check showOnSteps
    if (action.showOnSteps && !action.showOnSteps.includes(currentStep)) {
      return false;
    }

    // Check hideOnSteps
    if (action.hideOnSteps && action.hideOnSteps.includes(currentStep)) {
      return false;
    }

    return true;
  }

  isActionDisabled(action: StepAction): boolean {
    return action.disabled ? action.disabled(this.stepperState) : false;
  }

  isActionHidden(action: StepAction): boolean {
    return action.hidden ? action.hidden(this.stepperState) : false;
  }

  isActionLoading(action: StepAction): boolean {
    return action.loading ? action.loading(this.stepperState) : false;
  }

  getActionLabel(action: StepAction): string {
    return typeof action.label === 'function' ? action.label(this.stepperState) : (action.label || '');
  }

  getActionIcon(action: StepAction): string {
    return typeof action.icon === 'function' ? action.icon(this.stepperState) : (action.icon || '');
  }

  getLeftActions(): StepAction[] {
    return this.stepperConfig.actions.filter(action =>
      action.location === 'left' && this.shouldShowAction(action)
    );
  }

  getRightActions(): StepAction[] {
    return this.stepperConfig.actions.filter(action =>
      action.location === 'right' && this.shouldShowAction(action)
    );
  }

  getVisibleLeftActions(): StepAction[] {
    return this.getLeftActions().filter(action => !this.isActionHidden(action));
  }

  getVisibleRightActions(): StepAction[] {
    return this.getRightActions().filter(action => !this.isActionHidden(action));
  }

  getStepFormConfig(step: StepConfig): StepFormConfig | null {
    if (!step.formConfig) return null;
    return this.resolvedStepConfigs[step.value] || null;
  }

  // Helper methods for template to get resolved boolean values
  getStepEditMode(step: StepConfig): boolean {
    const resolvedConfig = this.resolvedStepConfigs[step.value];
    if (!resolvedConfig) return false;
    return (resolvedConfig.editMode as boolean) || false;
  }

  getStepIsDisabled(step: StepConfig): boolean {
    const resolvedConfig = this.resolvedStepConfigs[step.value];
    if (!resolvedConfig) return false;
    return (resolvedConfig.isDisabled as boolean) || false;
  }

  getStepInitialData(step: StepConfig): any {
    return this.stepperState.stepData[step.value] || this.stepperState.initialData;
  }

  onDialogHide() {
    this.hideDialog();
  }

  hideDialog() {

    // Check if there's partial data and show confirmation if configured
    if (this.stepperConfig.confirmOnClose && this.hasPartialData()) {
      const confirmMessage = this.getConfirmationMessage();

      this.confirmationService.confirm({
        message: confirmMessage,
        header: 'Xác nhận thoát',
        icon: 'pi pi-exclamation-triangle',
        acceptLabel: 'Có',
        rejectLabel: 'Không',
        accept: () => {
          this.visible = false;
          this.visibleChange.emit(false);
          this.onCancel.emit();
          this.resetStepperState();
          this.cdr.markForCheck();
        },
        reject: () => {
          // Do nothing - keep the stepper dialog open
        }
      });
    } else {
      this.visible = false;
      this.visibleChange.emit(false);
      this.onCancel.emit();
      this.cdr.markForCheck();
    }
  }

  private hasPartialData(): boolean {
    if (this.stepperConfig.hasPartialData) {
      return this.stepperConfig.hasPartialData(this.stepperState);
    }

    // Default check: if we're past step 1 and have step data
    return this.stepperState.currentStep > 1 &&
      Object.keys(this.stepperState.stepData).length > 0;
  }

  private getConfirmationMessage(): string {
    if (this.stepperConfig.exitConfirmationMessage) {
      return this.stepperConfig.exitConfirmationMessage(this.stepperState);
    }

    return 'Bạn có chắc chắn muốn thoát? Dữ liệu đã nhập sẽ bị mất.';
  }

  private resetStepperState() {
    this.stepperState = {
      currentStep: 1,
      forms: {},
      stepData: {},
      stepModes: {},
      isLoading: false,
      initialData: this.initialData
    };

    // Initialize step modes - all steps start in 'create' mode
    this.stepperConfig.steps.forEach(step => {
      this.stepperState.stepModes[step.value] = 'create';
    });
  }

  get dialogHeader(): string {
    return this.stepperConfig.header || 'Stepper';
  }

  get dialogWidth(): string {
    return this.stepperConfig.width || '650px';
  }

  get isLinear(): boolean {
    return this.stepperConfig.linear !== false;
  }
}
