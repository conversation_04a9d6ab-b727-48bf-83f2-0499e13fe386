import { inject } from '@angular/core';
import { EmployeePositionApiService } from '@shared/services';
import { DialogHandlerConfig, EditCreateDialogConfig } from '../edit-create-dialog.interfaces';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { employeePositionDialogFormConfig } from '@shared/components/edit-create-form';

export const employeePositionDialogConfig = (onSuccess: () => void, onCancel: () => void): EditCreateDialogConfig => {
  const employeePositionApiService = inject(EmployeePositionApiService);

  return {
    width: '650px',
    modal: true,
    dismissableMask: true,
    styleClass: 'p-fluid',
    formConfig: employeePositionDialogFormConfig(),
    actions: [defaultCancelAction, defaultConfirmAction],
    handlerConfig: {
      service: employeePositionApiService,
      createMethod: 'assignPositionToEmployee',
      updateMethod: 'updateEmployeePosition',
      entityLabel: 'lịch sử công tác',
      commonDataTransform: (formValue: any) => ({
        employeeId: formValue.employeeId,
        positionId: formValue.positionId,
        departmentId: formValue.departmentId,
        fromDate: formValue.fromDate
      }),
      updateDataTransform: (formValue: any) => ({
        id: formValue.id
      }),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id,
        fromDate: rawData.fromDate ? new Date(rawData.fromDate) : null
      }),
      onSuccess,
      onCancel
    } as DialogHandlerConfig
  };
};
