import { CommonModule } from '@angular/common';
import { Component, ContentChild, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, TemplateRef, ViewChild } from '@angular/core';
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { PopoverModule } from 'primeng/popover';
import { DefaultCancelButtonDirective, DefaultConfirmButtonDirective } from '@shared/directives/';
import { EditCreateFormComponent, FormConfig } from '@shared/components/edit-create-form';
import { PopoverConfig, PopoverHandlerConfig, PopoverAction } from './generic-popover.interfaces';
import { FormHandlerService } from '@shared/services';

@Component({
  selector: 'generic-popover',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    PopoverModule,
    ButtonModule,
    EditCreateFormComponent,
    DefaultCancelButtonDirective,
    DefaultConfirmButtonDirective
  ],
  templateUrl: './generic-popover.component.html'
})
export class GenericPopoverComponent implements OnInit, OnChanges {
  @Input() visible: boolean = false;
  @Input() popoverConfig: PopoverConfig = { formConfig: { fields: [] }, actions: [] };
  @Input() selectedData: any = null;

  @Output() onHide = new EventEmitter<void>();
  @Output() onSubmit = new EventEmitter<{ formValue: any }>();
  @Output() onFieldChange = new EventEmitter<{ fieldKey: string, value: any, formValue: any }>();

  // Template references for custom field templates
  @ContentChild('customFieldTemplate') customFieldTemplate?: TemplateRef<any>;
  @ViewChild('popover') popoverRef: any;

  formConfig: FormConfig = { fields: [] };
  transformedInitialData: any = null;
  form: FormGroup | null = null; // Reference to the form from edit-create-form

  // Function map for action onClick handlers
  actionFunctionMap: Record<string, (popoverConfig: PopoverHandlerConfig, formValue: any) => void | Promise<any>> = {};

  constructor(
    private formHandlerService: FormHandlerService
  ) {
    this.actionFunctionMap = {
      'cancel': (popoverHandlerConfig: PopoverHandlerConfig, formValue: any) => this.formHandlerService.handleCancel(popoverHandlerConfig),
      'save': (popoverHandlerConfig: PopoverHandlerConfig, formValue: any) => this.formHandlerService.handleSave({
        config: popoverHandlerConfig,
        formValue
      })
    };
  }

  ngOnInit() {
    this.loadFormConfig();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['popoverConfig'] && !changes['popoverConfig'].firstChange) {
      this.loadFormConfig();
    }
    if (changes['selectedData']) {
      this.transformInitialData();
    }
  }

  private loadFormConfig() {
    this.formConfig = this.popoverConfig.formConfig;
    this.transformInitialData();
  }

  private transformInitialData() {
    if (this.selectedData && this.popoverConfig.handlerConfig?.initialDataTransform) {
      this.transformedInitialData = this.popoverConfig.handlerConfig.initialDataTransform(this.selectedData);
    } else {
      this.transformedInitialData = this.selectedData;
    }
  }

  get popoverWidth(): string {
    return this.popoverConfig.width || '350px';
  }

  get popoverTitle(): string {
    return this.popoverConfig.title || '';
  }

  onFormChange(form: FormGroup) {
    this.form = form;
    this.onFieldChange.emit({
      fieldKey: '',
      value: null,
      formValue: form.value
    });
  }

  show(event?: Event) {
    if (this.popoverRef) {
      this.transformInitialData();
      this.popoverRef.show(event);
    }
  }

  hide() {
    if (this.popoverRef) {
      this.popoverRef.hide();
    }
  }

  onPopoverHide() {
    // Emits the onHide event to notify parent components
    this.onHide.emit();
  }

  executeAction(action: PopoverAction) {
      if (!this.form) {
        console.error('Form not available');
        return;
      }
      let handlerConfig = this.popoverConfig.handlerConfig;
      if (!handlerConfig) {
        console.error('Handler configuration is required');
        return
      }
      const actionFunction = this.actionFunctionMap[action.onClick];
      if (actionFunction) {
        const result = actionFunction(handlerConfig, this.form.getRawValue());
        if (result instanceof Promise) {
          result.catch(error => {
            console.error('Action failed:', error);
          });
        }
      }
    }
}
