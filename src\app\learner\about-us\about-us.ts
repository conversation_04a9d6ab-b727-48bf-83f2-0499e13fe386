import { CommonModule } from '@angular/common';
import { Component, ViewChild } from '@angular/core';
import { ErrorHandlerService, PartnerApiService, ToastService } from '@shared/services';
import { Carousel, CarouselModule } from 'primeng/carousel';

@Component({
    selector: 'app-about-us',
    standalone: true,
    imports: [CommonModule, CarouselModule],
    templateUrl: './about-us.html',
    styleUrls: ['./about-us.scss']
})
export class AboutUs {
    @ViewChild('carousel') carousel!: Carousel;

    listData: any[] = [];

    responsiveOptions = [
        {
            breakpoint: '1024px',
            numVisible: 6,
            numScroll: 1
        },
        {
            breakpoint: '768px',
            numVisible: 3,
            numScroll: 1
        },
        {
            breakpoint: '560px',
            numVisible: 2,
            numScroll: 1
        }
    ];

    private isDragging = false;
    private startX = 0;
    private scrollPosition = 0;

    constructor(
        private partnerApiService: PartnerApiService,
        private toastService: ToastService,
        private errorHandlerService: ErrorHandlerService
    ) {}

    async ngOnInit() {
        await this.fetchData();
    }

    ngAfterViewInit() {
        const carouselElement = document.querySelector('.custom-carousel .p-carousel-items-container') as HTMLElement;

        carouselElement.addEventListener('mousedown', this.onMouseDown.bind(this));
        carouselElement.addEventListener('mousemove', this.onMouseMove.bind(this));
        carouselElement.addEventListener('mouseup', this.onMouseUp.bind(this));
        carouselElement.addEventListener('mouseleave', this.onMouseUp.bind(this));
    }

    fetchData() {
        this.listData = [];
        this.partnerApiService.getAll().subscribe((response) => {
            const { hasError, data } = this.errorHandlerService.handleInternal(response);
            if (!hasError) {
                this.listData = data;
            }
        });
    }

    onMouseDown(event: MouseEvent) {
        this.isDragging = true;
        this.startX = event.pageX;
        this.scrollPosition = this.carousel.page;
    }

    onMouseMove(event: MouseEvent) {
        if (!this.isDragging) return;

        const diff = this.startX - event.pageX;

        if (diff > 50) {
            this.carousel.navForward(event);
            this.isDragging = false;
        } else if (diff < -50) {
            this.carousel.navBackward(event);
            this.isDragging = false;
        }
    }

    onMouseUp() {
        this.isDragging = false;
    }
}
