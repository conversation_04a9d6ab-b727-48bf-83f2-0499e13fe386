import { Injectable } from '@angular/core';
import { DialogEntityType, EditCreateDialogConfig, DialogConfigRegistry } from '@shared/components/edit-create-dialog';

// Cache to avoid re-importing the same config
const dialogConfigCache = new Map<string, EditCreateDialogConfig>();

// Module-level variables (shared across all imports of this module)
const dialogConfigRegistry: DialogConfigRegistry = {
  'employee': () => import('../components/edit-create-dialog/configs/employee.config').then(m => m.employeeDialogConfig),
  'position': () => import('../components/edit-create-dialog/configs/position.config').then(m => m.positionDialogConfig),
  'probation': () => import('../components/edit-create-dialog/configs/probation.config').then(m => m.probationDialogConfig),
  'employeePosition': () => import('../components/edit-create-dialog/configs/employee-position.config').then(m => m.employeePositionDialogConfig),
  // Add more configs here as needed - each will be lazy loaded
};



/**
 * Gets dialog configuration for a specific entity type
 * @param entityType The type of entity dialog to create
 * @param onSuccess Callback function called after successful save
 * @param onCancel Callback function called when dialog is cancelled
 * @returns Promise that resolves to complete dialog configuration
 */
export const getDialogConfig = async (
  entityType: DialogEntityType,
  onSuccess: () => void,
  onCancel: () => void
): Promise<EditCreateDialogConfig> => {
  // Create cache key to avoid re-creating configs with same callbacks
  const cacheKey = `${entityType}_${onSuccess.toString()}_${onCancel.toString()}`;

  if (dialogConfigCache.has(cacheKey)) {
    return dialogConfigCache.get(cacheKey)!;
  }

  const configFactory = dialogConfigRegistry[entityType];
  if (!configFactory) {
    throw new Error(`Unsupported entity type: ${entityType}. Available types: ${Object.keys(dialogConfigRegistry).join(', ')}`);
  }

  // Dynamically import and create the config
  const configFunction = await configFactory();
  const config = configFunction(onSuccess, onCancel);

  // Cache in module-level variable
  dialogConfigCache.set(cacheKey, config);
  return config;
};

/**
 * Optional service class for backward compatibility or DI scenarios
 * Delegates to standalone functions
 */
@Injectable({
  providedIn: 'root'
})
export class EditCreateDialogConfigService {
  async getDialogConfig(
    entityType: DialogEntityType,
    onSuccess: () => void,
    onCancel: () => void
  ): Promise<EditCreateDialogConfig> {
    return getDialogConfig(entityType, onSuccess, onCancel);
  }
}
