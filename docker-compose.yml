# version: '3.7'

# services:
#   ws-be:
#     build: ./
#     container_name: 'ws-be'
#     restart: always
#     env_file:
#       - ./.env
#     ports:
#       - '8800:8800'
#     networks:
#       - ws-shared-network
#     healthcheck:
#       test: ["CMD", "curl", "-f", "http://localhost:8800/health"]
#       interval: 30s
#       timeout: 10s
#       retries: 3
#       start_period: 30s
# networks:
#   ws-shared-network:
#     driver: bridge
#     name: ws-shared-network
