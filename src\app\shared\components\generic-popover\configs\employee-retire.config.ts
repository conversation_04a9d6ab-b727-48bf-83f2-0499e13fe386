import { inject } from '@angular/core';
import { EmployeePositionApiService } from '@shared/services';
import { PopoverHandlerConfig, PopoverConfig } from '../generic-popover.interfaces';
import { defaultCancelAction, defaultConfirmAction } from './default-actions';
import { retireEmployeePositionFormConfig } from '@shared/components/edit-create-form';

export const employeeRetirePopoverConfig = (onSuccess: () => void, onCancel: () => void): PopoverConfig => {
  const employeePositionApiService = inject(EmployeePositionApiService);

  const commonDataTransform = (formValue: any) => ({
    id: formValue.id,
    toDate: formValue.toDate
  });

  return {
    width: '350px',
    title: 'Nghỉ việc nhân viên',
    dismissable: true,
    styleClass: 'p-fluid',
    formConfig: retireEmployeePositionFormConfig(),
    actions: [
      defaultCancelAction,
      { ...defaultConfirmAction, label: 'Xác nhận', severity: 'warn' }
    ],
    handlerConfig: {
      service: employeePositionApiService,
      method: 'retireEmployeePosition',
      entityLabel: 'nghỉ việc nhân viên',
      dataTransform: (formValue: any) => commonDataTransform(formValue),
      initialDataTransform: (rawData: any) => ({
        id: rawData.id,
        employee: rawData.employee,
        position: rawData.position,
        toDate: null
      }),
      onSuccess,
      onCancel
    } as PopoverHandlerConfig
  };
};
