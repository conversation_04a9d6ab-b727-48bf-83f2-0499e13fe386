<div class="card">
    <h2>Component Playground</h2>
    <p class="text-muted-color mb-4">Test and experiment with various components here.</p>

    <!-- Test Probation Form Section -->
    <div class="mb-4">
        <h3>Form Testing</h3>
        <p-button label="Test Probation Form"
                  icon="pi pi-cog"
                  severity="info"
                  (onClick)="openTestForm()">
        </p-button>
    </div>

    <!-- Add more test sections here as needed -->
    <div class="mb-4">
        <h3>Future Components</h3>
        <p class="text-muted-color">Add more test components and functionality here...</p>
    </div>
</div>

<!-- Test Form Dialog -->
<p-dialog header="Test Probation Form Design"
          [(visible)]="testFormDialog"
          [modal]="true"
          [style]="{width: '600px'}"
          [dismissableMask]="true"
          styleClass="p-fluid">

    <edit-create-form [editMode]="false"
                      [initialData]="testFormData"
                      [isDisabled]="false"
                      [formConfig]="testFormConfig"
                      (formChange)="onTestFormChange($event)">
    </edit-create-form>

    <ng-template pTemplate="footer">
        <p-button label="Close"
                  icon="pi pi-times"
                  severity="secondary"
                  (onClick)="closeTestForm()">
        </p-button>
    </ng-template>
</p-dialog>