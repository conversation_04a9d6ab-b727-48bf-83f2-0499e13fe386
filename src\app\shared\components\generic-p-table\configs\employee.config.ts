import { inject } from '@angular/core';
import { EmployeeApiService } from '@shared/services';
import { TableConfig, PLACEHOLDER_FUNCTIONS } from '../generic-p-table.interfaces';

export const employeeTableConfig = (): TableConfig => {
  const employeeApiService = inject(EmployeeApiService);

  return {
    title: 'Quản lý nhân viên',
    entityName: 'nhân viên',
    service: employeeApiService,
    method: 'getEmployees',
    columns: [
      { field: 'code', header: 'Mã NV', width: 'min-w-[100px]', sortable: true, type: 'text' },
      { field: 'profile.fullname', header: 'Họ tên', width: 'min-w-[150px]', sortable: true, type: 'text' },
      { field: 'profile.nickname', header: 'Nickname', width: 'min-w-[100px]', sortable: false, type: 'text' },
      { field: 'profile.birthday', header: '<PERSON><PERSON><PERSON> sinh', width: 'min-w-[100px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'profile.phone', header: '<PERSON>i<PERSON><PERSON> thoại', width: 'min-w-[120px]', sortable: false, type: 'text' },
      { field: 'profile.email', header: 'Email', width: 'min-w-[100px]', sortable: false, type: 'text' },
      { field: 'status', header: 'Trạng thái', width: 'min-w-[100px]', sortable: true, type: 'tag', tagType: 'employeeStatus' },
      { field: 'employeePositions', header: 'Vị trí', width: 'min-w-[350px]', sortable: true, type: 'custom' },
      { field: 'fromDate', header: 'First onb', width: 'min-w-[100px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'toDate', header: 'Last LWD', width: 'min-w-[100px]', sortable: true, type: 'date', dateFormat: 'dd/MM/yyyy' },
      { field: 'note', header: 'Ghi chú', width: 'min-w-[200px]', sortable: false, type: 'text' }
    ],
    features: {
      virtualScroll: true,
      toolbar: true,
      selection: {
        mode: 'single',
        metaKeySelection: false,
        dataKey: 'id',
        onSelect: PLACEHOLDER_FUNCTIONS.ON_SELECT,
        onUnselect: PLACEHOLDER_FUNCTIONS.ON_UNSELECT
      }
    },
    actions: [
      {
        location: 'toolbar',
        useDefaultStyle: 'add',
        onClick: PLACEHOLDER_FUNCTIONS.OPEN_NEW
      },
      {
        location: 'row',
        useDefaultStyle: 'edit',
        onClick: PLACEHOLDER_FUNCTIONS.EDIT
      },
      {
        location: 'row',
        useDefaultStyle: 'delete',
        onClick: PLACEHOLDER_FUNCTIONS.DELETE,
        confirmMessage: 'Bạn có chắc chắn muốn xóa nhân viên này?'
      }
    ]
  };
};
