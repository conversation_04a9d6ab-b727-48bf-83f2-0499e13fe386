---
- name: Deploy Portal Backend
  hosts: all
  gather_facts: no
  become: yes
  vars:
    app_dir: "{{ app_dir }}"
    branch: "{{ branch }}"
  tasks:
    - name: Ensuring {{ app_dir }} exists... 
      # become: yes
      file:
        path: "{{ app_dir }}"
        state: directory
        # owner: gitlab-runner
        # group: gitlab-runner
        mode: '0755'

    - name: Git pulling latest code...
      # become: no
      # become_user: gitlab-runner
      git:
        repo: "{{ lookup('env', 'CI_REPOSITORY_URL') }}"
        dest: "{{ app_dir }}"
        version: "{{ branch }}"
        force: yes

    - name: Copying Docker Compose template...
      # become: no
      # become_user: gitlab-runner
      template:
        src: templates/docker-compose.j2
        dest: "{{ app_dir }}/docker-compose.yml"
        mode: '0644'

    - name: Building images and capturing logs...
      shell: docker compose build
      args:
        chdir: "{{ app_dir }}"
      register: docker_build_log
      environment:
        COMPOSE_HTTP_TIMEOUT: 360
        DOCKER_BUILDKIT: 1

    - name: Docker build logs
      # debug:
      #   var: docker_build_log.stdout_lines
      vars:
        cleaned_log: >-
          {{ docker_build_log.stdout_lines
            | select('match','\S')
            | map('regex_replace','^\s*#\d+\s+','')
            | map('regex_replace','\[.*?\]\|\|\s*','')
            | list }}
      debug:
        var: cleaned_log

    - name: Starting containers in detached mode...
      # become: yes
      command: docker compose up -d
      args:
        chdir: "{{ app_dir }}"
      