import { PERMISSIONS } from '@app/constants';
import { Logged, Permissions } from '@app/decorators';
import { JwtGuard, PermissionsGuard } from '@app/guards';
import { DepartmentDTO, JwtPayloadDTO, SearchOptionsDTO } from '@app/models/dto';
import { PaginationRequest } from '@app/models/pagination';
import { DepartmentService } from '@app/services/department.service';
import { CustomValidationPipe, PaginationPipe } from '@app/shared/pipes';
import { Body, Controller, Delete, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards } from '@nestjs/common';

@UseGuards(JwtGuard)
@Controller('departments')
export class DepartmentController {
    constructor(private departmentService: DepartmentService) {}

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.DEPARTMENT_VIEW.code)
    @Get()
    async getWithPagination(@Query(new PaginationPipe()) request: PaginationRequest) {
        return await this.departmentService.findWithPagination(request);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.EMPLOYEE_VIEW.code)
    @Get('root')
    async getRootDepartment() {
        return await this.departmentService.getRoots();
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.DEPARTMENT_VIEW.code)
    @Get('all')
    async getAllDepartments() {
        return await this.departmentService.getAllDepartments();
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.DEPARTMENT_EDIT.code)
    @Post()
    async createDepartment(@Body(new CustomValidationPipe()) dto: DepartmentDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.departmentService.createDepartment(dto, logged);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.DEPARTMENT_VIEW.code)
    @Post('options')
    async getOptions(@Query(new PaginationPipe()) request: PaginationRequest, @Body() body: SearchOptionsDTO) {
        return await this.departmentService.getDepartmentOptions(request, body);
    }

    @HttpCode(HttpStatus.OK)
    @UseGuards(PermissionsGuard)
    @Permissions(PERMISSIONS.DEPARTMENT_EDIT.code)
    @Put()
    async updateDepartment(@Body(new CustomValidationPipe()) dto: DepartmentDTO, @Logged() logged: JwtPayloadDTO) {
        return await this.departmentService.updateDepartment(dto, logged);
    }

    // @HttpCode(HttpStatus.OK)
    // @UseGuards(PermissionsGuard)
    // @Permissions(PERMISSIONS.EMPLOYEE_EDIT.code)
    // @Delete()
    // async deleteDepartment(@Param('code') code: string, @Logged() logged: JwtPayloadDTO) {
    //     return await this.departmentService.deleteDepartment(code, logged);
    // }
}
