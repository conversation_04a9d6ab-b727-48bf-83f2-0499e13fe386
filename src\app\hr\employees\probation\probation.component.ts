import { CommonModule } from '@angular/common';
import { Component, OnInit, ViewChild } from '@angular/core';
import { EditCreateDialogComponent, EditCreateDialogConfig, probationDialogConfig } from '@shared/components/edit-create-dialog';
import {
    GenericPTableComponent,
    TableConfig,
    TableDataLoadEvent,
    mapFunctions,
    probationTableConfig,
} from '@shared/components/generic-p-table';
import { ConfirmationService, MessageService } from 'primeng/api';

@Component({
    selector: 'app-probation',
    standalone: true,
    imports: [
        CommonModule,
        EditCreateDialogComponent,
        GenericPTableComponent
    ],
    providers: [ConfirmationService, MessageService],
    templateUrl: './probation.component.html',
    styleUrl: './probation.component.scss'
})
export class ProbationComponent implements OnInit {
    // Table & dialog configuration - will be initialized in constructor
    probationLazyScrollPTableConfig!: TableConfig;
    dialogConfig!: EditCreateDialogConfig;

    // Data properties - now managed by table component
    probations: any[] = [];
    totalRecords: number = 0;

    // Dialog and form properties
    probationDialog: boolean = false;
    editMode: boolean = false;
    selectedProbation: any = null;



    // ViewChild for table
    @ViewChild(GenericPTableComponent) tableComponent!: GenericPTableComponent;

    constructor(
    ) {
        // Initialize table configuration using direct import
        this.probationLazyScrollPTableConfig = mapFunctions(probationTableConfig(), {
            OPEN_NEW: () => this.openNew(),
            EDIT: (item: any) => this.editProbation(item)
        });
        // Initialize dialog configuration using direct import
        this.dialogConfig = probationDialogConfig(
            () => this.onDialogSuccess(),
            () => this.onDialogCancel()
        );
    }

    ngOnInit() {
    }

    // Table functions
    onTableDataLoaded(data: TableDataLoadEvent) {
        this.probations = data.items;
        this.totalRecords = data.totalRecords;
    }

    openNew() {
        this.editMode = false;
        this.selectedProbation = null;
        this.probationDialog = true;
    }

    editProbation(probation: any) {
        this.editMode = true;
        this.selectedProbation = probation;
        this.probationDialog = true;
    }

    // Dialog functions
    onDialogSuccess() {
        this.probationDialog = false;
        this.selectedProbation = null;
        this.tableComponent.refresh();
    }

    onDialogCancel() {
        this.probationDialog = false;
    }
}
