import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequestHelper } from '@shared/helpers/request.helper';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class PositionApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/positions`;
    }

    getPositions(req: PaginationRequest): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.get<any>(this.url, { params: options });
    }

    createPosition(position: any): Observable<any> {
        return this.httpClient.post<any>(this.url, position);
    }

    updatePosition(position: any): Observable<any> {
        return this.httpClient.put<any>(this.url, position);
    }

    deletePosition(id: string): Observable<any> {
        return this.httpClient.delete<any>(`${this.url}/${id}`);
    }

    getPositionOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }
}
