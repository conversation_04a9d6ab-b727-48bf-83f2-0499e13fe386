import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { RequestHelper } from '@shared/helpers/request.helper';
import { PaginationRequest } from './models/pagination/pagination.request.model';
import { EnvironmentService } from './environment.service';

@Injectable({ providedIn: 'root' })
export class CourseApiService {
    constructor(
        private httpClient: HttpClient,
        private environmentService: EnvironmentService
    ) {}

    private get url(): string {
        return `${this.environmentService.getCurrentApiUrl()}/courses`;
    }

    getCourseOptions(req: PaginationRequest, body?: any): Observable<any> {
        const options = RequestHelper.createRequestOption(req);
        return this.httpClient.post<any>(`${this.url}/options`, body || {}, { params: options });
    }
}
