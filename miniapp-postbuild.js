const fs = require('fs');
const path = require('path');
const fse = require('fs-extra');

// Patch asset paths - add '/' at beginning where there is none

const DIST_DIR = path.resolve(__dirname, 'dist/weset-fe/browser');

const shouldPatch = (val) =>
  val.startsWith('assets/') &&
  !val.startsWith('/assets/') &&
  !val.startsWith('http') &&
  !val.startsWith('data:');

const patchFile = (filePath) => {
  let content = fs.readFileSync(filePath, 'utf8');

  // Replace ["src", "assets/..."] or ["href", "assets/..."]
  content = content.replace(
    /(\["(?:src|href)",\s*")([^"]+)("\])/g,
    (_, prefix, val, suffix) =>
      shouldPatch(val) ? `${prefix}/${val}${suffix}` : `${prefix}${val}${suffix}`
  );

  // Also patch raw "assets/..." strings in case they're standalone
  content = content.replace(
    /(["'])assets\/(?!\/)([^"']+\1)/g,
    (_, quote, rest) => `${quote}/assets/${rest}`
  );

  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ Patched: ${path.basename(filePath)}`);
};

const files = fs.readdirSync(DIST_DIR);

files
  .filter((f) => f.endsWith('.js'))
  .forEach((f) => patchFile(path.join(DIST_DIR, f)));

console.log('✅ All .js files patched for asset path prefixing.');





// Copy angular build to weset-fe-miniapp/public
const src = path.resolve(__dirname, 'dist/weset-fe/browser');
const dest = path.resolve(__dirname, '../weset-fe-miniapp/public'); // ✅ Your case

fse.emptyDirSync(dest);
fse.copySync(src, dest);

console.log('✅ Angular build copied to weset-fe-miniapp/public');