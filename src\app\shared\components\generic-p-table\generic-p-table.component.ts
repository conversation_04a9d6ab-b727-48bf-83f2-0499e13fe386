import { CommonModule } from '@angular/common';
import { Component, ContentChild, EventEmitter, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { GenericTagComponent } from '@shared/components/generic-tag/generic-tag.component';
import { PageConstant } from '@shared/constants';
import { DefaultLazyScrollTableDirective } from '@shared/directives';
import { ErrorHandlerService } from '@shared/services';
import { PaginationRequest } from '@shared/services';
import { ISort, SORT_VAL } from '@shared/services';
import { ConfirmationService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { Table, TableLazyLoadEvent, TableModule } from 'primeng/table';
import { ToolbarModule } from 'primeng/toolbar';
import { TooltipModule } from 'primeng/tooltip';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
    TableConfig,
    TableDataLoadEvent,
    TableLazyLoadParams
} from './generic-p-table.interfaces';

@Component({
    selector: 'generic-p-table',
    standalone: true,
    imports: [
        CommonModule,
        TableModule,
        ButtonModule,
        ToolbarModule,
        TooltipModule,
        ConfirmDialogModule,
        GenericTagComponent,
        DefaultLazyScrollTableDirective
    ],
    providers: [ConfirmationService],
    templateUrl: './generic-p-table.component.html',
    styleUrls: ['./generic-p-table.component.scss']
})
export class GenericPTableComponent<T = any> implements OnInit, OnDestroy {
    @Input() config!: TableConfig<T>;
    @Input() data: T[] = [];
    @Input() totalRecords: number = 0;
    @Input() selection: T | T[] | null = null;

    @Output() onDataLoad = new EventEmitter<TableDataLoadEvent>();
    @Output() onLazyLoad = new EventEmitter<TableLazyLoadParams>();
    @Output() selectionChange = new EventEmitter<T | T[] | null>();

    // Internal loading state
    loading: boolean = false;

    // Content projection for custom templates
    @ContentChild('customActions') customActionsTemplate?: TemplateRef<any>;
    @ContentChild('toolbarActions') toolbarActionsTemplate?: TemplateRef<any>;
    @ContentChild('customColumnTemplate') customColumnTemplate?: TemplateRef<any>;

    @ViewChild('dataTable') dataTable!: Table;

    // Internal state
    private destroy$ = new Subject<void>();
    
    // Pagination and sorting state
    itemPerPage: number = PageConstant.ITEM_PER_PAGE;
    itemPerPageOptions: number[] = PageConstant.ITEM_PER_PAGE_OPTIONS;
    offset: number = 0;
    sort: ISort = { field: null, order: 1 };

    constructor(
        private errorHandlerService: ErrorHandlerService,
        private confirmationService: ConfirmationService
    ) {}

    ngOnInit() {
        if (!this.config) {
            throw new Error('LazyscrollPTableComponent requires a config input');
        }

        // Set default values from config
        if (this.config.itemsPerPage) {
            this.itemPerPage = this.config.itemsPerPage;
        }
        if (this.config.itemsPerPageOptions) {
            this.itemPerPageOptions = this.config.itemsPerPageOptions;
        }
    }

    ngOnDestroy() {
        this.destroy$.next();
        this.destroy$.complete();
    }

    onTableLazyLoad(event: TableLazyLoadEvent) {
        const newOffset = event.first || 0;
        const newItemPerPage = event.rows || this.itemPerPage;
        const newSort = {
            field: event.sortField,
            order: event.sortOrder || 1
        };

        // Check if this is a sort/filter change (reset data) or just pagination (append data)
        const isSortChange = this.sort.field !== newSort.field || this.sort.order !== newSort.order;
        const isPageSizeChange = this.itemPerPage !== newItemPerPage;
        const resetData = isSortChange || isPageSizeChange || newOffset === 0;

        // Check for gaps when not resetting data (virtual scroll gap detection)
        if (!resetData && this.config.features?.virtualScroll) {
            const gap = newOffset - this.offset;
            if (gap > this.itemPerPage) {
                // Request the missing in-between page first
                console.log('Gap detected, fetching missing page...', this.offset + '-' + newOffset);
                const missingOffset = this.offset + this.itemPerPage;
                this.loadData({
                    first: missingOffset,
                    rows: this.itemPerPage,
                    sortField: event.sortField,
                    sortOrder: event.sortOrder
                }, false);
            }
        }

        this.offset = newOffset;
        this.itemPerPage = newItemPerPage;
        this.sort = newSort;

        // Emit the lazy load event for parent component to handle if needed
        this.onLazyLoad.emit({
            first: newOffset,
            rows: newItemPerPage,
            sortField: event.sortField,
            sortOrder: event.sortOrder
        });

        // Load data
        this.loadData({
            first: newOffset,
            rows: newItemPerPage,
            sortField: event.sortField,
            sortOrder: event.sortOrder
        }, resetData, event);
    }

    private loadData(params: TableLazyLoadParams, resetData: boolean = false, event?: TableLazyLoadEvent) {
        this.loading = true;

        // Build request parameters
        const requestParams: PaginationRequest = {
            limit: params.rows,
            offset: params.first,
            sort: params.sortField ? `${params.sortField},${SORT_VAL[params.sortOrder?.toString() || '1']}` : '',
            query: this.config.queryBuilder ? this.config.queryBuilder(params) : null,
            ...this.config.additionalParams
        };

        // Call the service method dynamically
        const serviceMethod = this.config.service[this.config.method];
        if (!serviceMethod) {
            console.error(`Method ${this.config.method} not found on service`);
            this.loading = false;
            return;
        }

        serviceMethod.call(this.config.service, requestParams)
            .pipe(takeUntil(this.destroy$))
            .subscribe({
                next: (response: any) => {
                    // Apply response transformation if provided
                    const transformedResponse = this.config.transformResponse
                        ? this.config.transformResponse(response)
                        : response;

                    const { hasError, data } = this.errorHandlerService.handleInternal(transformedResponse);
                    if (!hasError) {
                        if (resetData) {
                            this.totalRecords = data.total;
                            this.data = this.config.features?.virtualScroll ? Array(data.total) : data.items || [];
                        }

                        if (this.config.features?.virtualScroll) {
                            // For virtual scroll, patch items into the right slots
                            const targetOffset = params.first;
                            const targetLimit = params.rows;
                            this.data.splice(targetOffset, targetLimit, ...data.items);

                            // Tell the table to re-render its rows
                            if (event?.forceUpdate) {
                                event.forceUpdate();
                            }
                        } else {
                            // For pagination, replace all data
                            this.data = data.items || [];
                            this.totalRecords = data.total;
                        }

                        // Emit data load event
                        this.onDataLoad.emit({
                            items: this.data,
                            totalRecords: this.totalRecords,
                            loading: false
                        });
                    }
                    this.loading = false;
                },
                error: () => {
                    this.loading = false;
                    this.onDataLoad.emit({
                        items: this.data,
                        totalRecords: this.totalRecords,
                        loading: false
                    });
                }
            });
    }

    // Action handlers - now execute embedded functions directly
    executeAction(action: any, item: T | undefined = undefined, event?: Event) {
        if (action.confirmMessage) {
            this.confirmationService.confirm({
                message: action.confirmMessage,
                header: 'Xác nhận',
                icon: 'pi pi-exclamation-triangle',
                accept: () => {
                    const result = action.onClick(item, event);
                    if (result instanceof Promise) {
                        result.catch(error => {
                            console.error('Action failed:', error);
                        });
                    }
                }
            });
        } else {
            const result = action.onClick(item, event);
            if (result instanceof Promise) {
                result.catch(error => {
                    console.error('Action failed:', error);
                });
            }
        }
    }

    // Utility methods
    getFieldValue(item: any, field: string): any {
        return field.split('.').reduce((obj, key) => obj?.[key], item);
    }

    get hasActions(): boolean {
        return !!(this.getRowActions().length || this.customActionsTemplate);
    }

    get hasToolbarActions(): boolean {
        return !!this.getToolbarActions().length;
    }

    getToolbarActions(): any[] {
        return this.config.actions?.filter(action => action.location === 'toolbar') || [];
    }

    getRowActions(): any[] {
        return this.config.actions?.filter(action => action.location === 'row') || [];
    }

    get virtualScrollItemSize(): number {
        return this.config.virtualScrollItemSize || 46.75;
    }

    get virtualScrollOptions(): any {
        return this.config.virtualScrollOptions || { numToleratedItems: 5 };
    }

    // Public methods
    reload() {
        this.onTableLazyLoad({
            first: 0,
            rows: this.itemPerPage,
            sortField: this.sort.field,
            sortOrder: this.sort.order
        });
    }

    refresh() {
        this.onTableLazyLoad({
            first: this.offset,
            rows: this.itemPerPage,
            sortField: this.sort.field,
            sortOrder: this.sort.order
        });
    }

    // Selection event handlers
    onTableRowSelect(event: any) {
        // For single selection, event.data is the selected item
        const selectedItem = event.data as T;
        this.selection = selectedItem;
        this.selectionChange.emit(this.selection);

        // Call config selection handler if provided
        if (this.config.features?.selection?.onSelect) {
            this.config.features.selection.onSelect(selectedItem, event.originalEvent);
        }
    }

    onTableRowUnselect(event: any) {
        // For unselect, event.data is the unselected item
        const unselectedItem = event.data as T;
        this.selection = null;
        this.selectionChange.emit(this.selection);

        // Call config selection handler if provided
        if (this.config.features?.selection?.onUnselect) {
            this.config.features.selection.onUnselect(unselectedItem, event.originalEvent);
        }
    }

    // Selection configuration getters
    get selectionMode(): "single" | "multiple" | null | undefined {
        const mode = this.config.features?.selection?.mode;
        return mode === 'none' ? null : mode;
    }

    get metaKeySelection(): boolean {
        return this.config.features?.selection?.metaKeySelection ?? false;
    }

    get dataKey(): string {
        return this.config.features?.selection?.dataKey || 'id';
    }

    get hasSelection(): boolean {
        return this.config.features?.selection?.mode !== 'none' &&
               this.config.features?.selection?.mode !== undefined;
    }
}
