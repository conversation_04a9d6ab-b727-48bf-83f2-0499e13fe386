import { Prop } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export class Audit extends Document {
    @Prop({ index: true, default: null })
    createdAt: Date;
    @Prop({ index: true, default: null })
    createdBy: string;
    @Prop({ index: true, default: null })
    updatedAt: Date;
    @Prop({ index: true, default: null })
    updatedBy: string;
    @Prop({ index: true, default: null })
    deletedAt: Date;
    @Prop({ index: true, default: null })
    deletedBy: string;
}
