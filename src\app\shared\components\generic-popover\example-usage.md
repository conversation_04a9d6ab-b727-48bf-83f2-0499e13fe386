# Generic Popover Component - Usage Examples

## Treeshakeable, Modular Architecture

The Generic Popover system now follows the same treeshakeable, modular approach as edit-create-dialog with:
- Separate interface files for type safety
- Individual config files in configs folder with index.ts barrel exports
- Lazy-loading config service
- Dual usage patterns (config service or direct import)

## Usage Pattern 1: Config Service (Lazy Loading)

For common operations like employee retirement, use the `PopoverConfigService` which lazy-loads configurations:

```typescript
export class MyComponent {
  retirePopoverConfig!: PopoverConfig;

  constructor(private popoverConfigService: PopoverConfigService) {}

  async ngOnInit() {
    // Lazy-loaded configuration
    this.retirePopoverConfig = await this.popoverConfigService.getPopoverConfig(
      'employeeRetire',
      () => this.onPopoverSuccess(),
      () => this.onPopoverCancel()
    );
  }

  onPopoverSuccess() {
    this.selectedItem = null;
    this.reloadTable();
  }

  onPopoverCancel() {
    this.selectedItem = null;
  }
}
```

## Usage Pattern 2: Direct Import (Tree-shakeable)

For better tree-shaking and when you only need specific configs:

```typescript
import { GenericPopoverComponent, PopoverConfig, employeeRetirePopoverConfig } from '@shared/components/generic-popover';

export class MyComponent {
  retirePopoverConfig: PopoverConfig;

  constructor() {
    // Direct configuration import - only bundles what you use
    this.retirePopoverConfig = employeeRetirePopoverConfig(
      () => this.onPopoverSuccess(),
      () => this.onPopoverCancel()
    );
  }

  onPopoverSuccess() {
    this.selectedItem = null;
    this.reloadTable();
  }

  onPopoverCancel() {
    this.selectedItem = null;
  }
}
```

Template (same for both patterns):
```html
<generic-popover #myPopover
                 [popoverConfig]="retirePopoverConfig"
                 [selectedData]="selectedItem">
</generic-popover>
```

## Architecture Benefits

### Tree-shaking and Bundle Size
- **Selective Imports**: Only import the configs you actually use
- **Lazy Loading**: Config service loads configurations on-demand
- **Modular Structure**: Each config is in its own file for better code splitting

### Type Safety
- **Dedicated Interfaces**: `PopoverConfig`, `PopoverAction`, `PopoverHandlerConfig`
- **Entity Types**: `PopoverEntityType` for supported popover types
- **Factory Functions**: `PopoverConfigFactory` for consistent config creation

### Scalability
- **Easy to Add**: New configs just need a file in configs folder and registry entry
- **Consistent Pattern**: Same approach as edit-create-dialog for familiarity
- **No Circular Dependencies**: Clean import structure

## Manual Configuration Pattern

For custom popovers not covered by predefined configs:

```typescript
import { GenericPopoverComponent, PopoverConfig } from '@shared/components/generic-popover';

@Component({
  imports: [GenericPopoverComponent, ...],
  // ...
})
export class MyComponent {
  selectedItem: any = null;

  popoverConfig: PopoverConfig = {
    width: '350px',
    title: 'Action Title',
    dismissable: true,
    formConfig: {
      fields: [
        {
          key: 'employee',
          label: 'Employee',
          type: 'static',
          value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
        },
        {
          key: 'position',
          label: 'Position',
          type: 'static',
          value: (data: any) => data.position?.name || ''
        },
        {
          key: 'date',
          label: 'Date',
          type: 'datepicker',
          required: true,
          placeholder: 'Select date'
        },
        {
          key: 'reason',
          label: 'Reason',
          type: 'textarea',
          rows: 3,
          placeholder: 'Enter reason'
        }
      ]
    },
    actions: [
      { onClick: (popoverConfig: PopoverConfig, formValue: any) => this.handleCancel(), useDefaultStyle: 'cancel' },
      {
        onClick: (popoverConfig: PopoverConfig, formValue: any) => this.handleConfirm(formValue),
        disabled: (formValue: any, formValid: boolean) => !formValid,
        useDefaultStyle: 'confirm',
        label: 'Confirm',
        severity: 'primary'
      }
    ]
  };
}
```

### 2. Template Usage

```html
<generic-popover #myPopover
                 [popoverConfig]="popoverConfig"
                 [selectedData]="selectedItem"
                 (onHide)="handleHide()">
</generic-popover>
```

### 3. Triggering the Popover

```typescript
// In your component
@ViewChild('myPopover') myPopover!: GenericPopoverComponent;

showPopover(item: any, event?: Event) {
  this.selectedItem = item;
  this.myPopover.show(event);
}

handleHide() {
  this.selectedItem = null;
}

handleConfirm(formValue: any) {
  // Process the form data
  console.log('Form value:', formValue);
  this.myPopover.hide();
}
```

## Field Types

The generic popover now uses edit-create-form internally and supports all the same field types:

### Static Display Field
```typescript
{
  key: 'employee',
  label: 'Employee',
  type: 'static',
  value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
}
```

### Text Input
```typescript
{
  key: 'name',
  label: 'Name',
  type: 'text',
  required: true,
  placeholder: 'Enter name'
}
```

### Textarea
```typescript
{
  key: 'description',
  label: 'Description',
  type: 'textarea',
  rows: 4,
  placeholder: 'Enter description'
}
```

### Date Picker
```typescript
{
  key: 'date',
  label: 'Date',
  type: 'date',
  required: true,
  placeholder: 'Select date'
}
```

### Checkbox
```typescript
{
  key: 'active',
  label: 'Active',
  type: 'checkbox'
}
```

### Generic Select
```typescript
{
  key: 'categoryId',
  label: 'Category',
  type: 'select',
  required: true,
  config: CATEGORY_SELECT_CONFIG
}
```

### Generic Tree Select
```typescript
{
  key: 'departmentId',
  label: 'Department',
  type: 'treeselect',
  required: true,
  config: DEPARTMENT_TREE_SELECT_CONFIG
}
```

## Hidden Fields

Fields can be hidden from the UI while still being part of the form. This is useful for ID fields needed for operations but not visible to users.

```typescript
// Always hidden field (e.g., for ID fields)
{
  key: 'id',
  label: 'ID',
  type: 'text',
  hidden: true
}

// Conditionally hidden field
{
  key: 'internalNotes',
  label: 'Internal Notes',
  type: 'textarea',
  hidden: (formValue) => !formValue.isAdmin // Hidden for non-admin users
}
```

**Note:** Hidden fields are still included in form validation and data submission. They're only hidden from the UI.

## Static Display Fields

Use `type: 'static'` fields to show information about the selected item:

```typescript
formConfig: {
  fields: [
    {
      key: 'employee',
      label: 'Employee',
      type: 'static',
      value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
    },
    {
      key: 'status',
      label: 'Current Status',
      type: 'static',
      value: (data: any) => data.status || 'Unknown'
    },
    // ... other form fields
  ]
}
```

## Actions

### Default Styled Actions
```typescript
actions: [
  { onClick: (popoverConfig, formValue) => this.cancel(), useDefaultStyle: 'cancel' },
  {
    onClick: (popoverConfig, formValue) => this.confirm(formValue),
    useDefaultStyle: 'confirm',
    label: 'Save',
    severity: 'primary'
  }
]
```

### Custom Actions
```typescript
actions: [
  {
    label: 'Custom Action',
    icon: 'pi pi-check',
    severity: 'success',
    onClick: (popoverConfig, formValue) => this.customAction(formValue),
    disabled: (formValue, formValid) => !formValid
  }
]
```

## Real-world Example: Retire Employee Popover

```typescript
retirePopoverConfig: PopoverConfig = {
  width: '350px',
  title: 'Retire Employee',
  dismissable: true,
  formConfig: {
    fields: [
      {
        key: 'id',
        label: 'ID',
        type: 'text',
        hidden: true  // Hidden field for employee position ID
      },
      {
        key: 'employee',
        label: 'Employee',
        type: 'static',
        value: (data: any) => `${data.employee?.code} - ${data.employee?.name}`
      },
      {
        key: 'position',
        label: 'Position',
        type: 'static',
        value: (data: any) => data.position?.name || ''
      },
      {
        key: 'toDate',
        label: 'Retirement Date',
        type: 'datepicker',
        required: true,
        placeholder: 'Select retirement date'
      }
    ]
  },
  actions: [
    {
      onClick: (popoverConfig: PopoverConfig, formValue: any) => this.hideRetirePopover(),
      useDefaultStyle: 'cancel'
    },
    {
      onClick: (popoverConfig: PopoverConfig, formValue: any) => this.confirmRetireEmployee(formValue),
      disabled: (_formValue: any, formValid: boolean) => !formValid,
      useDefaultStyle: 'confirm',
      label: 'Confirm',
      severity: 'warn'
    }
  ]
};
```

## Configuration Options

### PopoverConfig Interface
```typescript
interface PopoverConfig {
  width?: string;                    // Default: '350px'
  title?: string;                    // Popover title
  dismissable?: boolean;             // Default: true
  styleClass?: string;               // Custom CSS class
  formConfig: FormConfig;            // Form configuration object (required)
  actions: PopoverAction[];          // Action buttons (required)
  handlerConfig?: PopoverHandlerConfig; // Optional - for automatic operation handling
}

interface FormConfig {
  fields: FormFieldConfig[];         // Form fields configuration
}

interface FormFieldConfig {
  key: string;
  label: string | null;
  type: 'text' | 'textarea' | 'datepicker' | 'checkbox' | 'toggleSwitch' | 'select' | 'treeselect' | 'static' | 'custom';
  required?: boolean;
  disabled?: boolean | ((editMode: boolean, formValue: any) => boolean);
  hidden?: boolean | ((editMode: boolean, formValue: any) => boolean);
  width?: 'full' | 'half';
  rows?: number;                     // For textarea
  placeholder?: string;
  validators?: ValidatorFn[];
  config?: any;                      // For select/treeselect configurations
  showDividerAfter?: boolean;
  value?: (data: any) => string;     // For static fields to display computed values
}
```

This pattern keeps your components clean and makes popover functionality reusable across your application.
