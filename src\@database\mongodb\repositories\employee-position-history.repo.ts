import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { EmployeePositionHistoryDocument } from '../schemas';

@Injectable()
export class EmployeePositionHistoryRepository extends GenericRepository<EmployeePositionHistoryDocument> {
    private readonly context = EmployeePositionHistoryRepository.name;

    constructor(
        @Inject(MONGO_CONST.EMPLOYEE_POSITION_HISTORY_COLLECTION)
        private readonly employeePositionHistoryModel: Model<EmployeePositionHistoryDocument>,
    ) {
        super(employeePositionHistoryModel);
    }
}
