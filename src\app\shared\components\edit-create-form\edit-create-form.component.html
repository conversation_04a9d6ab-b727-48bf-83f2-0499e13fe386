<form [formGroup]="form">
  <!-- Field Template Definition -->
  <ng-template #fieldTemplate let-field="field" let-showLabel="showLabel">
    <!-- Label when provided and showLabel is true and labelPosition is default -->
    <label *ngIf="field.label && showLabel && field.labelPosition !== 'inline'" [for]="field.key" class="block text-sm font-medium text-gray-700">
      {{ field.label }}
      <span *ngIf="field.required" class="text-red-500">*</span>
    </label>

    <!-- Generic Select Field -->
    <div *ngIf="field.type === 'select'" [class]="field.labelPosition === 'inline' ? 'flex items-center gap-2' : ''">
      <label *ngIf="field.label && showLabel && field.labelPosition === 'inline'" [for]="field.key" class="text-sm font-bold text-gray-700 whitespace-nowrap">
        {{ field.label }}<span *ngIf="field.required" class="text-red-500">*</span>:
      </label>
      <generic-p-select [id]="field.key"
                        [formControlName]="field.key"
                        [config]="field.config"
                        [disabled]="isFieldDisabled(field)"
                        [dependencyValue]="getDependencyValue(field)"
                        [placeholder]="field.placeholder || ''"
                        [pTooltip]="getFieldTooltip(field)"
                        [class]="field.labelPosition === 'inline' ? 'flex-1' : ''">
      </generic-p-select>
    </div>

    <!-- Generic Tree Select Field -->
    <div *ngIf="field.type === 'treeselect'" [class]="field.labelPosition === 'inline' ? 'flex items-center gap-2' : ''">
      <label *ngIf="field.label && showLabel && field.labelPosition === 'inline'" [for]="field.key" class="text-sm font-bold text-gray-700 whitespace-nowrap">
        {{ field.label }}<span *ngIf="field.required" class="text-red-500">*</span>:
      </label>
      <generic-p-treeselect [id]="field.key"
                            [formControlName]="field.key"
                            [config]="field.config"
                            [disabled]="isFieldDisabled(field)"
                            [dependencyValue]="getDependencyValue(field)"
                            [placeholder]="field.placeholder || ''"
                            [pTooltip]="getFieldTooltip(field)"
                            [class]="field.labelPosition === 'inline' ? 'flex-1' : ''">
      </generic-p-treeselect>
    </div>

    <!-- Date Picker Field -->
    <div *ngIf="field.type === 'datepicker'" [class]="field.labelPosition === 'inline' ? 'flex items-center gap-2' : ''">
      <label *ngIf="field.label && showLabel && field.labelPosition === 'inline'" [for]="field.key" class="text-sm font-bold text-gray-700 whitespace-nowrap">
        {{ field.label }}<span *ngIf="field.required" class="text-red-500">*</span>:
      </label>
      <p-datepicker [id]="field.key"
                    [formControlName]="field.key"
                    [placeholder]="field.placeholder || ''"
                    [disabled]="isFieldDisabled(field)"
                    [pTooltip]="getFieldTooltip(field)"
                    dateFormat="dd/mm/yy"
                    defaultConfig
                    [class]="field.labelPosition === 'inline' ? 'flex-1' : ''" />
    </div>

    <!-- Text Input Field -->
    <div *ngIf="field.type === 'text'" [class]="field.labelPosition === 'inline' ? 'flex items-center gap-2' : ''">
      <label *ngIf="field.label && showLabel && field.labelPosition === 'inline'" [for]="field.key" class="text-sm font-bold text-gray-700 whitespace-nowrap">
        {{ field.label }}<span *ngIf="field.required" class="text-red-500">*</span>:
      </label>
      <input pInputText
             [autoFormat]="field.autoFormat || ''"
             [id]="field.key"
             [formControlName]="field.key"
             [placeholder]="field.placeholder || ''"
             [disabled]="isFieldDisabled(field)"
             [pTooltip]="getFieldTooltip(field)"
             [class]="field.labelPosition === 'inline' ? 'flex-1' : 'w-full'">
    </div>

    <!-- Textarea Field -->
    <div *ngIf="field.type === 'textarea'" [class]="field.labelPosition === 'inline' ? 'flex items-start gap-2' : ''">
      <label *ngIf="field.label && showLabel && field.labelPosition === 'inline'" [for]="field.key" class="text-sm font-bold text-gray-700 whitespace-nowrap pt-2">
        {{ field.label }}<span *ngIf="field.required" class="text-red-500">*</span>:
      </label>
      <textarea pTextarea
                [id]="field.key"
                [formControlName]="field.key"
                [placeholder]="field.placeholder || ''"
                [disabled]="isFieldDisabled(field)"
                [pTooltip]="getFieldTooltip(field)"
                [rows]="field.rows || 3"
                [class]="field.labelPosition === 'inline' ? 'flex-1' : 'w-full'">
      </textarea>
    </div>

    <!-- Checkbox Field -->
    <div *ngIf="field.type === 'checkbox'" class="flex items-center gap-2">
      <p-checkbox [id]="field.key"
                  [formControlName]="field.key"
                  [disabled]="isFieldDisabled(field)"
                  [pTooltip]="getFieldTooltip(field)"
                  [binary]="true"
                  class="flex">
      </p-checkbox>
      <label *ngIf="field.placeholder || field.label" [for]="field.key" [class]="field.labelPosition === 'inline' ? 'text-sm text-gray-700 cursor-pointer' : 'text-sm text-gray-700 cursor-pointer'">
        {{ field.placeholder || field.label }}
      </label>
    </div>

    <!-- Toggle Switch Field -->
    <div *ngIf="field.type === 'toggleSwitch'" class="flex items-center gap-2">
      <p-toggleswitch [id]="field.key"
                      [formControlName]="field.key"
                      [disabled]="isFieldDisabled(field)"
                      [pTooltip]="getFieldTooltip(field)"
                      class="flex">
      </p-toggleswitch>
      <label *ngIf="field.placeholder || field.label" [for]="field.key" [class]="field.labelPosition === 'inline' ? 'text-sm text-gray-700 cursor-pointer' : 'text-sm text-gray-700 cursor-pointer'">
        {{ field.placeholder || field.label }}
      </label>
    </div>

    <!-- Static Display Field -->
    <div *ngIf="field.type === 'static' && initialData" class="text-gray-600">
      <span *ngIf="field.label" [class]="field.labelPosition === 'inline' ? 'font-bold' : 'font-bold'">{{ field.label }}:</span> {{ getStaticFieldValue(field) }}
    </div>

    <!-- Custom Field Template -->
    <ng-container *ngIf="field.type === 'custom'">
      <ng-container *ngIf="customFieldTemplate">
        <ng-container *ngTemplateOutlet="customFieldTemplate; context: { $implicit: form, field: field, editMode: editMode, initialData: initialData }">
        </ng-container>
      </ng-container>
      <!-- Fallback for custom fields without template -->
      <div *ngIf="!customFieldTemplate" class="text-gray-500 italic">
        Custom field template not provided{{ field.label ? ' for: ' + field.label : '' }}
      </div>
    </ng-container>

    <!-- Error Message -->
    <small *ngIf="getFieldError(field.key)" class="text-red-500">
      {{ getFieldError(field.key) }}
    </small>
  </ng-template>
  <!-- Dynamic Field Generation using Field Groups -->
  <ng-container *ngFor="let fieldGroup of fieldGroups; let groupIndex = index">

    <!-- Single field group (full-width or single half-width) -->
    <div *ngIf="fieldGroup.length === 1" class="flex flex-col mb-4 gap-2">
      <ng-container *ngTemplateOutlet="fieldTemplate; context: { field: fieldGroup[0], showLabel: !(fieldGroup[0].label == null) }"></ng-container>
      <!-- Optional Divider -->
      <p-divider *ngIf="fieldGroup[0].showDividerAfter"></p-divider>
    </div>

    <!-- Two field group (two half-width fields) -->
    <div *ngIf="fieldGroup.length === 2" class="grid grid-cols-2 gap-6 mb-4">
      <!-- First half-width field -->
      <div class="flex flex-col gap-2">
        <ng-container *ngTemplateOutlet="fieldTemplate; context: { field: fieldGroup[0], showLabel: !(fieldGroup[0].label == null) }"></ng-container>
      </div>

      <!-- Second half-width field -->
      <div class="flex flex-col gap-2">
        <ng-container *ngTemplateOutlet="fieldTemplate; context: { field: fieldGroup[1], showLabel: !(fieldGroup[1].label == null) }"></ng-container>
      </div>

      <!-- Optional Divider after the group -->
      <div *ngIf="fieldGroup[0].showDividerAfter || fieldGroup[1].showDividerAfter" class="col-span-2">
        <p-divider></p-divider>
      </div>
    </div>

  </ng-container>
</form>