import { Routes } from '@angular/router';
import { PERMISSIONS } from '@shared/constants';
import { PermissionGuard } from '../core/guards/permission.guard';
import { AppLayout } from '../layout/app.layout';

export default [
    {
        path: '',
        component: AppLayout,
        children: [
            { path: '', redirectTo: 'settings', pathMatch: 'full' },
            {
                path: 'sync-errors',
                loadChildren: () => import('./sync-errors/sync-errors.module').then((m) => m.SyncErrorsModule),
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.SYNC_VIEW.code] }
            },
            {
                path: 'settings',
                loadChildren: () => import('./settings/settings.routes'),
                canActivate: [PermissionGuard],
                data: { permissions: [PERMISSIONS.ROLE_VIEW.code] }
            },
            {
                path: 'playground',
                loadComponent: () => import('./playground/playground.component').then(m => m.PlaygroundComponent)
            }
        ]
    }
] as Routes;
