import { MONGO_CONST } from '@database/mongodb';
import { Inject, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';
import { GenericRepository } from '.';
import { ProfileDocument } from '../schemas';

@Injectable()
export class ProfileRepository extends GenericRepository<ProfileDocument> {
    private readonly context = ProfileRepository.name;

    constructor(
        @Inject(MONGO_CONST.PROFILE_COLLECTION)
        private readonly profileModel: Model<ProfileDocument>,
    ) {
        super(profileModel);
    }

    async textSearchForLearner(text: string) {
        return await this.profileModel.aggregate([
            {
                $match: {
                    $or: [{ $text: { $search: text } }, { textSearch: { $regex: '^' + text } }],
                },
            },
            { $lookup: { from: MONGO_CONST.LEARNER_COLLECTION, localField: 'id', foreignField: 'id', as: 'learner' } },
            { $unwind: '$learner' },
            {
                $project: {
                    _id: 0,
                    id: '$learner.id',
                    code: '$learner.code',
                    ec: '$learner.ec',
                    fullname: 1,
                    email: 1,
                },
            },
            { $limit: 10 },
        ]);
    }
}
